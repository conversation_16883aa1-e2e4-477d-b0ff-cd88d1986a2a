#!/bin/bash

# Script to generate flamegraph from perf.data using Docker
# This script runs Linux perf tools in a container to process the perf.data file

set -e

PERF_DATA_FILE="$1"
OUTPUT_DIR="$(pwd)/flamegraph_output"

if [ -z "$PERF_DATA_FILE" ]; then
    echo "Usage: $0 <path_to_perf.data>"
    echo "Example: $0 '/Users/<USER>/Downloads/Blinkit Data Staging Perf.data'"
    exit 1
fi

if [ ! -f "$PERF_DATA_FILE" ]; then
    echo "Error: File '$PERF_DATA_FILE' not found"
    exit 1
fi

echo "Processing perf.data file: $PERF_DATA_FILE"
echo "Output directory: $OUTPUT_DIR"

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Ensure perf.data is present in the output directory (avoid copying the same file)
DEST_FILE="$OUTPUT_DIR/perf.data"
if [ "$PERF_DATA_FILE" -ef "$DEST_FILE" ]; then
    echo "perf.data is already in the output directory, skipping copy"
else
    cp "$PERF_DATA_FILE" "$DEST_FILE"
fi

echo "Starting Docker container with Linux perf tools..."

# Run Docker container with Ubuntu and install necessary tools (non-interactive)
docker run --rm \
    -v "$OUTPUT_DIR:/data" \
    -w /data \
    ubuntu:22.04 \
    bash -c "
        set -e
        echo 'Installing required packages...'
        apt-get update -qq
        apt-get install -y linux-tools-generic git perl wget curl
        
        echo 'Preparing FlameGraph tools...'
        if [ -d FlameGraph ]; then
            echo 'FlameGraph directory already exists, using existing copy'
        else
            git clone https://github.com/brendangregg/FlameGraph.git
        fi
        
        echo 'Processing perf.data file...'
        
        # Try to extract stack traces from perf.data
        echo 'Extracting stack traces...'
        perf script -i perf.data > stacks.txt 2>/dev/null || {
            echo 'Warning: perf script failed, trying alternative approach...'
            perf script -i perf.data --no-demangle > stacks.txt 2>/dev/null || {
                echo 'Error: Could not extract stack traces from perf.data'
                echo 'This might be due to:'
                echo '1. The perf.data file was created on a different architecture'
                echo '2. Missing symbol information'
                echo '3. Incompatible perf version'
                exit 1
            }
        }
        
        if [ ! -s stacks.txt ]; then
            echo 'Error: No stack traces extracted (empty file)'
            exit 1
        fi
        
        echo 'Collapsing stack traces...'
        ./FlameGraph/stackcollapse-perf.pl stacks.txt > folded.txt
        
        if [ ! -s folded.txt ]; then
            echo 'Error: No folded stack traces generated'
            exit 1
        fi
        
        echo 'Generating flamegraph...'
        ./FlameGraph/flamegraph.pl folded.txt > flamegraph.svg
        
        echo 'Generating additional visualizations...'
        
        # Generate a reverse flamegraph (icicle graph)
        ./FlameGraph/flamegraph.pl --reverse folded.txt > icicle.svg
        
        # Generate a differential flamegraph if we have enough data
        ./FlameGraph/flamegraph.pl --colors=hot folded.txt > flamegraph_hot.svg
        
        echo 'Analysis complete!'
        echo 'Files generated:'
        ls -la *.txt *.svg
        
        # Show some basic statistics
        echo ''
        echo 'Stack trace statistics:'
        echo \"Total lines in stacks.txt: \$(wc -l < stacks.txt)\"
        echo \"Total folded stacks: \$(wc -l < folded.txt)\"
        echo \"Flamegraph file size: \$(ls -lh flamegraph.svg | awk '{print \$5}')\"
    "

echo ""
echo "Flamegraph generation complete!"
echo "Output files are in: $OUTPUT_DIR"
echo ""
echo "Generated files:"
ls -la "$OUTPUT_DIR"/*.svg 2>/dev/null || echo "No SVG files found - check for errors above"

echo ""
echo "To view the flamegraph:"
echo "1. Open flamegraph.svg in a web browser"
echo "2. Or use: open '$OUTPUT_DIR/flamegraph.svg'"
echo ""
echo "Additional files:"
echo "- icicle.svg: Reverse flamegraph (icicle chart)"
echo "- flamegraph_hot.svg: Flamegraph with hot color scheme"
echo "- stacks.txt: Raw stack traces"
echo "- folded.txt: Collapsed stack traces"
