import subprocess
import time
import tempfile
import json
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
from functools import partial
import logging
import boto3
from botocore.client import BaseClient
from botocore.config import Config

def setup_logging():
    """Configure logging for the benchmark."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('s3_benchmark')

logger = setup_logging()

class S3SyncBenchmark:
    def __init__(
        self,
        source_bucket: str,
        source_prefix: str,
        destination_bucket: str,
        destination_prefix: str,
        region_name: str = "ap-south-1",
        max_workers: int = 10,
    ):
        self.source_bucket = source_bucket
        self.source_prefix = source_prefix.rstrip('/')
        self.destination_bucket = destination_bucket
        self.destination_prefix = destination_prefix.rstrip('/')
        self.max_workers = max_workers
        
        # Initialize S3 client using environment variables for credentials
        config = Config(
            max_pool_connections=50,  # Increase from default 10
            retries={'max_attempts': 10}
        )
        self.s3_client = boto3.client('s3', region_name=region_name, config=config)
        self.test_files = []
        
        logger.info(f"Initialized S3 client with region: {region_name}")
        logger.info("Using AWS credentials from environment variables")
        
    def _get_test_files(self) -> List[str]:
        """Get all files from the source prefix for benchmarking."""
        s3_uri = f"s3://{self.source_bucket}/{self.source_prefix}"
        logger.info(f"Searching for files in {s3_uri}")
        
        paginator = self.s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(
            Bucket=self.source_bucket,
            Prefix=self.source_prefix,
            Delimiter=''
        )
        
        files = []
        try:
            for page_num, page in enumerate(pages, 1):
                if 'Contents' in page:
                    for obj in page['Contents']:
                        key = obj['Key']
                        # Skip directories and empty objects
                        if not key.endswith('/') and obj.get('Size', 0) > 0:
                            files.append(key)
                            if len(files) % 1000 == 0:
                                logger.info(f"Found {len(files)} files so far...")
                
                logger.debug(f"Processed page {page_num}, found {len(files)} files so far")
        except Exception as e:
            logger.error(f"Error listing files in {s3_uri}: {str(e)}")
            raise
        
        if not files:
            logger.warning(f"No files found in {s3_uri}")
            # Try to list the bucket contents at a higher level for debugging
            try:
                parent_prefix = '/'.join(self.source_prefix.split('/')[:-2])
                if parent_prefix:
                    logger.info(f"Checking parent directory s3://{self.source_bucket}/{parent_prefix}")
                    response = self.s3_client.list_objects_v2(
                        Bucket=self.source_bucket,
                        Prefix=parent_prefix,
                        Delimiter='',
                        MaxKeys=10
                    )
                    if 'Contents' in response:
                        logger.info("Found objects at parent level:")
                        for obj in response['Contents']:
                            logger.info(f"- {obj['Key']} ({obj.get('Size', 0)} bytes)")
            except Exception as e:
                logger.debug(f"Error listing parent directory: {str(e)}")
        else:
            logger.info(f"Found {len(files)} files in {s3_uri}")
        
        return files
    
    def _cleanup_destination(self):
        """Clean up any previously synced files in destination."""
        logger.info("Cleaning up destination prefix...")
        try:
            # List all objects in the destination prefix
            paginator = self.s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(
                Bucket=self.destination_bucket,
                Prefix=self.destination_prefix
            )
            
            # Collect all keys to delete
            keys_to_delete = []
            for page in pages:
                if 'Contents' in page:
                    for obj in page['Contents']:
                        keys_to_delete.append({'Key': obj['Key']})
            
            # Delete in batches of 1000 (S3 limit)
            for i in range(0, len(keys_to_delete), 1000):
                batch = keys_to_delete[i:i + 1000]
                self.s3_client.delete_objects(
                    Bucket=self.destination_bucket,
                    Delete={'Objects': batch}
                )
                logger.info(f"Deleted {len(batch)} objects")
                
        except Exception as e:
            if hasattr(e, 'response') and e.response.get('Error', {}).get('Code') == 'NoSuchKey':
                logger.info("No objects to clean up")
            else:
                logger.warning(f"Cleanup failed: {str(e)}")
    
    def _get_destination_key(self, source_key: str, method_type: str) -> str:
        """Generate destination key with method type subdirectory."""
        # Remove the source prefix and add method type subdirectory
        rel_path = source_key[len(self.source_prefix):].lstrip('/')
        return f"{self.destination_prefix}/{method_type}/{rel_path}"

    def benchmark_s3hook_sequential(self) -> Dict:
        """Benchmark S3Hook sequential copy."""
        method_type = "s3hook_sequential"
        logger.info(f"Starting {method_type} benchmark...")
        start_time = time.time()
        
        def copy_file(key: str) -> Tuple[bool, str]:
            try:
                dest_key = self._get_destination_key(key, method_type)
                self.s3_client.copy_object(
                    CopySource={'Bucket': self.source_bucket, 'Key': key},
                    Bucket=self.destination_bucket,
                    Key=dest_key
                )
                return True, ""
            except Exception as e:
                return False, str(e)
        
        results = []
        for key in self.test_files:
            success, error = copy_file(key)
            results.append((key, success, error))
        
        duration = time.time() - start_time
        return self._process_results("S3Hook Sequential", results, duration)
    
    def benchmark_s3hook_parallel(self) -> Dict:
        """Benchmark S3Hook parallel copy with ThreadPool."""
        method_type = "s3hook_parallel"
        logger.info(f"Starting {method_type} benchmark...")
        start_time = time.time()
        
        # Create a counter using itertools.count to track file numbers
        from itertools import count
        counter = count(1)
        
        # Thread-local storage for worker ID
        import threading
        thread_local = threading.local()
        
        def get_worker_id():
            if not hasattr(thread_local, 'worker_id'):
                thread_local.worker_id = threading.get_ident()
            return thread_local.worker_id
        
        def copy_file(key: str) -> Tuple[str, bool, str]:
            try:
                file_num = next(counter)
                worker_id = get_worker_id()
                dest_key = self._get_destination_key(key, method_type)
                print(f"Syncing: File #{file_num} (Worker {worker_id}): {key} -> {dest_key}")
                self.s3_client.copy_object(
                    CopySource={'Bucket': self.source_bucket, 'Key': key},
                    Bucket=self.destination_bucket,
                    Key=dest_key
                )
                return key, True, ""
            except Exception as e:
                return key, False, str(e)
        
        results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_key = {executor.submit(copy_file, key): key for key in self.test_files}
            for future in future_to_key:
                results.append(future.result())
        
        duration = time.time() - start_time
        return self._process_results("S3Hook Parallel", results, duration)
    
    def benchmark_aws_cli(self) -> Dict:
        """Benchmark AWS CLI sync command."""
        method_type = "aws_cli"
        logger.info(f"Starting {method_type} benchmark...")
        start_time = time.time()
        
        # Ensure source doesn't end with '/' for proper prefix handling
        source_prefix = self.source_prefix.rstrip('/')
        dest_prefix = f"{self.destination_prefix.rstrip('/')}/{method_type}"
        
        # Build the sync command with method type subdirectory
        cmd = [
            "aws", "s3", "cp",
            f"s3://{self.source_bucket}/{source_prefix}/",
            f"s3://{self.destination_bucket}/{dest_prefix}/",
            "--recursive",
            "--exclude", "_SUCCESS",
        ]
        
        # Execute the command with a timeout
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True,
                timeout=600  # 10 minute timeout
            )
            success = result.returncode == 0
            error = result.stderr if not success else ""
            if result.stderr:
                logger.debug(f"AWS CLI stderr: {result.stderr}")
            if result.stdout:
                logger.debug(f"AWS CLI stdout: {result.stdout}")
                
        except subprocess.CalledProcessError as e:
            success = False
            error = (
                f"Command failed with code {e.returncode}. "
                f"Stderr: {e.stderr or 'None'}. "
                f"Stdout: {e.stdout or 'None'}"
            )
            logger.error(f"AWS CLI command failed: {error}")
            
        except subprocess.TimeoutExpired:
            success = False
            error = "AWS CLI command timed out after 10 minutes"
            logger.error(error)
        
        # # Create a temporary file for the file list
        # with tempfile.NamedTemporaryFile(mode='w+') as f:
        #     # Write the list of files to copy
        #     for key in self.test_files:
        #         f.write(f"{key}\n")
        #     f.flush()
            
        #     # Ensure source doesn't end with '/' for proper prefix handling
        #     source_prefix = self.source_prefix.rstrip('/')
        #     dest_prefix = f"{self.destination_prefix.rstrip('/')}/{method_type}"
            
        #     # Build the sync command with method type subdirectory
        #     cmd = [
        #         "aws", "s3", "cp",
        #         f"s3://{self.source_bucket}/{source_prefix}/",
        #         f"s3://{self.destination_bucket}/{dest_prefix}/",
        #         "--recursive",
        #         "--quiet",
        #         "--exclude", "*",
        #         "--include-from", f.name,
        #         "--region", self.s3_client.meta.region_name,
        #         "--no-progress"
        #     ]
            
        #     # Log the command (without sensitive info)
        #     safe_cmd = ' '.join(cmd[:6] + ['...', cmd[-1]])
        #     logger.debug(f"Executing AWS CLI command: {safe_cmd}")
            
        #     # Execute the command with a timeout
        #     try:
        #         result = subprocess.run(
        #             cmd,
        #             capture_output=True,
        #             text=True,
        #             check=True,
        #             timeout=600  # 10 minute timeout
        #         )
        #         success = result.returncode == 0
        #         error = result.stderr if not success else ""
        #         if result.stderr:
        #             logger.debug(f"AWS CLI stderr: {result.stderr}")
        #         if result.stdout:
        #             logger.debug(f"AWS CLI stdout: {result.stdout}")
                    
        #     except subprocess.CalledProcessError as e:
        #         success = False
        #         error = (
        #             f"Command failed with code {e.returncode}. "
        #             f"Stderr: {e.stderr or 'None'}. "
        #             f"Stdout: {e.stdout or 'None'}"
        #         )
        #         logger.error(f"AWS CLI command failed: {error}")
                
        #     except subprocess.TimeoutExpired:
        #         success = False
        #         error = "AWS CLI command timed out after 10 minutes"
        #         logger.error(error)
        
        duration = time.time() - start_time
        
        # For CLI, we'll consider it all or nothing
        results = [(key, success, error) for key in self.test_files]
        return self._process_results("AWS CLI", results, duration)
    
    def _process_results(self, method: str, results: List[tuple], duration: float) -> Dict:
        """Process and log benchmark results."""
        total = len(results)
        success = sum(1 for _, s, _ in results if s)
        failed = total - success
        rate = total / duration if duration > 0 else 0
        
        # Get sample errors
        errors = [e for _, s, e in results if not s and e]
        sample_error = errors[0] if errors else None
        
        result = {
            'method': method,
            'total_files': total,
            'successful': success,
            'failed': failed,
            'duration_seconds': round(duration, 2),
            'files_per_second': round(rate, 2),
            'sample_error': sample_error
        }
        
        logger.info(f"\n=== {method} Results ===")
        logger.info(f"Duration: {result['duration_seconds']}s")
        logger.info(f"Files: {result['successful']}/{result['total_files']} successful")
        logger.info(f"Rate: {result['files_per_second']} files/second")
        if failed > 0:
            logger.warning(f"Failed files: {failed}")
            if sample_error:
                logger.warning(f"Sample error: {sample_error}")
        
        return result
    
    def run_benchmark(self) -> List[Dict]:
        """Run all benchmarks and return results."""
        # Get test files
        self.test_files = self._get_test_files()
        logger.info(f"Found {len(self.test_files)} files matching the criteria")
        # logger.info(f"Test files: {self.test_files}")
        if not self.test_files:
            raise ValueError("No files found matching the criteria")
        
        results = []
        
        # Run benchmarks
        try:
            # S3Hook Sequential
            # self._cleanup_destination()
            # results.append(self.benchmark_s3hook_sequential())
            
            # S3Hook Parallel
            # self._cleanup_destination()
            results.append(self.benchmark_s3hook_parallel())
            
            # AWS CLI
            # self._cleanup_destination()
            # results.append(self.benchmark_aws_cli())
            
        finally:
            # Clean up after all tests
            # self._cleanup_destination()
            logger.info("Benchmark completed.")
        
        return results

def compare_benchmark_results(results: List[Dict]) -> str:
    """Generate a comparison report from benchmark results."""
    if not results:
        return "No benchmark results to compare"
    
    # Find the fastest method
    fastest = min(results, key=lambda x: x['duration_seconds'])
    
    # Create comparison table
    headers = ["Method", "Duration (s)", "Files/s", "Success", "Failed"]
    rows = []
    
    for r in results:
        rows.append([
            r['method'],
            f"{r['duration_seconds']:.2f}",
            f"{r['files_per_second']:.2f}",
            r['successful'],
            r['failed']
        ])
    
    # Format the table
    col_widths = [max(len(str(x)) for x in col) for col in zip(headers, *rows)]
    header = " | ".join(h.ljust(w) for h, w in zip(headers, col_widths))
    separator = "-" * len(header)
    rows_fmt = []
    
    for row in rows:
        rows_fmt.append(" | ".join(str(x).ljust(w) for x, w in zip(row, col_widths)))
    
    # Build the report
    report = [
        "\n=== S3 Sync Benchmark Results ===",
        f"Fastest Method: {fastest['method']} ({fastest['duration_seconds']:.2f}s)",
        "",
        header,
        separator
    ]
    report.extend(rows_fmt)
    
    return "\n".join(report)

if __name__ == "__main__":
    # Configuration
    CONFIG = {
        "source_bucket": "grofers-test-dse-singapore",
        "source_prefix": "neelesh/jumbo_consumer_hourly_load_test/jumbo_consumer_events/ImageShownV4Impressions/",
        "destination_bucket": "grofers-test-dse-singapore",
        "destination_prefix": "neelesh/jumbo_consumer_hourly_load_test/jumbo_consumer_image_shown_data/",
        "region_name": "ap-southeast-1",
        "max_workers": 20
    }
    
    try:
        # Run benchmarks
        benchmark = S3SyncBenchmark(**CONFIG)
        results = benchmark.run_benchmark()
        
        # Print comparison
        print(compare_benchmark_results(results))
        
    except Exception as e:
        logger.error(f"Benchmark failed: {str(e)}", exc_info=True)
        raise