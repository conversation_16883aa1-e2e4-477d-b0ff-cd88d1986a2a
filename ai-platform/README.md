# AI Platform – High-Level Design (HLD)

## Overview

The **AI Platform** is a centralized service that provides:

* **LLM-as-a-Service** – unified, quota-managed access to multiple LLM providers.
* **MCP Tool Orchestration** – ability to register service APIs (via YAML) as tools for LLMs to call dynamically.
* **Multi-Tenant Support** – per-tenant quotas, authentication, and RBAC.
* **Streaming Responses** – near real-time results back to end-user frontends.

This platform serves as a plug-and-play AI backend for any service (e.g., Superset, CRM, Payments), abstracting away token management, cost tracking, orchestration, and scaling.

---

## Goals

* Provide **single entrypoint** for all AI requests.
* Support both **LLM-only** and **LLM+Tools (MCP)** use cases.
* Enforce **quotas, rate limits, and security** centrally.
* Make it **easy to integrate** via SDKs (backend + frontend).
* Be **scalable & fault-tolerant** (multi-tenant, HA).

---

## Architecture

### Components

* **External Service Backend** – Authenticates user, calls AI SDK.
* **AI SDK** – Thin client library to send requests & handle streaming responses.
* **API Gateway** – Validates auth token, applies rate limits, routes to control plane.
* **Control Plane** – Manages sessions, quotas, registry of tools, dispatches work.
* **Postgres** – Persistent store (tenants, users, service registrations, policies).
* **Redis** – Hot counters (quota, rate limit), short-lived caches.
* **Kafka** – Event bus for async logs, usage events, billing.
* **Execution Workers** – Orchestrate prompts, tool calls, LLM calls.
* **LLM Gateway** – Unified access to LLM providers (OpenAI, Anthropic, local).
* **MCP Servers** – One per service; hosts service-specific tools registered via YAML.

### Architecture Diagram

```mermaid
flowchart TD

    subgraph ExternalService["External Service"]
        FE["Frontend (Chat UI)"]
        BE["Backend"]
        FE --> BE
    end

    BE --> SDK["AI SDK"]

    SDK --> APIGW["API Gateway"]

    APIGW --> CP["Control Plane"]

    CP -->|stores metadata| PG["Postgres"]
    CP -->|stores counters/cache| Redis["Redis"]
    CP -->|event bus| Kafka["Kafka"]

    CP --> Workers["Execution Workers"]

    Workers --> LLMGW["LLM Gateway"]
    LLMGW --> LLM["LLM Providers"]

    Workers --> MCP["MCP Servers (1 per service)"]

    MCP --> Tools["Registered Tools (from YAML)"]
```

---

## Request Flow

### User Query Sequence

```mermaid
sequenceDiagram
    participant User as User (Frontend)
    participant BE as External Backend
    participant SDK as AI SDK
    participant APIGW as API Gateway
    participant CP as Control Plane
    participant Redis as Redis
    participant PG as Postgres
    participant Workers as Execution Workers
    participant LLMGW as LLM Gateway
    participant LLM as LLM Providers
    participant MCP as MCP Server (Service-Specific)
    participant Tools as Service Tools (YAML APIs)

    User ->> BE: Sends query via chat UI
    BE ->> SDK: Forward user query
    SDK ->> APIGW: Request with auth token + metadata
    APIGW ->> CP: Validate request, check tenant & quotas
    CP ->> Redis: Increment counters (rate limiting/usage)
    CP ->> PG: Fetch tenant config, tool registry
    CP ->> Workers: Dispatch execution request

    alt LLM-only query
        Workers ->> LLMGW: Forward query for model inference
        LLMGW ->> LLM: Call selected LLM provider
        LLM ->> LLMGW: Return response
        LLMGW ->> Workers: Stream tokens
    else Query requires tools
        Workers ->> MCP: Route to service-specific MCP server
        MCP ->> Tools: Invoke registered tool API
        Tools ->> MCP: Return tool response
        MCP ->> Workers: Send enriched result
        Workers ->> LLMGW: Combine with LLM if needed
        LLMGW ->> LLM: Call model with tool context
        LLM ->> LLMGW: Return response
        LLMGW ->> Workers: Stream tokens
    end

    Workers ->> SDK: Send streaming response
    SDK ->> BE: Deliver response
    BE ->> User: Show answer in chat UI
```

---

## Key Responsibilities

### Control Plane

* **Session Lifecycle** – create, expire, clean up sessions.
* **Tool Registry** – store service YAMLs, versioned; route to correct MCP.
* **Quota Enforcement** – pre-checks in Redis + reconciliation on completion.
* **Routing** – choose execution path (LLM-only vs MCP-enabled).

### MCP Servers

* **One per service** – isolated namespace in EKS.
* **Load tools dynamically** from ConfigMap or via Control Plane API.
* **Autoscale** via HPA/KEDA.
* **Secure** via mTLS + namespace-level NetworkPolicies.

### Execution Workers

* **Run prompts** – call LLM Gateway, orchestrate multi-step workflows.
* **Invoke tools** – via MCP servers (REST/gRPC).
* **Stream responses** – back to client via Gateway.

---

## Deployment Strategy

* **EKS** hosts MCP servers in separate namespaces (e.g., `mcp-superset`, `mcp-payments`).
* **Kubernetes Operator** watches `ServiceRegistration` CRD and dynamically:

  * Creates namespace, ConfigMap, Secrets.
  * Deploys/updates MCP runtime pods.
  * Scales pods based on traffic (HPA/KEDA).
* **Control Plane** creates/updates CRDs when YAML changes.
* **Rolling updates** for MCP deployments on new tool versions.

---

## Security

* Validate **external service tokens** (OIDC/JWT/API key).
* Extract tenant & user ID for per-tenant rate-limits & quotas.
* **mTLS** for internal comms (Gateway → Workers → MCP).
* **Vault** for secrets & LLM API keys.
* Namespace-isolation for MCP servers + network policies.

---

## Observability

* **Metrics**: Prometheus (latency, success rate, token usage).
* **Tracing**: OpenTelemetry (end-to-end request trace).
* **Logging**: Centralized via FluentBit/Loki.
* **Dashboards**: Grafana for usage, quota consumption, failures.

---

## Supported Use Cases

* **LLM-as-a-Service** – just call a model, get response, no tools.
* **MCP Tool Orchestration** – expose APIs as tools, LLM decides when to call them.
* **Data-focused Scenarios**:

  * NL → SQL for Trino/Spark
  * Data Catalog Q\&A
  * Data Governance Guardrails
* **Embeddings / RAG** – vector search over knowledge base.
* **Prompt Library** – centrally managed, versioned prompts.
