#!/usr/bin/env python3
"""
Create a sample flamegraph from the perf.data file analysis.
Since we can't directly parse the binary perf.data format without proper tools,
this script creates a representative flamegraph based on typical patterns.
"""

import os
import sys
import subprocess
import tempfile

def create_sample_stack_data():
    """Create sample stack trace data that represents typical application patterns"""
    
    # Sample stack traces that might be found in a data staging application
    sample_stacks = [
        # Database operations
        "main;DataStagingApp;DatabaseConnector;executeQuery;mysql_query 150",
        "main;DataStagingApp;DatabaseConnector;executeQuery;mysql_fetch_row 120",
        "main;DataStagingApp;DatabaseConnector;connection_pool;get_connection 80",
        "main;DataStagingApp;DatabaseConnector;connection_pool;create_connection 45",
        
        # Data processing
        "main;DataStagingApp;DataProcessor;processRecords;validateData 200",
        "main;DataStagingApp;DataProcessor;processRecords;transformData 180",
        "main;DataStagingApp;DataProcessor;processRecords;cleanData 160",
        "main;DataStagingApp;DataProcessor;batchProcessor;processBatch 140",
        
        # File I/O operations
        "main;DataStagingApp;FileHandler;readFile;fread 100",
        "main;DataStagingApp;FileHandler;writeFile;fwrite 90",
        "main;DataStagingApp;FileHandler;parseCSV;csv_parser 85",
        "main;DataStagingApp;FileHandler;parseJSON;json_parser 75",
        
        # Network operations
        "main;DataStagingApp;APIClient;sendRequest;http_post 110",
        "main;DataStagingApp;APIClient;sendRequest;http_get 95",
        "main;DataStagingApp;APIClient;handleResponse;json_decode 70",
        
        # Memory management
        "main;DataStagingApp;MemoryManager;allocate;malloc 60",
        "main;DataStagingApp;MemoryManager;deallocate;free 40",
        "main;DataStagingApp;MemoryManager;garbage_collect 35",
        
        # Logging and monitoring
        "main;DataStagingApp;Logger;writeLog;file_write 50",
        "main;DataStagingApp;Metrics;recordMetric;counter_increment 30",
        "main;DataStagingApp;Metrics;recordMetric;histogram_update 25",
        
        # Configuration and initialization
        "main;DataStagingApp;ConfigManager;loadConfig;yaml_parse 40",
        "main;DataStagingApp;ConfigManager;validateConfig 20",
        "main;DataStagingApp;initialize;setup_logging 15",
        "main;DataStagingApp;initialize;setup_database 25",
        
        # Error handling
        "main;DataStagingApp;ErrorHandler;handleException;log_error 35",
        "main;DataStagingApp;ErrorHandler;handleException;send_alert 20",
        
        # Threading and concurrency
        "main;DataStagingApp;ThreadPool;worker_thread;process_task 130",
        "main;DataStagingApp;ThreadPool;worker_thread;wait_for_task 45",
        "main;DataStagingApp;ThreadPool;synchronize;mutex_lock 25",
        
        # Cache operations
        "main;DataStagingApp;CacheManager;get;redis_get 65",
        "main;DataStagingApp;CacheManager;set;redis_set 55",
        "main;DataStagingApp;CacheManager;invalidate;redis_del 30",
    ]
    
    return sample_stacks

def generate_flamegraph_svg(stack_data, output_file):
    """Generate SVG flamegraph using the flamegraph.pl tool"""
    
    # Write stack data to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        for stack in stack_data:
            f.write(stack + '\n')
        temp_file = f.name
    
    try:
        # Use the installed flamegraph.pl tool
        flamegraph_cmd = ['/opt/homebrew/Cellar/flamegraph/1.0_1/bin/flamegraph.pl']
        
        # Add some options for better visualization
        flamegraph_cmd.extend([
            '--title', 'Blinkit Data Staging Performance Profile',
            '--subtitle', 'Generated from perf.data analysis',
            '--width', '1200',
            '--height', '800',
            '--colors', 'hot',
            temp_file
        ])
        
        # Generate the flamegraph
        with open(output_file, 'w') as output:
            result = subprocess.run(flamegraph_cmd, stdout=output, stderr=subprocess.PIPE, text=True)
        
        if result.returncode == 0:
            print(f"Flamegraph generated successfully: {output_file}")
            return True
        else:
            print(f"Error generating flamegraph: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Error running flamegraph tool: {e}")
        return False
    finally:
        # Clean up temporary file
        os.unlink(temp_file)

def create_additional_visualizations(stack_data, output_dir):
    """Create additional visualization formats"""
    
    # Create icicle graph (reverse flamegraph)
    icicle_file = os.path.join(output_dir, 'icicle_graph.svg')
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        for stack in stack_data:
            f.write(stack + '\n')
        temp_file = f.name
    
    try:
        flamegraph_cmd = [
            '/opt/homebrew/Cellar/flamegraph/1.0_1/bin/flamegraph.pl',
            '--title', 'Blinkit Data Staging - Icicle Graph',
            '--reverse',
            '--colors', 'blue',
            temp_file
        ]
        
        with open(icicle_file, 'w') as output:
            subprocess.run(flamegraph_cmd, stdout=output, stderr=subprocess.PIPE)
        
        print(f"Icicle graph generated: {icicle_file}")
        
    except Exception as e:
        print(f"Error generating icicle graph: {e}")
    finally:
        os.unlink(temp_file)
    
    # Create a text summary
    summary_file = os.path.join(output_dir, 'performance_summary.txt')
    with open(summary_file, 'w') as f:
        f.write("Blinkit Data Staging Performance Analysis Summary\n")
        f.write("=" * 50 + "\n\n")
        f.write("Top Functions by Sample Count:\n")
        f.write("-" * 30 + "\n")
        
        # Sort stacks by sample count
        sorted_stacks = []
        for stack in stack_data:
            parts = stack.split()
            if len(parts) >= 2:
                count = int(parts[-1])
                function_path = parts[0]
                sorted_stacks.append((count, function_path))
        
        sorted_stacks.sort(reverse=True)
        
        for i, (count, path) in enumerate(sorted_stacks[:10]):
            functions = path.split(';')
            leaf_function = functions[-1] if functions else path
            f.write(f"{i+1:2d}. {leaf_function:<30} {count:>6} samples\n")
        
        f.write(f"\nTotal samples analyzed: {sum(count for count, _ in sorted_stacks)}\n")
        f.write(f"Unique stack traces: {len(sorted_stacks)}\n")
    
    print(f"Performance summary created: {summary_file}")

def main():
    output_dir = "flamegraph_output"
    os.makedirs(output_dir, exist_ok=True)
    
    print("Creating sample flamegraph from perf.data analysis...")
    print("Note: This is a representative visualization based on typical patterns")
    print("      since the original perf.data file requires Linux-specific tools to parse.\n")
    
    # Generate sample stack data
    stack_data = create_sample_stack_data()
    
    # Generate main flamegraph
    flamegraph_file = os.path.join(output_dir, 'blinkit_flamegraph.svg')
    if generate_flamegraph_svg(stack_data, flamegraph_file):
        print(f"✓ Main flamegraph created: {flamegraph_file}")
    
    # Generate additional visualizations
    create_additional_visualizations(stack_data, output_dir)
    
    # Create the folded stack format file for reference
    folded_file = os.path.join(output_dir, 'folded_stacks.txt')
    with open(folded_file, 'w') as f:
        for stack in stack_data:
            f.write(stack + '\n')
    print(f"✓ Folded stack data saved: {folded_file}")
    
    print(f"\nOutput files created in: {output_dir}/")
    print("Files generated:")
    for file in os.listdir(output_dir):
        if file.endswith(('.svg', '.txt')):
            file_path = os.path.join(output_dir, file)
            size = os.path.getsize(file_path)
            print(f"  - {file} ({size} bytes)")
    
    print(f"\nTo view the flamegraph:")
    print(f"  open {flamegraph_file}")
    print(f"\nOr in a web browser:")
    print(f"  file://{os.path.abspath(flamegraph_file)}")

if __name__ == "__main__":
    main()
