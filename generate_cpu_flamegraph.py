#!/usr/bin/env python3
"""
Generate a traditional CPU flame graph with classic orange/red color scheme
and dense stack traces similar to the reference image provided.
"""

import os
import sys
import subprocess
import tempfile
import random

def create_dense_cpu_stack_data():
    """Create dense, realistic CPU stack trace data"""
    
    # Base functions that appear frequently in CPU profiles
    base_stacks = [
        # Kernel and system level
        "all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64",
        "all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault",
        "all;kernel;irq;do_IRQ;handle_irq;handle_level_irq",
        "all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll",
        
        # Application base
        "all;mysqld;main;mysqld_main;handle_connections_sockets",
        "all;blinkit_app;main;app_main;event_loop",
        "all;java;main;JavaMain;run",
        "all;python;main;Py_Main;PyRun_SimpleFileExFlags",
    ]
    
    # Detailed function chains
    detailed_functions = [
        # MySQL/Database operations
        ["mysqld", "handle_connection", "do_command", "dispatch_command", "mysql_execute_command", "mysql_select", "JOIN::exec", "evaluate_join_record", "handler::ha_rnd_next", "ha_innobase::rnd_next", "row_search_mvcc", "btr_cur_search_to_nth_level", "buf_page_get_gen"],
        ["mysqld", "handle_connection", "do_command", "dispatch_command", "mysql_execute_command", "mysql_insert", "write_record", "handler::ha_write_row", "ha_innobase::write_row", "row_insert_for_mysql", "btr_cur_optimistic_insert"],
        ["mysqld", "buf_flush_page_cleaner_coordinator", "buf_flush_page_cleaner_worker", "buf_flush_do_batch", "buf_flush_write_block_low", "fil_io", "os_aio_func", "io_submit"],
        ["mysqld", "log_writer", "log_write_up_to", "log_write_blocks", "os_file_write_func", "pwrite", "sys_pwrite64"],
        ["mysqld", "srv_master_thread", "srv_master_do_active_tasks", "lock_sys_close", "lock_rec_free_all_from_discard_page"],
        
        # Application processing
        ["blinkit_app", "data_processor", "process_batch", "validate_records", "check_data_integrity", "validate_field_format", "regex_match", "pcre_exec"],
        ["blinkit_app", "data_processor", "process_batch", "transform_data", "apply_business_rules", "calculate_derived_fields", "math_operations", "floating_point_calc"],
        ["blinkit_app", "data_processor", "process_batch", "clean_data", "remove_duplicates", "hash_record_key", "sha256_hash", "crypto_hash_update"],
        ["blinkit_app", "file_handler", "read_csv_file", "parse_csv_line", "split_fields", "string_tokenize", "memcpy", "__memcpy_avx_unaligned"],
        ["blinkit_app", "api_client", "send_http_request", "build_request_payload", "json_encode", "json_object_to_string", "string_append"],
        
        # Network stack
        ["kernel", "entry_SYSCALL_64_after_hwframe", "do_syscall_64", "__x64_sys_sendto", "__sys_sendto", "sock_sendmsg", "inet_sendmsg", "tcp_sendmsg", "tcp_write_xmit", "tcp_transmit_skb", "ip_queue_xmit", "ip_output"],
        ["kernel", "entry_SYSCALL_64_after_hwframe", "do_syscall_64", "__x64_sys_recvfrom", "__sys_recvfrom", "sock_recvmsg", "inet_recvmsg", "tcp_recvmsg", "tcp_cleanup_rbuf", "tcp_rcv_established"],
        ["kernel", "net_rx_action", "__napi_poll", "e1000_clean", "e1000_clean_rx_irq", "netif_receive_skb", "__netif_receive_skb_core", "ip_rcv", "ip_rcv_finish", "dst_input"],
        
        # Memory management
        ["kernel", "page_fault", "do_page_fault", "__do_page_fault", "handle_mm_fault", "__handle_mm_fault", "handle_pte_fault", "do_anonymous_page", "alloc_zeroed_user_highpage_movable", "__alloc_pages_nodemask"],
        ["blinkit_app", "memory_manager", "allocate_buffer", "malloc", "__libc_malloc", "_int_malloc", "sysmalloc", "mmap", "sys_mmap"],
        ["blinkit_app", "memory_manager", "free_buffer", "free", "__libc_free", "_int_free", "munmap", "sys_munmap"],
        
        # File I/O
        ["kernel", "entry_SYSCALL_64_after_hwframe", "do_syscall_64", "__x64_sys_read", "ksys_read", "vfs_read", "ext4_file_read_iter", "generic_file_read_iter", "page_cache_sync_readahead", "ondemand_readahead"],
        ["kernel", "entry_SYSCALL_64_after_hwframe", "do_syscall_64", "__x64_sys_write", "ksys_write", "vfs_write", "ext4_file_write_iter", "__generic_file_write_iter", "generic_perform_write", "ext4_da_write_begin"],
        
        # Threading
        ["blinkit_app", "thread_pool", "worker_thread", "wait_for_task", "pthread_cond_wait", "futex_wait", "sys_futex", "futex_wait_queue_me"],
        ["blinkit_app", "thread_pool", "worker_thread", "execute_task", "process_data_chunk", "validate_chunk", "field_validation", "type_check"],
        ["blinkit_app", "thread_pool", "synchronize", "acquire_lock", "pthread_mutex_lock", "futex_lock_pi", "sys_futex"],
        
        # Java/JVM operations (if applicable)
        ["java", "JavaCalls::call_helper", "JavaCalls::call_virtual", "instanceKlass::call_class_initializer", "Method::invoke", "Reflection::invoke_method"],
        ["java", "CompileBroker::compiler_thread_loop", "CompileBroker::invoke_compiler_on_method", "C2Compiler::compile_method", "Compile::Compile"],
        ["java", "VMThread::loop", "VMThread::evaluate_operation", "VM_GenCollectForAllocation::doit", "GenCollectedHeap::do_collection"],
        
        # Python operations (if applicable)
        ["python", "PyEval_EvalFrameEx", "PyObject_Call", "function_call", "PyEval_EvalCodeEx", "PyRun_StringFlags"],
        ["python", "gc_collect", "collect", "move_unreachable", "delete_garbage", "subtype_dealloc"],
    ]
    
    # Generate stack traces with realistic sample counts
    stacks = []
    
    for base in base_stacks:
        for functions in detailed_functions:
            # Create various depth combinations
            for depth in range(3, len(functions) + 1):
                stack_path = base + ";" + ";".join(functions[:depth])
                # Generate realistic sample counts with some randomness
                base_count = random.randint(50, 400)
                # Add some variance based on depth (deeper stacks typically have fewer samples)
                depth_factor = max(0.3, 1.0 - (depth * 0.1))
                sample_count = int(base_count * depth_factor * random.uniform(0.7, 1.3))
                
                if sample_count > 10:  # Only include stacks with meaningful sample counts
                    stacks.append(f"{stack_path} {sample_count}")
    
    # Add some high-frequency base operations
    high_freq_stacks = [
        "all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 850",
        "all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 720",
        "all;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 650",
        "all;blinkit_app;data_processor;process_batch;validate_records 580",
        "all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 520",
        "all;blinkit_app;data_processor;process_batch;transform_data 480",
        "all;kernel;softirq;__do_softirq;net_rx_action 420",
        "all;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 380",
        "all;blinkit_app;file_handler;read_csv_file;parse_csv_line 350",
        "all;kernel;irq;do_IRQ;handle_irq 320",
    ]
    
    stacks.extend(high_freq_stacks)
    
    return stacks

def generate_cpu_flamegraph(stack_data, output_file):
    """Generate traditional CPU flame graph with orange/red colors"""
    
    # Write stack data to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        for stack in stack_data:
            f.write(stack + '\n')
        temp_file = f.name
    
    try:
        # Use flamegraph.pl with CPU-specific options
        flamegraph_cmd = [
            '/opt/homebrew/Cellar/flamegraph/1.0_1/bin/flamegraph.pl',
            '--title', 'CPU Flame Graph',
            '--subtitle', 'Blinkit Data Staging Performance Profile',
            '--width', '1200',
            '--fonttype', 'Verdana',
            '--fontsize', '12',
            '--minwidth', '0.1',
            '--colors', 'hot',  # This gives the classic orange/red CPU colors
            '--hash',  # Consistent colors for same function names
            temp_file
        ]
        
        # Generate the flamegraph
        with open(output_file, 'w') as output:
            result = subprocess.run(flamegraph_cmd, stdout=output, stderr=subprocess.PIPE, text=True)
        
        if result.returncode == 0:
            print(f"CPU Flamegraph generated successfully: {output_file}")
            return True
        else:
            print(f"Error generating flamegraph: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"Error running flamegraph tool: {e}")
        return False
    finally:
        # Clean up temporary file
        os.unlink(temp_file)

def main():
    output_dir = "flamegraph_output"
    os.makedirs(output_dir, exist_ok=True)
    
    print("Generating traditional CPU flame graph...")
    print("Creating dense stack traces with classic orange/red color scheme\n")
    
    # Generate dense CPU stack data
    stack_data = create_dense_cpu_stack_data()
    
    print(f"Generated {len(stack_data)} stack traces")
    
    # Generate CPU flamegraph
    cpu_flamegraph_file = os.path.join(output_dir, 'cpu_flamegraph_traditional.svg')
    if generate_cpu_flamegraph(stack_data, cpu_flamegraph_file):
        print(f"✓ Traditional CPU flamegraph created: {cpu_flamegraph_file}")
    
    # Save the folded stack data
    folded_file = os.path.join(output_dir, 'cpu_folded_stacks.txt')
    with open(folded_file, 'w') as f:
        for stack in stack_data:
            f.write(stack + '\n')
    print(f"✓ CPU stack data saved: {folded_file}")
    
    # Calculate some statistics
    total_samples = sum(int(stack.split()[-1]) for stack in stack_data)
    print(f"\nStatistics:")
    print(f"  Total stack traces: {len(stack_data)}")
    print(f"  Total samples: {total_samples:,}")
    print(f"  Average samples per stack: {total_samples // len(stack_data)}")
    
    print(f"\nTo view the CPU flamegraph:")
    print(f"  open {cpu_flamegraph_file}")
    print(f"\nOr in a web browser:")
    print(f"  file://{os.path.abspath(cpu_flamegraph_file)}")

if __name__ == "__main__":
    main()
