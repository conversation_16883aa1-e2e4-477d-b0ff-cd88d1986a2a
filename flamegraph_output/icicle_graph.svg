<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="7294" onload="init(evt)" viewBox="0 0 1200 7294" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#f8f8f8" offset="5%" />
		<stop stop-color="#e8e8e8" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details, searchbtn, matchedtxt, svg;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
	}

	// mouse-over for info
	function s(node) {		// show
		info = g_to_text(node);
		details.nodeValue = "Function: " + info;
	}
	function c() {			// clear
		details.nodeValue = ' ';
	}

	// ctrl-F for search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
	})

	// functions
	function find_child(parent, name, attr) {
		var children = parent.childNodes;
		for (var i=0; i<children.length;i++) {
			if (children[i].tagName == name)
				return (attr != undefined) ? children[i].attributes[attr].value : children[i];
		}
		return;
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_"+attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_"+attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_"+attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes["width"].value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes["x"].value = parseFloat(r.attributes["x"].value) +3;

		// Smaller than this size won't fit anything
		if (w < 2*12*0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		// Fit in full text width
		if (/^ *$/.test(txt) || t.getSubStringLength(0, txt.length) < w)
			return;

		for (var x=txt.length-2; x>0; x--) {
			if (t.getSubStringLength(0, x+2) <= w) {
				t.textContent = txt.substring(0,x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = (parseFloat(e.attributes["x"].value) - x - 10) * ratio + 10;
				if(e.tagName == "text") e.attributes["x"].value = find_child(e.parentNode, "rect", "x") + 3;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseFloat(e.attributes["width"].value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_child(c[i], x-10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = 10;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseInt(svg.width.baseVal.value) - (10*2);
			}
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr["width"].value);
		var xmin = parseFloat(attr["x"].value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr["y"].value);
		var ratio = (svg.width.baseVal.value - 2*10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "1.0";

		var el = document.getElementsByTagName("g");
		for(var i=0;i<el.length;i++){
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a["x"].value);
			var ew = parseFloat(a["width"].value);
			// Is it an ancestor
			if (1 == 0) {
				var upstack = parseFloat(a["y"].value) > ymin;
			} else {
				var upstack = parseFloat(a["y"].value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.style["opacity"] = "0.5";
					zoom_parent(e);
					e.onclick = function(e){unzoom(); zoom(this);};
					update_text(e);
				}
				// not in current path
				else
					e.style["display"] = "none";
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.style["display"] = "none";
				}
				else {
					zoom_child(e, xmin, ratio);
					e.onclick = function(e){zoom(this);};
					update_text(e);
				}
			}
		}
	}
	function unzoom() {
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "0.0";

		var el = document.getElementsByTagName("g");
		for(i=0;i<el.length;i++) {
			el[i].style["display"] = "block";
			el[i].style["opacity"] = "1";
			zoom_reset(el[i]);
			update_text(el[i]);
		}
	}

	// search
	function reset_search() {
		var el = document.getElementsByTagName("rect");
		for (var i=0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)", "");
			if (term != null) {
				search(term)
			}
		} else {
			reset_search();
			searching = 0;
			searchbtn.style["opacity"] = "0.1";
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.style["opacity"] = "0.0";
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		var re = new RegExp(term);
		var el = document.getElementsByTagName("g");
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			if (e.attributes["class"].value != "func_g")
				continue;
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (rect == null) {
				// the rect might be wrapped in an anchor
				// if nameattr href is being used
				if (rect = find_child(e, "a")) {
				    rect = find_child(r, "rect");
				}
			}
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes["width"].value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes["x"].value);
				orig_save(rect, "fill");
				rect.attributes["fill"].value =
				    "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;

		searchbtn.style["opacity"] = "1.0";
		searchbtn.firstChild.nodeValue = "Reset Search"

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.style["opacity"] = "1.0";
		pct = 100 * count / maxwidth;
		if (pct == 100)
			pct = "100"
		else
			pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
	function searchover(e) {
		searchbtn.style["opacity"] = "1.0";
	}
	function searchout(e) {
		if (searching) {
			searchbtn.style["opacity"] = "1.0";
		} else {
			searchbtn.style["opacity"] = "0.1";
		}
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="7294.0" fill="url(#background)"  />
<text text-anchor="middle" x="600.00" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >CPU Icicle Graph</text>
<text text-anchor="middle" x="600.00" y="48" font-size="12" font-family="Verdana" fill="rgb(160,160,160)"  >Blinkit Data Staging (Inverted)</text>
<text text-anchor="" x="10.00" y="7277" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<text text-anchor="" x="10.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="unzoom" onclick="unzoom()" style="opacity:0.0;cursor:pointer" >Reset Zoom</text>
<text text-anchor="" x="1090.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="search" onmouseover="searchover()" onmouseout="searchout()" onclick="search_prompt()" style="opacity:0.1;cursor:pointer" >Search</text>
<text text-anchor="" x="1090.00" y="7277" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="matched" > </text>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (88 samples, 1.17%)</title><rect x="674.2" y="4836" width="13.9" height="599.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="677.23" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>format_record (170 samples, 2.26%)</title><rect x="320.7" y="1836" width="26.8" height="599.0" fill="rgb(115,115,234)" rx="2" ry="2" />
<text text-anchor="" x="323.75" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >f..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>data_processor (320 samples, 4.26%)</title><rect x="1131.1" y="3036" width="50.3" height="599.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="1134.09" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >data_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_fsync (88 samples, 1.17%)</title><rect x="674.2" y="3036" width="13.9" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="677.23" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>futex_wait (180 samples, 2.40%)</title><rect x="404.0" y="636" width="28.3" height="599.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="407.01" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >f..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ha_innobase::update_row (210 samples, 2.80%)</title><rect x="584.7" y="636" width="33.0" height="599.0" fill="rgb(104,104,225)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>realloc (95 samples, 1.26%)</title><rect x="54.8" y="1836" width="14.9" height="599.0" fill="rgb(83,83,208)" rx="2" ry="2" />
<text text-anchor="" x="57.77" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>memory_manager (75 samples, 1.00%)</title><rect x="21.8" y="3036" width="11.8" height="599.0" fill="rgb(131,131,247)" rx="2" ry="2" />
<text text-anchor="" x="24.78" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_delete (185 samples, 2.46%)</title><rect x="511.6" y="2436" width="29.1" height="599.0" fill="rgb(126,126,243)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__handle_mm_fault (125 samples, 1.66%)</title><rect x="248.0" y="1836" width="19.6" height="599.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="251.01" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>pthread_mutex_unlock (45 samples, 0.60%)</title><rect x="396.9" y="1236" width="7.1" height="599.0" fill="rgb(84,84,209)" rx="2" ry="2" />
<text text-anchor="" x="399.94" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (280 samples, 3.73%)</title><rect x="540.7" y="6636" width="44.0" height="599.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="6938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__netif_receive_skb_core (110 samples, 1.46%)</title><rect x="656.9" y="1236" width="17.3" height="599.0" fill="rgb(87,87,210)" rx="2" ry="2" />
<text text-anchor="" x="659.95" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysqld (195 samples, 2.60%)</title><rect x="750.1" y="4236" width="30.6" height="599.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="753.11" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_connection (280 samples, 3.73%)</title><rect x="540.7" y="5436" width="44.0" height="599.0" fill="rgb(112,112,232)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >hand..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>pthread_mutex_lock (85 samples, 1.13%)</title><rect x="383.6" y="1236" width="13.3" height="599.0" fill="rgb(132,132,249)" rx="2" ry="2" />
<text text-anchor="" x="386.59" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_delete_single_table (185 samples, 2.46%)</title><rect x="511.6" y="1836" width="29.1" height="599.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (175 samples, 2.33%)</title><rect x="780.7" y="4836" width="27.5" height="599.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (95 samples, 1.26%)</title><rect x="688.1" y="3636" width="14.9" height="599.0" fill="rgb(87,87,210)" rx="2" ry="2" />
<text text-anchor="" x="691.06" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (95 samples, 1.26%)</title><rect x="283.0" y="4236" width="15.0" height="599.0" fill="rgb(132,132,249)" rx="2" ry="2" />
<text text-anchor="" x="286.04" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>thread_pool (150 samples, 2.00%)</title><rect x="1037.6" y="3036" width="23.6" height="599.0" fill="rgb(85,85,209)" rx="2" ry="2" />
<text text-anchor="" x="1040.61" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>page_fault (125 samples, 1.66%)</title><rect x="248.0" y="4236" width="19.6" height="599.0" fill="rgb(88,88,212)" rx="2" ry="2" />
<text text-anchor="" x="251.01" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_read (175 samples, 2.33%)</title><rect x="780.7" y="3636" width="27.5" height="599.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (185 samples, 2.46%)</title><rect x="511.6" y="6036" width="29.1" height="599.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="6338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_write (165 samples, 2.20%)</title><rect x="485.7" y="1836" width="25.9" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="488.71" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_send (125 samples, 1.66%)</title><rect x="988.1" y="636" width="19.7" height="599.0" fill="rgb(129,129,246)" rx="2" ry="2" />
<text text-anchor="" x="991.12" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__close_fd (75 samples, 1.00%)</title><rect x="10.0" y="2436" width="11.8" height="599.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>buf_flush_write_block_low (195 samples, 2.60%)</title><rect x="750.1" y="1836" width="30.6" height="599.0" fill="rgb(125,125,243)" rx="2" ry="2" />
<text text-anchor="" x="753.11" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >bu..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (140 samples, 1.86%)</title><rect x="808.2" y="3636" width="22.0" height="599.0" fill="rgb(102,102,223)" rx="2" ry="2" />
<text text-anchor="" x="811.24" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >b..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>release_lock (45 samples, 0.60%)</title><rect x="396.9" y="1836" width="7.1" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="399.94" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (190 samples, 2.53%)</title><rect x="1007.8" y="5436" width="29.8" height="599.0" fill="rgb(117,117,236)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_page_fault (125 samples, 1.66%)</title><rect x="248.0" y="3636" width="19.6" height="599.0" fill="rgb(115,115,234)" rx="2" ry="2" />
<text text-anchor="" x="251.01" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>hash_record_key (250 samples, 3.33%)</title><rect x="617.7" y="636" width="39.2" height="599.0" fill="rgb(88,88,212)" rx="2" ry="2" />
<text text-anchor="" x="620.67" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >has..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (160 samples, 2.13%)</title><rect x="460.6" y="4236" width="25.1" height="599.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_recvmsg (155 samples, 2.06%)</title><rect x="946.5" y="2436" width="24.3" height="599.0" fill="rgb(116,116,235)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>net_tx_action (98 samples, 1.30%)</title><rect x="267.6" y="4236" width="15.4" height="599.0" fill="rgb(107,107,227)" rx="2" ry="2" />
<text text-anchor="" x="270.65" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__libc_free (75 samples, 1.00%)</title><rect x="21.8" y="1236" width="11.8" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="24.78" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (180 samples, 2.40%)</title><rect x="432.3" y="4236" width="28.3" height="599.0" fill="rgb(94,94,217)" rx="2" ry="2" />
<text text-anchor="" x="435.29" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io_manager (85 samples, 1.13%)</title><rect x="347.5" y="3036" width="13.3" height="599.0" fill="rgb(122,122,240)" rx="2" ry="2" />
<text text-anchor="" x="350.46" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (195 samples, 2.60%)</title><rect x="1061.2" y="3636" width="30.6" height="599.0" fill="rgb(83,83,207)" rx="2" ry="2" />
<text text-anchor="" x="1064.18" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >bl..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_batch (250 samples, 3.33%)</title><rect x="617.7" y="2436" width="39.2" height="599.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="620.67" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >pro..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__libc_realloc (95 samples, 1.26%)</title><rect x="54.8" y="1236" width="14.9" height="599.0" fill="rgb(113,113,233)" rx="2" ry="2" />
<text text-anchor="" x="57.77" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>qdisc_restart (98 samples, 1.30%)</title><rect x="267.6" y="2436" width="15.4" height="599.0" fill="rgb(120,120,238)" rx="2" ry="2" />
<text text-anchor="" x="270.65" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (165 samples, 2.20%)</title><rect x="837.3" y="4236" width="25.9" height="599.0" fill="rgb(99,99,221)" rx="2" ry="2" />
<text text-anchor="" x="840.30" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>JOIN::exec (280 samples, 3.73%)</title><rect x="540.7" y="2436" width="44.0" height="599.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >JOIN..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (175 samples, 2.33%)</title><rect x="780.7" y="4236" width="27.5" height="599.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (285 samples, 3.79%)</title><rect x="144.3" y="3636" width="44.8" height="599.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="147.32" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >blin..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_connection (210 samples, 2.80%)</title><rect x="584.7" y="4836" width="33.0" height="599.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_cleanup_rbuf (155 samples, 2.06%)</title><rect x="946.5" y="636" width="24.3" height="599.0" fill="rgb(126,126,243)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dispatch_command (245 samples, 3.26%)</title><rect x="897.8" y="4236" width="38.5" height="599.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >dis..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>transform_chunk (150 samples, 2.00%)</title><rect x="1037.6" y="636" width="23.6" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="1040.61" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>execute_task (165 samples, 2.20%)</title><rect x="1105.2" y="1836" width="25.9" height="599.0" fill="rgb(131,131,247)" rx="2" ry="2" />
<text text-anchor="" x="1108.16" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (65 samples, 0.87%)</title><rect x="717.9" y="3036" width="10.2" height="599.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="720.91" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>arch_cpu_idle (320 samples, 4.26%)</title><rect x="94.1" y="636" width="50.2" height="599.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="97.05" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >arch_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>allocate_buffer (135 samples, 1.80%)</title><rect x="33.6" y="2436" width="21.2" height="599.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="36.57" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mmap_region (140 samples, 1.86%)</title><rect x="728.1" y="636" width="22.0" height="599.0" fill="rgb(111,111,231)" rx="2" ry="2" />
<text text-anchor="" x="731.12" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ksys_write (165 samples, 2.20%)</title><rect x="485.7" y="2436" width="25.9" height="599.0" fill="rgb(129,129,246)" rx="2" ry="2" />
<text text-anchor="" x="488.71" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_batch (320 samples, 4.26%)</title><rect x="1131.1" y="2436" width="50.3" height="599.0" fill="rgb(85,85,209)" rx="2" ry="2" />
<text text-anchor="" x="1134.09" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >proce..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>validate_field_format (320 samples, 4.26%)</title><rect x="1131.1" y="636" width="50.3" height="599.0" fill="rgb(98,98,220)" rx="2" ry="2" />
<text text-anchor="" x="1134.09" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >valid..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cache_manager (110 samples, 1.46%)</title><rect x="970.8" y="3036" width="17.3" height="599.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="973.84" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (85 samples, 1.13%)</title><rect x="383.6" y="4236" width="13.3" height="599.0" fill="rgb(126,126,243)" rx="2" ry="2" />
<text text-anchor="" x="386.59" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__writeback_single_inode (145 samples, 1.93%)</title><rect x="360.8" y="1836" width="22.8" height="599.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_basic_setup (45 samples, 0.60%)</title><rect x="830.2" y="1836" width="7.1" height="599.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="833.23" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (155 samples, 2.06%)</title><rect x="946.5" y="4236" width="24.3" height="599.0" fill="rgb(108,108,228)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>write_log_entry (95 samples, 1.26%)</title><rect x="703.0" y="2436" width="14.9" height="599.0" fill="rgb(138,138,254)" rx="2" ry="2" />
<text text-anchor="" x="705.98" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_munmap (85 samples, 1.13%)</title><rect x="1091.8" y="2436" width="13.4" height="599.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="1094.81" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>thread_pool (85 samples, 1.13%)</title><rect x="383.6" y="3036" width="13.3" height="599.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="386.59" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysqld (185 samples, 2.46%)</title><rect x="511.6" y="5436" width="29.1" height="599.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysqld (210 samples, 2.80%)</title><rect x="584.7" y="5436" width="33.0" height="599.0" fill="rgb(119,119,237)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>evaluate_join_record (280 samples, 3.73%)</title><rect x="540.7" y="1836" width="44.0" height="599.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >eval..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fil_io (195 samples, 2.60%)</title><rect x="750.1" y="1236" width="30.6" height="599.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="753.11" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >fi..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysqld (75 samples, 1.00%)</title><rect x="201.7" y="3036" width="11.7" height="599.0" fill="rgb(83,83,207)" rx="2" ry="2" />
<text text-anchor="" x="204.67" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>all (7,511 samples, 100%)</title><rect x="10.0" y="36" width="1180.0" height="599.0" fill="rgb(83,83,207)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>worker_thread (165 samples, 2.20%)</title><rect x="1105.2" y="2436" width="25.9" height="599.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="1108.16" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>calculate_percentile (80 samples, 1.07%)</title><rect x="189.1" y="636" width="12.6" height="599.0" fill="rgb(82,82,206)" rx="2" ry="2" />
<text text-anchor="" x="192.10" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__vfs_read (180 samples, 2.40%)</title><rect x="432.3" y="1236" width="28.3" height="599.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="435.29" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>metrics_collector (80 samples, 1.07%)</title><rect x="189.1" y="2436" width="12.6" height="599.0" fill="rgb(99,99,221)" rx="2" ry="2" />
<text text-anchor="" x="192.10" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_execute_command (245 samples, 3.26%)</title><rect x="897.8" y="3636" width="38.5" height="599.0" fill="rgb(88,88,212)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vm_mmap_pgoff (140 samples, 1.86%)</title><rect x="728.1" y="1836" width="22.0" height="599.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="731.12" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>e1000_clean_rx_irq (110 samples, 1.46%)</title><rect x="656.9" y="2436" width="17.3" height="599.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="659.95" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_poll (145 samples, 1.93%)</title><rect x="298.0" y="636" width="22.7" height="599.0" fill="rgb(96,96,218)" rx="2" ry="2" />
<text text-anchor="" x="300.97" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dev_hard_start_xmit (98 samples, 1.30%)</title><rect x="267.6" y="1236" width="15.4" height="599.0" fill="rgb(131,131,247)" rx="2" ry="2" />
<text text-anchor="" x="270.65" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>reallocate_buffer (95 samples, 1.26%)</title><rect x="54.8" y="2436" width="14.9" height="599.0" fill="rgb(114,114,233)" rx="2" ry="2" />
<text text-anchor="" x="57.77" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (160 samples, 2.13%)</title><rect x="460.6" y="5436" width="25.1" height="599.0" fill="rgb(89,89,213)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_wp_page (125 samples, 1.66%)</title><rect x="248.0" y="636" width="19.6" height="599.0" fill="rgb(114,114,233)" rx="2" ry="2" />
<text text-anchor="" x="251.01" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>redis_set (110 samples, 1.46%)</title><rect x="970.8" y="2436" width="17.3" height="599.0" fill="rgb(111,111,230)" rx="2" ry="2" />
<text text-anchor="" x="973.84" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_update (210 samples, 2.80%)</title><rect x="584.7" y="2436" width="33.0" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>free_buffer (75 samples, 1.00%)</title><rect x="21.8" y="2436" width="11.8" height="599.0" fill="rgb(96,96,218)" rx="2" ry="2" />
<text text-anchor="" x="24.78" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__writeback_inodes_wb (145 samples, 1.93%)</title><rect x="360.8" y="3036" width="22.8" height="599.0" fill="rgb(94,94,216)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (95 samples, 1.26%)</title><rect x="54.8" y="3636" width="14.9" height="599.0" fill="rgb(125,125,242)" rx="2" ry="2" />
<text text-anchor="" x="57.77" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__napi_poll (110 samples, 1.46%)</title><rect x="656.9" y="3636" width="17.3" height="599.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="659.95" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>row_insert_for_mysql (245 samples, 3.26%)</title><rect x="897.8" y="636" width="38.5" height="599.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >row..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>worker_thread (145 samples, 1.93%)</title><rect x="360.8" y="5436" width="22.8" height="599.0" fill="rgb(116,116,235)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>api_client (140 samples, 1.86%)</title><rect x="808.2" y="3036" width="22.0" height="599.0" fill="rgb(131,131,248)" rx="2" ry="2" />
<text text-anchor="" x="811.24" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >a..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (75 samples, 1.00%)</title><rect x="21.8" y="4236" width="11.8" height="599.0" fill="rgb(112,112,232)" rx="2" ry="2" />
<text text-anchor="" x="24.78" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_mm_fault (220 samples, 2.93%)</title><rect x="213.4" y="2436" width="34.6" height="599.0" fill="rgb(124,124,242)" rx="2" ry="2" />
<text text-anchor="" x="216.45" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>path_openat (95 samples, 1.26%)</title><rect x="688.1" y="1236" width="14.9" height="599.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="691.06" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>parse_csv_line (195 samples, 2.60%)</title><rect x="1061.2" y="1836" width="30.6" height="599.0" fill="rgb(105,105,225)" rx="2" ry="2" />
<text text-anchor="" x="1064.18" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >pa..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>parse_object (140 samples, 1.86%)</title><rect x="808.2" y="636" width="22.0" height="599.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="811.24" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ext4_file_read_iter (175 samples, 2.33%)</title><rect x="780.7" y="1836" width="27.5" height="599.0" fill="rgb(130,130,247)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_batch (285 samples, 3.79%)</title><rect x="144.3" y="2436" width="44.8" height="599.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="147.32" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >proc..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>pthread_cond_wait (180 samples, 2.40%)</title><rect x="404.0" y="1236" width="28.3" height="599.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="407.01" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_select (280 samples, 3.73%)</title><rect x="540.7" y="3036" width="44.0" height="599.0" fill="rgb(120,120,238)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysq..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (165 samples, 2.20%)</title><rect x="485.7" y="4836" width="25.9" height="599.0" fill="rgb(130,130,247)" rx="2" ry="2" />
<text text-anchor="" x="488.71" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>memory_manager (95 samples, 1.26%)</title><rect x="54.8" y="3036" width="14.9" height="599.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="57.77" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>epoll_ctl (95 samples, 1.26%)</title><rect x="283.0" y="1836" width="15.0" height="599.0" fill="rgb(108,108,228)" rx="2" ry="2" />
<text text-anchor="" x="286.04" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (65 samples, 0.87%)</title><rect x="936.3" y="3636" width="10.2" height="599.0" fill="rgb(131,131,248)" rx="2" ry="2" />
<text text-anchor="" x="939.28" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>remove_duplicates (250 samples, 3.33%)</title><rect x="617.7" y="1236" width="39.2" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="620.67" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >rem..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (180 samples, 2.40%)</title><rect x="432.3" y="3636" width="28.3" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="435.29" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dispatch_command (280 samples, 3.73%)</title><rect x="540.7" y="4236" width="44.0" height="599.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >disp..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_int_free (75 samples, 1.00%)</title><rect x="21.8" y="636" width="11.8" height="599.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="24.78" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rest_init (45 samples, 0.60%)</title><rect x="830.2" y="3636" width="7.1" height="599.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="833.23" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>localtime (95 samples, 1.26%)</title><rect x="703.0" y="636" width="14.9" height="599.0" fill="rgb(102,102,224)" rx="2" ry="2" />
<text text-anchor="" x="705.98" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>garbage_collect (65 samples, 0.87%)</title><rect x="717.9" y="1836" width="10.2" height="599.0" fill="rgb(83,83,207)" rx="2" ry="2" />
<text text-anchor="" x="720.91" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fput (75 samples, 1.00%)</title><rect x="10.0" y="1236" width="11.8" height="599.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>read_csv_file (195 samples, 2.60%)</title><rect x="1061.2" y="2436" width="30.6" height="599.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="1064.18" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >re..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (150 samples, 2.00%)</title><rect x="1037.6" y="3636" width="23.6" height="599.0" fill="rgb(138,138,253)" rx="2" ry="2" />
<text text-anchor="" x="1040.61" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >b..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (150 samples, 2.00%)</title><rect x="1037.6" y="4236" width="23.6" height="599.0" fill="rgb(130,130,247)" rx="2" ry="2" />
<text text-anchor="" x="1040.61" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kworker/u16:1 (145 samples, 1.93%)</title><rect x="360.8" y="6036" width="22.8" height="599.0" fill="rgb(96,96,218)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="6338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>log_write_up_to (165 samples, 2.20%)</title><rect x="837.3" y="2436" width="25.9" height="599.0" fill="rgb(131,131,248)" rx="2" ry="2" />
<text text-anchor="" x="840.30" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >l..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>yaml_parse_document (55 samples, 0.73%)</title><rect x="1181.4" y="1236" width="8.6" height="599.0" fill="rgb(93,93,216)" rx="2" ry="2" />
<text text-anchor="" x="1184.36" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (320 samples, 4.26%)</title><rect x="1131.1" y="4236" width="50.3" height="599.0" fill="rgb(99,99,220)" rx="2" ry="2" />
<text text-anchor="" x="1134.09" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ksys_read (180 samples, 2.40%)</title><rect x="432.3" y="2436" width="28.3" height="599.0" fill="rgb(99,99,221)" rx="2" ry="2" />
<text text-anchor="" x="435.29" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>strftime (95 samples, 1.26%)</title><rect x="703.0" y="1236" width="14.9" height="599.0" fill="rgb(82,82,207)" rx="2" ry="2" />
<text text-anchor="" x="705.98" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ksys_write (160 samples, 2.13%)</title><rect x="460.6" y="3036" width="25.1" height="599.0" fill="rgb(112,112,231)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (170 samples, 2.26%)</title><rect x="320.7" y="3636" width="26.8" height="599.0" fill="rgb(114,114,233)" rx="2" ry="2" />
<text text-anchor="" x="323.75" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >b..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>buf_flush_page_cleaner_coordinator (195 samples, 2.60%)</title><rect x="750.1" y="3636" width="30.6" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="753.11" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >bu..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_read (180 samples, 2.40%)</title><rect x="432.3" y="3036" width="28.3" height="599.0" fill="rgb(97,97,219)" rx="2" ry="2" />
<text text-anchor="" x="435.29" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ksys_mmap_pgoff (140 samples, 1.86%)</title><rect x="728.1" y="2436" width="22.0" height="599.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="731.12" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_command (210 samples, 2.80%)</title><rect x="584.7" y="4236" width="33.0" height="599.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>send_http_request (155 samples, 2.06%)</title><rect x="69.7" y="2436" width="24.4" height="599.0" fill="rgb(100,100,222)" rx="2" ry="2" />
<text text-anchor="" x="72.70" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_pte_fault (125 samples, 1.66%)</title><rect x="248.0" y="1236" width="19.6" height="599.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="251.01" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (95 samples, 1.26%)</title><rect x="54.8" y="4236" width="14.9" height="599.0" fill="rgb(130,130,247)" rx="2" ry="2" />
<text text-anchor="" x="57.77" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_sys_openat2 (95 samples, 1.26%)</title><rect x="688.1" y="2436" width="14.9" height="599.0" fill="rgb(96,96,218)" rx="2" ry="2" />
<text text-anchor="" x="691.06" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>async_read (145 samples, 1.93%)</title><rect x="298.0" y="2436" width="22.7" height="599.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="300.97" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >a..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_write_xmit (190 samples, 2.53%)</title><rect x="1007.8" y="636" width="29.8" height="599.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tc..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ext4_writepages (145 samples, 1.93%)</title><rect x="360.8" y="636" width="22.8" height="599.0" fill="rgb(119,119,238)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_data_chunk (165 samples, 2.20%)</title><rect x="1105.2" y="1236" width="25.9" height="599.0" fill="rgb(116,116,235)" rx="2" ry="2" />
<text text-anchor="" x="1108.16" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_read (175 samples, 2.33%)</title><rect x="780.7" y="2436" width="27.5" height="599.0" fill="rgb(82,82,207)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_write (160 samples, 2.13%)</title><rect x="460.6" y="3636" width="25.1" height="599.0" fill="rgb(128,128,245)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>synchronize (85 samples, 1.13%)</title><rect x="383.6" y="2436" width="13.3" height="599.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="386.59" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>yaml_parse_node (55 samples, 0.73%)</title><rect x="1181.4" y="636" width="8.6" height="599.0" fill="rgb(107,107,228)" rx="2" ry="2" />
<text text-anchor="" x="1184.36" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__qdisc_run (98 samples, 1.30%)</title><rect x="267.6" y="3036" width="15.4" height="599.0" fill="rgb(102,102,223)" rx="2" ry="2" />
<text text-anchor="" x="270.65" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>logger (95 samples, 1.26%)</title><rect x="703.0" y="3036" width="14.9" height="599.0" fill="rgb(95,95,218)" rx="2" ry="2" />
<text text-anchor="" x="705.98" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fsync (85 samples, 1.13%)</title><rect x="347.5" y="1836" width="13.3" height="599.0" fill="rgb(83,83,207)" rx="2" ry="2" />
<text text-anchor="" x="350.46" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dict_table_close (75 samples, 1.00%)</title><rect x="201.7" y="636" width="11.7" height="599.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="204.67" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_sendmsg (190 samples, 2.53%)</title><rect x="1007.8" y="1836" width="29.8" height="599.0" fill="rgb(84,84,208)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >in..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>parse_json_response (140 samples, 1.86%)</title><rect x="808.2" y="1836" width="22.0" height="599.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="811.24" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>redis_get (125 samples, 1.66%)</title><rect x="988.1" y="2436" width="19.7" height="599.0" fill="rgb(108,108,228)" rx="2" ry="2" />
<text text-anchor="" x="991.12" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (140 samples, 1.86%)</title><rect x="728.1" y="3636" width="22.0" height="599.0" fill="rgb(85,85,209)" rx="2" ry="2" />
<text text-anchor="" x="731.12" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_recv (110 samples, 1.46%)</title><rect x="970.8" y="636" width="17.3" height="599.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="973.84" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (55 samples, 0.73%)</title><rect x="1181.4" y="3036" width="8.6" height="599.0" fill="rgb(133,133,249)" rx="2" ry="2" />
<text text-anchor="" x="1184.36" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_filp_open (95 samples, 1.26%)</title><rect x="688.1" y="1836" width="14.9" height="599.0" fill="rgb(89,89,212)" rx="2" ry="2" />
<text text-anchor="" x="691.06" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>start_kernel (320 samples, 4.26%)</title><rect x="94.1" y="3636" width="50.2" height="599.0" fill="rgb(133,133,249)" rx="2" ry="2" />
<text text-anchor="" x="97.05" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >start..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (165 samples, 2.20%)</title><rect x="485.7" y="4236" width="25.9" height="599.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="488.71" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>unmap_region (85 samples, 1.13%)</title><rect x="1091.8" y="636" width="13.4" height="599.0" fill="rgb(135,135,251)" rx="2" ry="2" />
<text text-anchor="" x="1094.81" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (95 samples, 1.26%)</title><rect x="703.0" y="4236" width="14.9" height="599.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="705.98" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kernel_init (45 samples, 0.60%)</title><rect x="830.2" y="3036" width="7.1" height="599.0" fill="rgb(95,95,218)" rx="2" ry="2" />
<text text-anchor="" x="833.23" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (125 samples, 1.66%)</title><rect x="248.0" y="4836" width="19.6" height="599.0" fill="rgb(117,117,236)" rx="2" ry="2" />
<text text-anchor="" x="251.01" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (145 samples, 1.93%)</title><rect x="360.8" y="6636" width="22.8" height="599.0" fill="rgb(100,100,222)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="6938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ha_innobase::delete_row (185 samples, 2.46%)</title><rect x="511.6" y="636" width="29.1" height="599.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>trim_whitespace (195 samples, 2.60%)</title><rect x="1061.2" y="636" width="30.6" height="599.0" fill="rgb(133,133,249)" rx="2" ry="2" />
<text text-anchor="" x="1064.18" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tr..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__handle_mm_fault (220 samples, 2.93%)</title><rect x="213.4" y="1836" width="34.6" height="599.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="216.45" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_exception (65 samples, 0.87%)</title><rect x="936.3" y="1836" width="10.2" height="599.0" fill="rgb(108,108,228)" rx="2" ry="2" />
<text text-anchor="" x="939.28" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_epoll_ctl (95 samples, 1.26%)</title><rect x="283.0" y="1236" width="15.0" height="599.0" fill="rgb(98,98,220)" rx="2" ry="2" />
<text text-anchor="" x="286.04" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dispatch_command (185 samples, 2.46%)</title><rect x="511.6" y="3636" width="29.1" height="599.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >di..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ha_innobase::write_row (245 samples, 3.26%)</title><rect x="897.8" y="1236" width="38.5" height="599.0" fill="rgb(116,116,235)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpu_startup_entry (320 samples, 4.26%)</title><rect x="94.1" y="2436" width="50.2" height="599.0" fill="rgb(126,126,244)" rx="2" ry="2" />
<text text-anchor="" x="97.05" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cpu_s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handler::ha_rnd_next (280 samples, 3.73%)</title><rect x="540.7" y="1236" width="44.0" height="599.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >hand..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>data_processor (285 samples, 3.79%)</title><rect x="144.3" y="3036" width="44.8" height="599.0" fill="rgb(126,126,244)" rx="2" ry="2" />
<text text-anchor="" x="147.32" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >data..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (95 samples, 1.26%)</title><rect x="688.1" y="4236" width="14.9" height="599.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="691.06" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (110 samples, 1.46%)</title><rect x="970.8" y="4236" width="17.3" height="599.0" fill="rgb(124,124,241)" rx="2" ry="2" />
<text text-anchor="" x="973.84" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (85 samples, 1.13%)</title><rect x="383.6" y="3636" width="13.3" height="599.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="386.59" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>worker_thread (180 samples, 2.40%)</title><rect x="404.0" y="2436" width="28.3" height="599.0" fill="rgb(106,106,227)" rx="2" ry="2" />
<text text-anchor="" x="407.01" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>error_handler (65 samples, 0.87%)</title><rect x="936.3" y="2436" width="10.2" height="599.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="939.28" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_update_single_table (210 samples, 2.80%)</title><rect x="584.7" y="1836" width="33.0" height="599.0" fill="rgb(106,106,226)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>filp_close (75 samples, 1.00%)</title><rect x="10.0" y="1836" width="11.8" height="599.0" fill="rgb(122,122,240)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>socket_read (110 samples, 1.46%)</title><rect x="970.8" y="1236" width="17.3" height="599.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="973.84" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (165 samples, 2.20%)</title><rect x="1105.2" y="4236" width="25.9" height="599.0" fill="rgb(134,134,250)" rx="2" ry="2" />
<text text-anchor="" x="1108.16" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysqld (245 samples, 3.26%)</title><rect x="897.8" y="6036" width="38.5" height="599.0" fill="rgb(119,119,237)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="6338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>transform_data (285 samples, 3.79%)</title><rect x="144.3" y="1836" width="44.8" height="599.0" fill="rgb(102,102,224)" rx="2" ry="2" />
<text text-anchor="" x="147.32" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tran..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>futex_lock_pi (85 samples, 1.13%)</title><rect x="383.6" y="636" width="13.3" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="386.59" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>page_fault (220 samples, 2.93%)</title><rect x="213.4" y="4236" width="34.6" height="599.0" fill="rgb(95,95,217)" rx="2" ry="2" />
<text text-anchor="" x="216.45" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >pa..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (135 samples, 1.80%)</title><rect x="33.6" y="3636" width="21.2" height="599.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="36.57" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>json_encode (155 samples, 2.06%)</title><rect x="69.7" y="1236" width="24.4" height="599.0" fill="rgb(134,134,250)" rx="2" ry="2" />
<text text-anchor="" x="72.70" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >j..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_connection (185 samples, 2.46%)</title><rect x="511.6" y="4836" width="29.1" height="599.0" fill="rgb(105,105,226)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>memory_manager (65 samples, 0.87%)</title><rect x="717.9" y="2436" width="10.2" height="599.0" fill="rgb(84,84,208)" rx="2" ry="2" />
<text text-anchor="" x="720.91" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_epoll_wait (145 samples, 1.93%)</title><rect x="298.0" y="1236" width="22.7" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="300.97" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (220 samples, 2.93%)</title><rect x="863.2" y="4236" width="34.6" height="599.0" fill="rgb(84,84,208)" rx="2" ry="2" />
<text text-anchor="" x="866.23" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_mm_fault (125 samples, 1.66%)</title><rect x="248.0" y="2436" width="19.6" height="599.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="251.01" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handler::ha_update_row (210 samples, 2.80%)</title><rect x="584.7" y="1236" width="33.0" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>serialize_json (170 samples, 2.26%)</title><rect x="320.7" y="1236" width="26.8" height="599.0" fill="rgb(131,131,247)" rx="2" ry="2" />
<text text-anchor="" x="323.75" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >s..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>e1000_clean (110 samples, 1.46%)</title><rect x="656.9" y="3036" width="17.3" height="599.0" fill="rgb(107,107,228)" rx="2" ry="2" />
<text text-anchor="" x="659.95" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (88 samples, 1.17%)</title><rect x="674.2" y="3636" width="13.9" height="599.0" fill="rgb(96,96,218)" rx="2" ry="2" />
<text text-anchor="" x="677.23" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (160 samples, 2.13%)</title><rect x="460.6" y="4836" width="25.1" height="599.0" fill="rgb(107,107,228)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (80 samples, 1.07%)</title><rect x="189.1" y="3036" width="12.6" height="599.0" fill="rgb(99,99,220)" rx="2" ry="2" />
<text text-anchor="" x="192.10" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (155 samples, 2.06%)</title><rect x="69.7" y="4236" width="24.4" height="599.0" fill="rgb(83,83,208)" rx="2" ry="2" />
<text text-anchor="" x="72.70" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_write (160 samples, 2.13%)</title><rect x="460.6" y="2436" width="25.1" height="599.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>escape_special_chars (170 samples, 2.26%)</title><rect x="320.7" y="636" width="26.8" height="599.0" fill="rgb(125,125,243)" rx="2" ry="2" />
<text text-anchor="" x="323.75" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (110 samples, 1.46%)</title><rect x="970.8" y="3636" width="17.3" height="599.0" fill="rgb(94,94,216)" rx="2" ry="2" />
<text text-anchor="" x="973.84" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (190 samples, 2.53%)</title><rect x="1007.8" y="4236" width="29.8" height="599.0" fill="rgb(100,100,221)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_command (245 samples, 3.26%)</title><rect x="897.8" y="4836" width="38.5" height="599.0" fill="rgb(117,117,235)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>thread_pool (45 samples, 0.60%)</title><rect x="396.9" y="3036" width="7.1" height="599.0" fill="rgb(106,106,227)" rx="2" ry="2" />
<text text-anchor="" x="399.94" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>free (75 samples, 1.00%)</title><rect x="21.8" y="1836" width="11.8" height="599.0" fill="rgb(124,124,242)" rx="2" ry="2" />
<text text-anchor="" x="24.78" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_writepages (145 samples, 1.93%)</title><rect x="360.8" y="1236" width="22.8" height="599.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__do_page_fault (125 samples, 1.66%)</title><rect x="248.0" y="3036" width="19.6" height="599.0" fill="rgb(101,101,223)" rx="2" ry="2" />
<text text-anchor="" x="251.01" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>apply_business_rules (285 samples, 3.79%)</title><rect x="144.3" y="1236" width="44.8" height="599.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="147.32" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >appl..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>srv_master_do_active_tasks (75 samples, 1.00%)</title><rect x="201.7" y="1836" width="11.7" height="599.0" fill="rgb(129,129,246)" rx="2" ry="2" />
<text text-anchor="" x="204.67" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>api_client (155 samples, 2.06%)</title><rect x="69.7" y="3036" width="24.4" height="599.0" fill="rgb(132,132,249)" rx="2" ry="2" />
<text text-anchor="" x="72.70" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >a..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (155 samples, 2.06%)</title><rect x="946.5" y="4836" width="24.3" height="599.0" fill="rgb(130,130,247)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>driver_init (45 samples, 0.60%)</title><rect x="830.2" y="1236" width="7.1" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="833.23" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (125 samples, 1.66%)</title><rect x="988.1" y="4236" width="19.7" height="599.0" fill="rgb(130,130,247)" rx="2" ry="2" />
<text text-anchor="" x="991.12" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_execute_command (280 samples, 3.73%)</title><rect x="540.7" y="3636" width="44.0" height="599.0" fill="rgb(81,81,205)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysq..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_batch (220 samples, 2.93%)</title><rect x="863.2" y="2436" width="34.6" height="599.0" fill="rgb(105,105,226)" rx="2" ry="2" />
<text text-anchor="" x="866.23" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >pr..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_fsync (88 samples, 1.17%)</title><rect x="674.2" y="2436" width="13.9" height="599.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="677.23" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (85 samples, 1.13%)</title><rect x="347.5" y="3636" width="13.3" height="599.0" fill="rgb(82,82,207)" rx="2" ry="2" />
<text text-anchor="" x="350.46" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (95 samples, 1.26%)</title><rect x="283.0" y="3636" width="15.0" height="599.0" fill="rgb(118,118,236)" rx="2" ry="2" />
<text text-anchor="" x="286.04" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (95 samples, 1.26%)</title><rect x="688.1" y="4836" width="14.9" height="599.0" fill="rgb(130,130,246)" rx="2" ry="2" />
<text text-anchor="" x="691.06" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>write_output_file (170 samples, 2.26%)</title><rect x="320.7" y="2436" width="26.8" height="599.0" fill="rgb(99,99,221)" rx="2" ry="2" />
<text text-anchor="" x="323.75" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>wait_for_task (180 samples, 2.40%)</title><rect x="404.0" y="1836" width="28.3" height="599.0" fill="rgb(120,120,238)" rx="2" ry="2" />
<text text-anchor="" x="407.01" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__vm_munmap (85 samples, 1.13%)</title><rect x="1091.8" y="1836" width="13.4" height="599.0" fill="rgb(115,115,234)" rx="2" ry="2" />
<text text-anchor="" x="1094.81" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>data_processor (250 samples, 3.33%)</title><rect x="617.7" y="3036" width="39.2" height="599.0" fill="rgb(128,128,245)" rx="2" ry="2" />
<text text-anchor="" x="620.67" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >dat..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (55 samples, 0.73%)</title><rect x="1181.4" y="3636" width="8.6" height="599.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="1184.36" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>split_fields (195 samples, 2.60%)</title><rect x="1061.2" y="1236" width="30.6" height="599.0" fill="rgb(106,106,227)" rx="2" ry="2" />
<text text-anchor="" x="1064.18" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sp..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (45 samples, 0.60%)</title><rect x="830.2" y="4836" width="7.1" height="599.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="833.23" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_int_realloc (95 samples, 1.26%)</title><rect x="54.8" y="636" width="14.9" height="599.0" fill="rgb(136,136,252)" rx="2" ry="2" />
<text text-anchor="" x="57.77" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_command (280 samples, 3.73%)</title><rect x="540.7" y="4836" width="44.0" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do_c..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>buf_flush_page_cleaner_worker (195 samples, 2.60%)</title><rect x="750.1" y="3036" width="30.6" height="599.0" fill="rgb(95,95,218)" rx="2" ry="2" />
<text text-anchor="" x="753.11" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >bu..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>log_write_blocks (165 samples, 2.20%)</title><rect x="837.3" y="1836" width="25.9" height="599.0" fill="rgb(116,116,235)" rx="2" ry="2" />
<text text-anchor="" x="840.30" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >l..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>netif_receive_skb (110 samples, 1.46%)</title><rect x="656.9" y="1836" width="17.3" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="659.95" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_execute_command (185 samples, 2.46%)</title><rect x="511.6" y="3036" width="29.1" height="599.0" fill="rgb(125,125,242)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (145 samples, 1.93%)</title><rect x="298.0" y="4236" width="22.7" height="599.0" fill="rgb(106,106,227)" rx="2" ry="2" />
<text text-anchor="" x="300.97" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_openat (95 samples, 1.26%)</title><rect x="688.1" y="3036" width="14.9" height="599.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="691.06" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>page_cache_sync_readahead (175 samples, 2.33%)</title><rect x="780.7" y="636" width="27.5" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>generic_file_read_iter (175 samples, 2.33%)</title><rect x="780.7" y="1236" width="27.5" height="599.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >g..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (125 samples, 1.66%)</title><rect x="988.1" y="3636" width="19.7" height="599.0" fill="rgb(137,137,253)" rx="2" ry="2" />
<text text-anchor="" x="991.12" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ip_rcv (110 samples, 1.46%)</title><rect x="656.9" y="636" width="17.3" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="659.95" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (180 samples, 2.40%)</title><rect x="404.0" y="4236" width="28.3" height="599.0" fill="rgb(132,132,249)" rx="2" ry="2" />
<text text-anchor="" x="407.01" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>record_metric (80 samples, 1.07%)</title><rect x="189.1" y="1836" width="12.6" height="599.0" fill="rgb(123,123,240)" rx="2" ry="2" />
<text text-anchor="" x="192.10" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (165 samples, 2.20%)</title><rect x="485.7" y="3636" width="25.9" height="599.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="488.71" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ext4_sync_file (88 samples, 1.17%)</title><rect x="674.2" y="1236" width="13.9" height="599.0" fill="rgb(84,84,208)" rx="2" ry="2" />
<text text-anchor="" x="677.23" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysqld (165 samples, 2.20%)</title><rect x="837.3" y="3636" width="25.9" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="840.30" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (180 samples, 2.40%)</title><rect x="432.3" y="4836" width="28.3" height="599.0" fill="rgb(112,112,232)" rx="2" ry="2" />
<text text-anchor="" x="435.29" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>async_write (95 samples, 1.26%)</title><rect x="283.0" y="2436" width="15.0" height="599.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="286.04" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>thread_pool (165 samples, 2.20%)</title><rect x="1105.2" y="3036" width="25.9" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="1108.16" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>default_idle_call (320 samples, 4.26%)</title><rect x="94.1" y="1236" width="50.2" height="599.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="97.05" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >defau..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>epoll_wait (145 samples, 1.93%)</title><rect x="298.0" y="1836" width="22.7" height="599.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="300.97" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>clean_data (250 samples, 3.33%)</title><rect x="617.7" y="1836" width="39.2" height="599.0" fill="rgb(131,131,248)" rx="2" ry="2" />
<text text-anchor="" x="620.67" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cle..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (180 samples, 2.40%)</title><rect x="404.0" y="3636" width="28.3" height="599.0" fill="rgb(115,115,234)" rx="2" ry="2" />
<text text-anchor="" x="407.01" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >b..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handler::ha_delete_row (185 samples, 2.46%)</title><rect x="511.6" y="1236" width="29.1" height="599.0" fill="rgb(117,117,236)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>wb_do_writeback (145 samples, 1.93%)</title><rect x="360.8" y="3636" width="22.8" height="599.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_mmap (140 samples, 1.86%)</title><rect x="728.1" y="1236" width="22.0" height="599.0" fill="rgb(106,106,227)" rx="2" ry="2" />
<text text-anchor="" x="731.12" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >d..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>e1000_xmit_frame (98 samples, 1.30%)</title><rect x="267.6" y="636" width="15.4" height="599.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="270.65" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_write (165 samples, 2.20%)</title><rect x="485.7" y="3036" width="25.9" height="599.0" fill="rgb(133,133,249)" rx="2" ry="2" />
<text text-anchor="" x="488.71" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (75 samples, 1.00%)</title><rect x="21.8" y="3636" width="11.8" height="599.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="24.78" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (155 samples, 2.06%)</title><rect x="946.5" y="5436" width="24.3" height="599.0" fill="rgb(120,120,238)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>memory_manager (135 samples, 1.80%)</title><rect x="33.6" y="3036" width="21.2" height="599.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="36.57" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__sys_recvfrom (155 samples, 2.06%)</title><rect x="946.5" y="3036" width="24.3" height="599.0" fill="rgb(105,105,226)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>kernel_init_freeable (45 samples, 0.60%)</title><rect x="830.2" y="2436" width="7.1" height="599.0" fill="rgb(95,95,217)" rx="2" ry="2" />
<text text-anchor="" x="833.23" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (80 samples, 1.07%)</title><rect x="189.1" y="3636" width="12.6" height="599.0" fill="rgb(95,95,217)" rx="2" ry="2" />
<text text-anchor="" x="192.10" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>json_decode (140 samples, 1.86%)</title><rect x="808.2" y="1236" width="22.0" height="599.0" fill="rgb(96,96,219)" rx="2" ry="2" />
<text text-anchor="" x="811.24" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >j..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>rest_init (320 samples, 4.26%)</title><rect x="94.1" y="3036" width="50.2" height="599.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="97.05" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >rest_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (65 samples, 0.87%)</title><rect x="936.3" y="3036" width="10.2" height="599.0" fill="rgb(133,133,249)" rx="2" ry="2" />
<text text-anchor="" x="939.28" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (220 samples, 2.93%)</title><rect x="213.4" y="4836" width="34.6" height="599.0" fill="rgb(119,119,238)" rx="2" ry="2" />
<text text-anchor="" x="216.45" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_mmap (140 samples, 1.86%)</title><rect x="728.1" y="3036" width="22.0" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="731.12" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>platform_driver_register (45 samples, 0.60%)</title><rect x="830.2" y="636" width="7.1" height="599.0" fill="rgb(113,113,233)" rx="2" ry="2" />
<text text-anchor="" x="833.23" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (110 samples, 1.46%)</title><rect x="656.9" y="4836" width="17.3" height="599.0" fill="rgb(120,120,238)" rx="2" ry="2" />
<text text-anchor="" x="659.95" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (220 samples, 2.93%)</title><rect x="863.2" y="3636" width="34.6" height="599.0" fill="rgb(101,101,223)" rx="2" ry="2" />
<text text-anchor="" x="866.23" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >bl..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sys_fsync (85 samples, 1.13%)</title><rect x="347.5" y="1236" width="13.3" height="599.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="350.46" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__do_page_fault (220 samples, 2.93%)</title><rect x="213.4" y="3036" width="34.6" height="599.0" fill="rgb(83,83,207)" rx="2" ry="2" />
<text text-anchor="" x="216.45" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>_int_malloc (135 samples, 1.80%)</title><rect x="33.6" y="636" width="21.2" height="599.0" fill="rgb(101,101,223)" rx="2" ry="2" />
<text text-anchor="" x="36.57" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handler::ha_write_row (245 samples, 3.26%)</title><rect x="897.8" y="1836" width="38.5" height="599.0" fill="rgb(82,82,207)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >han..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_close (75 samples, 1.00%)</title><rect x="10.0" y="3036" width="11.8" height="599.0" fill="rgb(104,104,225)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>file_handler (195 samples, 2.60%)</title><rect x="1061.2" y="3036" width="30.6" height="599.0" fill="rgb(98,98,220)" rx="2" ry="2" />
<text text-anchor="" x="1064.18" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >fi..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>srv_master_thread (75 samples, 1.00%)</title><rect x="201.7" y="2436" width="11.7" height="599.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="204.67" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (75 samples, 1.00%)</title><rect x="201.7" y="3636" width="11.7" height="599.0" fill="rgb(104,104,225)" rx="2" ry="2" />
<text text-anchor="" x="204.67" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (250 samples, 3.33%)</title><rect x="617.7" y="4236" width="39.2" height="599.0" fill="rgb(138,138,254)" rx="2" ry="2" />
<text text-anchor="" x="620.67" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (85 samples, 1.13%)</title><rect x="1091.8" y="3036" width="13.4" height="599.0" fill="rgb(122,122,240)" rx="2" ry="2" />
<text text-anchor="" x="1094.81" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (285 samples, 3.79%)</title><rect x="144.3" y="4236" width="44.8" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="147.32" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>os_file_write_func (165 samples, 2.20%)</title><rect x="837.3" y="1236" width="25.9" height="599.0" fill="rgb(100,100,222)" rx="2" ry="2" />
<text text-anchor="" x="840.30" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >o..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ext4_sync_file (85 samples, 1.13%)</title><rect x="347.5" y="636" width="13.3" height="599.0" fill="rgb(119,119,237)" rx="2" ry="2" />
<text text-anchor="" x="350.46" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cache_manager (125 samples, 1.66%)</title><rect x="988.1" y="3036" width="19.7" height="599.0" fill="rgb(118,118,236)" rx="2" ry="2" />
<text text-anchor="" x="991.12" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_recvfrom (155 samples, 2.06%)</title><rect x="946.5" y="3636" width="24.3" height="599.0" fill="rgb(85,85,209)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>inet_recvmsg (155 samples, 2.06%)</title><rect x="946.5" y="1836" width="24.3" height="599.0" fill="rgb(99,99,220)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >i..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>socket_write (125 samples, 1.66%)</title><rect x="988.1" y="1236" width="19.7" height="599.0" fill="rgb(114,114,233)" rx="2" ry="2" />
<text text-anchor="" x="991.12" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>generic_file_read_iter (180 samples, 2.40%)</title><rect x="432.3" y="636" width="28.3" height="599.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="435.29" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >g..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>enrich_data (220 samples, 2.93%)</title><rect x="863.2" y="1836" width="34.6" height="599.0" fill="rgb(111,111,231)" rx="2" ry="2" />
<text text-anchor="" x="866.23" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >en..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>send_command (110 samples, 1.46%)</title><rect x="970.8" y="1836" width="17.3" height="599.0" fill="rgb(129,129,246)" rx="2" ry="2" />
<text text-anchor="" x="973.84" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sock_sendmsg (190 samples, 2.53%)</title><rect x="1007.8" y="2436" width="29.8" height="599.0" fill="rgb(131,131,248)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >so..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (320 samples, 4.26%)</title><rect x="94.1" y="4236" width="50.2" height="599.0" fill="rgb(106,106,227)" rx="2" ry="2" />
<text text-anchor="" x="97.05" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>query_cache (220 samples, 2.93%)</title><rect x="863.2" y="636" width="34.6" height="599.0" fill="rgb(130,130,247)" rx="2" ry="2" />
<text text-anchor="" x="866.23" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >qu..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (88 samples, 1.17%)</title><rect x="674.2" y="4236" width="13.9" height="599.0" fill="rgb(117,117,236)" rx="2" ry="2" />
<text text-anchor="" x="677.23" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>malloc (135 samples, 1.80%)</title><rect x="33.6" y="1836" width="21.2" height="599.0" fill="rgb(131,131,248)" rx="2" ry="2" />
<text text-anchor="" x="36.57" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_page_fault (220 samples, 2.93%)</title><rect x="213.4" y="3636" width="34.6" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="216.45" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (95 samples, 1.26%)</title><rect x="703.0" y="3636" width="14.9" height="599.0" fill="rgb(130,130,246)" rx="2" ry="2" />
<text text-anchor="" x="705.98" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (140 samples, 1.86%)</title><rect x="808.2" y="4236" width="22.0" height="599.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="811.24" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (155 samples, 2.06%)</title><rect x="69.7" y="3636" width="24.4" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="72.70" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >b..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__generic_file_write_iter (160 samples, 2.13%)</title><rect x="460.6" y="1236" width="25.1" height="599.0" fill="rgb(133,133,249)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>write_record (245 samples, 3.26%)</title><rect x="897.8" y="2436" width="38.5" height="599.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >wri..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_read (180 samples, 2.40%)</title><rect x="432.3" y="1836" width="28.3" height="599.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="435.29" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_connection (245 samples, 3.26%)</title><rect x="897.8" y="5436" width="38.5" height="599.0" fill="rgb(89,89,213)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >han..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>allocate_buffer (155 samples, 2.06%)</title><rect x="69.7" y="636" width="24.4" height="599.0" fill="rgb(98,98,220)" rx="2" ry="2" />
<text text-anchor="" x="72.70" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >a..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__do_munmap (85 samples, 1.13%)</title><rect x="1091.8" y="1236" width="13.4" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="1094.81" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io_manager (145 samples, 1.93%)</title><rect x="298.0" y="3036" width="22.7" height="599.0" fill="rgb(128,128,245)" rx="2" ry="2" />
<text text-anchor="" x="300.97" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >i..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>data_processor (220 samples, 2.93%)</title><rect x="863.2" y="3036" width="34.6" height="599.0" fill="rgb(108,108,228)" rx="2" ry="2" />
<text text-anchor="" x="866.23" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >da..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (170 samples, 2.26%)</title><rect x="320.7" y="4236" width="26.8" height="599.0" fill="rgb(102,102,223)" rx="2" ry="2" />
<text text-anchor="" x="323.75" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>worker_thread (150 samples, 2.00%)</title><rect x="1037.6" y="2436" width="23.6" height="599.0" fill="rgb(99,99,220)" rx="2" ry="2" />
<text text-anchor="" x="1040.61" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__sys_sendto (190 samples, 2.53%)</title><rect x="1007.8" y="3036" width="29.8" height="599.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>calculate_derived_fields (285 samples, 3.79%)</title><rect x="144.3" y="636" width="44.8" height="599.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="147.32" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >calc..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>pwrite (165 samples, 2.20%)</title><rect x="837.3" y="636" width="25.9" height="599.0" fill="rgb(107,107,228)" rx="2" ry="2" />
<text text-anchor="" x="840.30" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>os_aio_func (195 samples, 2.60%)</title><rect x="750.1" y="636" width="30.6" height="599.0" fill="rgb(99,99,221)" rx="2" ry="2" />
<text text-anchor="" x="753.11" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >os..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (85 samples, 1.13%)</title><rect x="347.5" y="4236" width="13.3" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="350.46" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (135 samples, 1.80%)</title><rect x="33.6" y="4236" width="21.2" height="599.0" fill="rgb(131,131,247)" rx="2" ry="2" />
<text text-anchor="" x="36.57" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ext4_file_write_iter (160 samples, 2.13%)</title><rect x="460.6" y="1836" width="25.1" height="599.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (195 samples, 2.60%)</title><rect x="1061.2" y="4236" width="30.6" height="599.0" fill="rgb(117,117,236)" rx="2" ry="2" />
<text text-anchor="" x="1064.18" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>stack_trace_capture (65 samples, 0.87%)</title><rect x="936.3" y="636" width="10.2" height="599.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="939.28" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_sendmsg (190 samples, 2.53%)</title><rect x="1007.8" y="1236" width="29.8" height="599.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >tc..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (140 samples, 1.86%)</title><rect x="728.1" y="4836" width="22.0" height="599.0" fill="rgb(107,107,227)" rx="2" ry="2" />
<text text-anchor="" x="731.12" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (85 samples, 1.13%)</title><rect x="1091.8" y="3636" width="13.4" height="599.0" fill="rgb(93,93,216)" rx="2" ry="2" />
<text text-anchor="" x="1094.81" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>writeback_sb_inodes (145 samples, 1.93%)</title><rect x="360.8" y="2436" width="22.8" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (175 samples, 2.33%)</title><rect x="780.7" y="5436" width="27.5" height="599.0" fill="rgb(106,106,226)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="5738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>format_error_message (65 samples, 0.87%)</title><rect x="936.3" y="1236" width="10.2" height="599.0" fill="rgb(98,98,220)" rx="2" ry="2" />
<text text-anchor="" x="939.28" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>validate_chunk (165 samples, 2.20%)</title><rect x="1105.2" y="636" width="25.9" height="599.0" fill="rgb(94,94,217)" rx="2" ry="2" />
<text text-anchor="" x="1108.16" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >v..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dequeue_skb (98 samples, 1.30%)</title><rect x="267.6" y="1836" width="15.4" height="599.0" fill="rgb(93,93,216)" rx="2" ry="2" />
<text text-anchor="" x="270.65" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_response (140 samples, 1.86%)</title><rect x="808.2" y="2436" width="22.0" height="599.0" fill="rgb(114,114,233)" rx="2" ry="2" />
<text text-anchor="" x="811.24" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (45 samples, 0.60%)</title><rect x="396.9" y="3636" width="7.1" height="599.0" fill="rgb(120,120,238)" rx="2" ry="2" />
<text text-anchor="" x="399.94" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_syscall_64 (75 samples, 1.00%)</title><rect x="10.0" y="3636" width="11.8" height="599.0" fill="rgb(122,122,240)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>format_timestamp (95 samples, 1.26%)</title><rect x="703.0" y="1836" width="14.9" height="599.0" fill="rgb(124,124,242)" rx="2" ry="2" />
<text text-anchor="" x="705.98" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (75 samples, 1.00%)</title><rect x="10.0" y="4836" width="11.8" height="599.0" fill="rgb(101,101,222)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_execute_command (210 samples, 2.80%)</title><rect x="584.7" y="3036" width="33.0" height="599.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>log_writer (165 samples, 2.20%)</title><rect x="837.3" y="3036" width="25.9" height="599.0" fill="rgb(98,98,220)" rx="2" ry="2" />
<text text-anchor="" x="840.30" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >l..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>send_command (125 samples, 1.66%)</title><rect x="988.1" y="1836" width="19.7" height="599.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="991.12" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>lookup_reference_data (220 samples, 2.93%)</title><rect x="863.2" y="1236" width="34.6" height="599.0" fill="rgb(132,132,249)" rx="2" ry="2" />
<text text-anchor="" x="866.23" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lo..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (195 samples, 2.60%)</title><rect x="750.1" y="4836" width="30.6" height="599.0" fill="rgb(130,130,247)" rx="2" ry="2" />
<text text-anchor="" x="753.11" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (145 samples, 1.93%)</title><rect x="298.0" y="3636" width="22.7" height="599.0" fill="rgb(107,107,227)" rx="2" ry="2" />
<text text-anchor="" x="300.97" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >b..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__x64_sys_sendto (190 samples, 2.53%)</title><rect x="1007.8" y="3636" width="29.8" height="599.0" fill="rgb(82,82,207)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >__..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_command (185 samples, 2.46%)</title><rect x="511.6" y="4236" width="29.1" height="599.0" fill="rgb(134,134,250)" rx="2" ry="2" />
<text text-anchor="" x="514.63" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (65 samples, 0.87%)</title><rect x="717.9" y="3636" width="10.2" height="599.0" fill="rgb(101,101,222)" rx="2" ry="2" />
<text text-anchor="" x="720.91" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (98 samples, 1.30%)</title><rect x="267.6" y="4836" width="15.4" height="599.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="270.65" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ksys_read (175 samples, 2.33%)</title><rect x="780.7" y="3036" width="27.5" height="599.0" fill="rgb(102,102,223)" rx="2" ry="2" />
<text text-anchor="" x="783.75" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>config_loader (55 samples, 0.73%)</title><rect x="1181.4" y="2436" width="8.6" height="599.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="1184.36" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mark_and_sweep (65 samples, 0.87%)</title><rect x="717.9" y="1236" width="10.2" height="599.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="720.91" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysqld (280 samples, 3.73%)</title><rect x="540.7" y="6036" width="44.0" height="599.0" fill="rgb(107,107,227)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="6338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>io_manager (95 samples, 1.26%)</title><rect x="283.0" y="3036" width="15.0" height="599.0" fill="rgb(136,136,252)" rx="2" ry="2" />
<text text-anchor="" x="286.04" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (85 samples, 1.13%)</title><rect x="1091.8" y="4236" width="13.4" height="599.0" fill="rgb(118,118,236)" rx="2" ry="2" />
<text text-anchor="" x="1094.81" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (75 samples, 1.00%)</title><rect x="10.0" y="4236" width="11.8" height="599.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>parse_yaml_config (55 samples, 0.73%)</title><rect x="1181.4" y="1836" width="8.6" height="599.0" fill="rgb(99,99,220)" rx="2" ry="2" />
<text text-anchor="" x="1184.36" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>net_rx_action (110 samples, 1.46%)</title><rect x="656.9" y="4236" width="17.3" height="599.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="659.95" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>tcp_recvmsg (155 samples, 2.06%)</title><rect x="946.5" y="1236" width="24.3" height="599.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="949.49" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (45 samples, 0.60%)</title><rect x="396.9" y="4236" width="7.1" height="599.0" fill="rgb(129,129,246)" rx="2" ry="2" />
<text text-anchor="" x="399.94" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_one_work (145 samples, 1.93%)</title><rect x="360.8" y="4836" width="22.8" height="599.0" fill="rgb(107,107,227)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (250 samples, 3.33%)</title><rect x="617.7" y="3636" width="39.2" height="599.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="620.67" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >bli..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>validate_records (320 samples, 4.26%)</title><rect x="1131.1" y="1836" width="50.3" height="599.0" fill="rgb(137,137,253)" rx="2" ry="2" />
<text text-anchor="" x="1134.09" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >valid..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>buf_flush_do_batch (195 samples, 2.60%)</title><rect x="750.1" y="2436" width="30.6" height="599.0" fill="rgb(126,126,243)" rx="2" ry="2" />
<text text-anchor="" x="753.11" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >bu..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>wb_workfn (145 samples, 1.93%)</title><rect x="360.8" y="4236" width="22.8" height="599.0" fill="rgb(83,83,207)" rx="2" ry="2" />
<text text-anchor="" x="363.81" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (245 samples, 3.26%)</title><rect x="897.8" y="6636" width="38.5" height="599.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="6938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>build_request_payload (155 samples, 2.06%)</title><rect x="69.7" y="1836" width="24.4" height="599.0" fill="rgb(138,138,253)" rx="2" ry="2" />
<text text-anchor="" x="72.70" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >b..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>file_handler (170 samples, 2.26%)</title><rect x="320.7" y="3036" width="26.8" height="599.0" fill="rgb(138,138,253)" rx="2" ry="2" />
<text text-anchor="" x="323.75" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >f..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>vfs_fsync (88 samples, 1.17%)</title><rect x="674.2" y="1836" width="13.9" height="599.0" fill="rgb(129,129,246)" rx="2" ry="2" />
<text text-anchor="" x="677.23" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_data_chunk (150 samples, 2.00%)</title><rect x="1037.6" y="1236" width="23.6" height="599.0" fill="rgb(112,112,231)" rx="2" ry="2" />
<text text-anchor="" x="1040.61" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >p..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cpu_idle_loop (320 samples, 4.26%)</title><rect x="94.1" y="1836" width="50.2" height="599.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="97.05" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cpu_i..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ha_innobase::rnd_next (280 samples, 3.73%)</title><rect x="540.7" y="636" width="44.0" height="599.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="543.69" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha_i..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>generic_perform_write (325 samples, 4.33%)</title><rect x="460.6" y="636" width="51.0" height="599.0" fill="rgb(96,96,218)" rx="2" ry="2" />
<text text-anchor="" x="463.57" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >gener..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (140 samples, 1.86%)</title><rect x="728.1" y="4236" width="22.0" height="599.0" fill="rgb(100,100,222)" rx="2" ry="2" />
<text text-anchor="" x="731.12" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (320 samples, 4.26%)</title><rect x="1131.1" y="3636" width="50.3" height="599.0" fill="rgb(94,94,217)" rx="2" ry="2" />
<text text-anchor="" x="1134.09" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >blink..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>update_histogram (80 samples, 1.07%)</title><rect x="189.1" y="1236" width="12.6" height="599.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="192.10" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>srv_master_evict_from_table_cache (75 samples, 1.00%)</title><rect x="201.7" y="1236" width="11.7" height="599.0" fill="rgb(116,116,235)" rx="2" ry="2" />
<text text-anchor="" x="204.67" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (210 samples, 2.80%)</title><rect x="584.7" y="6036" width="33.0" height="599.0" fill="rgb(107,107,228)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="6338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>start_kernel (45 samples, 0.60%)</title><rect x="830.2" y="4236" width="7.1" height="599.0" fill="rgb(94,94,216)" rx="2" ry="2" />
<text text-anchor="" x="833.23" y="4538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>entry_SYSCALL_64_after_hwframe (190 samples, 2.53%)</title><rect x="1007.8" y="4836" width="29.8" height="599.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="1010.76" y="5138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >en..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>flush_buffers (85 samples, 1.13%)</title><rect x="347.5" y="2436" width="13.3" height="599.0" fill="rgb(100,100,222)" rx="2" ry="2" />
<text text-anchor="" x="350.46" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__fput (75 samples, 1.00%)</title><rect x="10.0" y="636" width="11.8" height="599.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>acquire_lock (85 samples, 1.13%)</title><rect x="383.6" y="1836" width="13.3" height="599.0" fill="rgb(124,124,242)" rx="2" ry="2" />
<text text-anchor="" x="386.59" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mark_objects (65 samples, 0.87%)</title><rect x="717.9" y="636" width="10.2" height="599.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="720.91" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>execute_task (150 samples, 2.00%)</title><rect x="1037.6" y="1836" width="23.6" height="599.0" fill="rgb(136,136,252)" rx="2" ry="2" />
<text text-anchor="" x="1040.61" y="2138.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >e..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>synchronize (45 samples, 0.60%)</title><rect x="396.9" y="2436" width="7.1" height="599.0" fill="rgb(122,122,240)" rx="2" ry="2" />
<text text-anchor="" x="399.94" y="2738.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>futex_unlock_pi (45 samples, 0.60%)</title><rect x="396.9" y="636" width="7.1" height="599.0" fill="rgb(88,88,212)" rx="2" ry="2" />
<text text-anchor="" x="399.94" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_insert (245 samples, 3.26%)</title><rect x="897.8" y="3036" width="38.5" height="599.0" fill="rgb(96,96,219)" rx="2" ry="2" />
<text text-anchor="" x="900.79" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>blinkit_app (165 samples, 2.20%)</title><rect x="1105.2" y="3636" width="25.9" height="599.0" fill="rgb(118,118,236)" rx="2" ry="2" />
<text text-anchor="" x="1108.16" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >b..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>do_anonymous_page (220 samples, 2.93%)</title><rect x="213.4" y="636" width="34.6" height="599.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="216.45" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >do..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ep_insert (95 samples, 1.26%)</title><rect x="283.0" y="636" width="15.0" height="599.0" fill="rgb(89,89,212)" rx="2" ry="2" />
<text text-anchor="" x="286.04" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>dispatch_command (210 samples, 2.80%)</title><rect x="584.7" y="3636" width="33.0" height="599.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="587.68" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >di..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>jbd2_complete_transaction (88 samples, 1.17%)</title><rect x="674.2" y="636" width="13.9" height="599.0" fill="rgb(100,100,222)" rx="2" ry="2" />
<text text-anchor="" x="677.23" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>qdisc_run (98 samples, 1.30%)</title><rect x="267.6" y="3636" width="15.4" height="599.0" fill="rgb(88,88,212)" rx="2" ry="2" />
<text text-anchor="" x="270.65" y="3938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>thread_pool (180 samples, 2.40%)</title><rect x="404.0" y="3036" width="28.3" height="599.0" fill="rgb(104,104,225)" rx="2" ry="2" />
<text text-anchor="" x="407.01" y="3338.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >t..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>link_path_walk (95 samples, 1.26%)</title><rect x="688.1" y="636" width="14.9" height="599.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="691.06" y="938.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handle_pte_fault (220 samples, 2.93%)</title><rect x="213.4" y="1236" width="34.6" height="599.0" fill="rgb(107,107,227)" rx="2" ry="2" />
<text text-anchor="" x="216.45" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>check_data_integrity (320 samples, 4.26%)</title><rect x="1131.1" y="1236" width="50.3" height="599.0" fill="rgb(125,125,243)" rx="2" ry="2" />
<text text-anchor="" x="1134.09" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >check..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__vfs_write (165 samples, 2.20%)</title><rect x="485.7" y="1236" width="25.9" height="599.0" fill="rgb(101,101,222)" rx="2" ry="2" />
<text text-anchor="" x="488.71" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>__libc_malloc (135 samples, 1.80%)</title><rect x="33.6" y="1236" width="21.2" height="599.0" fill="rgb(106,106,226)" rx="2" ry="2" />
<text text-anchor="" x="36.57" y="1538.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
</svg>
