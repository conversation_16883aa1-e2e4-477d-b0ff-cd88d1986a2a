<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="166" onload="init(evt)" viewBox="0 0 1200 166" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#f8f8f8" offset="5%" />
		<stop stop-color="#e8e8e8" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details, searchbtn, matchedtxt, svg;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
	}

	// mouse-over for info
	function s(node) {		// show
		info = g_to_text(node);
		details.nodeValue = "Function: " + info;
	}
	function c() {			// clear
		details.nodeValue = ' ';
	}

	// ctrl-F for search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
	})

	// functions
	function find_child(parent, name, attr) {
		var children = parent.childNodes;
		for (var i=0; i<children.length;i++) {
			if (children[i].tagName == name)
				return (attr != undefined) ? children[i].attributes[attr].value : children[i];
		}
		return;
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_"+attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_"+attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_"+attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes["width"].value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes["x"].value = parseFloat(r.attributes["x"].value) +3;

		// Smaller than this size won't fit anything
		if (w < 2*12*0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		// Fit in full text width
		if (/^ *$/.test(txt) || t.getSubStringLength(0, txt.length) < w)
			return;

		for (var x=txt.length-2; x>0; x--) {
			if (t.getSubStringLength(0, x+2) <= w) {
				t.textContent = txt.substring(0,x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = (parseFloat(e.attributes["x"].value) - x - 10) * ratio + 10;
				if(e.tagName == "text") e.attributes["x"].value = find_child(e.parentNode, "rect", "x") + 3;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseFloat(e.attributes["width"].value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_child(c[i], x-10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = 10;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseInt(svg.width.baseVal.value) - (10*2);
			}
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr["width"].value);
		var xmin = parseFloat(attr["x"].value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr["y"].value);
		var ratio = (svg.width.baseVal.value - 2*10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "1.0";

		var el = document.getElementsByTagName("g");
		for(var i=0;i<el.length;i++){
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a["x"].value);
			var ew = parseFloat(a["width"].value);
			// Is it an ancestor
			if (0 == 0) {
				var upstack = parseFloat(a["y"].value) > ymin;
			} else {
				var upstack = parseFloat(a["y"].value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.style["opacity"] = "0.5";
					zoom_parent(e);
					e.onclick = function(e){unzoom(); zoom(this);};
					update_text(e);
				}
				// not in current path
				else
					e.style["display"] = "none";
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.style["display"] = "none";
				}
				else {
					zoom_child(e, xmin, ratio);
					e.onclick = function(e){zoom(this);};
					update_text(e);
				}
			}
		}
	}
	function unzoom() {
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "0.0";

		var el = document.getElementsByTagName("g");
		for(i=0;i<el.length;i++) {
			el[i].style["display"] = "block";
			el[i].style["opacity"] = "1";
			zoom_reset(el[i]);
			update_text(el[i]);
		}
	}

	// search
	function reset_search() {
		var el = document.getElementsByTagName("rect");
		for (var i=0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)", "");
			if (term != null) {
				search(term)
			}
		} else {
			reset_search();
			searching = 0;
			searchbtn.style["opacity"] = "0.1";
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.style["opacity"] = "0.0";
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		var re = new RegExp(term);
		var el = document.getElementsByTagName("g");
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			if (e.attributes["class"].value != "func_g")
				continue;
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (rect == null) {
				// the rect might be wrapped in an anchor
				// if nameattr href is being used
				if (rect = find_child(e, "a")) {
				    rect = find_child(r, "rect");
				}
			}
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes["width"].value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes["x"].value);
				orig_save(rect, "fill");
				rect.attributes["fill"].value =
				    "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;

		searchbtn.style["opacity"] = "1.0";
		searchbtn.firstChild.nodeValue = "Reset Search"

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.style["opacity"] = "1.0";
		pct = 100 * count / maxwidth;
		if (pct == 100)
			pct = "100"
		else
			pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
	function searchover(e) {
		searchbtn.style["opacity"] = "1.0";
	}
	function searchout(e) {
		if (searching) {
			searchbtn.style["opacity"] = "1.0";
		} else {
			searchbtn.style["opacity"] = "0.1";
		}
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="166.0" fill="url(#background)"  />
<text text-anchor="middle" x="600.00" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Blinkit Data Staging - Icicle Graph</text>
<text text-anchor="" x="10.00" y="149" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<text text-anchor="" x="10.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="unzoom" onclick="unzoom()" style="opacity:0.0;cursor:pointer" >Reset Zoom</text>
<text text-anchor="" x="1090.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="search" onmouseover="searchover()" onmouseout="searchout()" onclick="search_prompt()" style="opacity:0.1;cursor:pointer" >Search</text>
<text text-anchor="" x="1090.00" y="149" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="matched" > </text>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (180 samples, 7.36%)</title><rect x="955.9" y="53" width="86.9" height="15.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="958.93" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataStagin..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>FileHandler (75 samples, 3.07%)</title><rect x="499.9" y="69" width="36.2" height="15.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="502.86" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Fil..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>worker_thread (45 samples, 1.84%)</title><rect x="1149.0" y="85" width="21.7" height="15.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="1151.98" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (25 samples, 1.02%)</title><rect x="936.6" y="53" width="12.1" height="15.0" fill="rgb(132,132,249)" rx="2" ry="2" />
<text text-anchor="" x="939.63" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>redis_del (30 samples, 1.23%)</title><rect x="854.6" y="101" width="14.5" height="15.0" fill="rgb(130,130,246)" rx="2" ry="2" />
<text text-anchor="" x="857.58" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>APIClient (95 samples, 3.89%)</title><rect x="367.1" y="69" width="45.9" height="15.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="370.14" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >APIC..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (30 samples, 1.23%)</title><rect x="87.2" y="53" width="14.5" height="15.0" fill="rgb(89,89,212)" rx="2" ry="2" />
<text text-anchor="" x="90.22" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handleResponse (70 samples, 2.86%)</title><rect x="466.1" y="85" width="33.8" height="15.0" fill="rgb(106,106,226)" rx="2" ry="2" />
<text text-anchor="" x="469.07" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (140 samples, 5.73%)</title><rect x="724.3" y="37" width="67.5" height="15.0" fill="rgb(126,126,243)" rx="2" ry="2" />
<text text-anchor="" x="727.27" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>transformData (180 samples, 7.36%)</title><rect x="955.9" y="101" width="86.9" height="15.0" fill="rgb(129,129,245)" rx="2" ry="2" />
<text text-anchor="" x="958.93" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >transformD..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>wait_for_task (45 samples, 1.84%)</title><rect x="1149.0" y="101" width="21.7" height="15.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="1151.98" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (50 samples, 2.04%)</title><rect x="164.4" y="53" width="24.2" height="15.0" fill="rgb(124,124,242)" rx="2" ry="2" />
<text text-anchor="" x="167.44" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >D..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>CacheManager (55 samples, 2.25%)</title><rect x="900.4" y="69" width="26.6" height="15.0" fill="rgb(101,101,223)" rx="2" ry="2" />
<text text-anchor="" x="903.43" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >C..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (110 samples, 4.50%)</title><rect x="413.0" y="53" width="53.1" height="15.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="415.99" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataS..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>setup_logging (15 samples, 0.61%)</title><rect x="948.7" y="101" width="7.2" height="15.0" fill="rgb(138,138,253)" rx="2" ry="2" />
<text text-anchor="" x="951.69" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (80 samples, 3.27%)</title><rect x="316.5" y="37" width="38.6" height="15.0" fill="rgb(114,114,234)" rx="2" ry="2" />
<text text-anchor="" x="319.46" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (40 samples, 1.64%)</title><rect x="1170.7" y="53" width="19.3" height="15.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="1173.70" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (25 samples, 1.02%)</title><rect x="355.1" y="37" width="12.0" height="15.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="358.07" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>CacheManager (65 samples, 2.66%)</title><rect x="869.1" y="69" width="31.3" height="15.0" fill="rgb(104,104,225)" rx="2" ry="2" />
<text text-anchor="" x="872.06" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Ca..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>redis_get (65 samples, 2.66%)</title><rect x="869.1" y="101" width="31.3" height="15.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="872.06" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >re..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>processRecords (200 samples, 8.18%)</title><rect x="1052.5" y="85" width="96.5" height="15.0" fill="rgb(106,106,227)" rx="2" ry="2" />
<text text-anchor="" x="1055.45" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >processReco..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>send_alert (20 samples, 0.82%)</title><rect x="927.0" y="101" width="9.6" height="15.0" fill="rgb(107,107,228)" rx="2" ry="2" />
<text text-anchor="" x="929.97" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>FileHandler (85 samples, 3.48%)</title><rect x="123.4" y="69" width="41.0" height="15.0" fill="rgb(94,94,216)" rx="2" ry="2" />
<text text-anchor="" x="126.42" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Fil..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>FileHandler (100 samples, 4.09%)</title><rect x="188.6" y="69" width="48.2" height="15.0" fill="rgb(122,122,240)" rx="2" ry="2" />
<text text-anchor="" x="191.57" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >File..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>batchProcessor (140 samples, 5.73%)</title><rect x="724.3" y="85" width="67.5" height="15.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="727.27" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >batchPr..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataProcessor (160 samples, 6.54%)</title><rect x="10.0" y="69" width="77.2" height="15.0" fill="rgb(88,88,212)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataProc..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Metrics (30 samples, 1.23%)</title><rect x="87.2" y="69" width="14.5" height="15.0" fill="rgb(105,105,226)" rx="2" ry="2" />
<text text-anchor="" x="90.22" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (180 samples, 7.36%)</title><rect x="955.9" y="37" width="86.9" height="15.0" fill="rgb(111,111,231)" rx="2" ry="2" />
<text text-anchor="" x="958.93" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (35 samples, 1.43%)</title><rect x="299.6" y="69" width="16.9" height="15.0" fill="rgb(101,101,223)" rx="2" ry="2" />
<text text-anchor="" x="302.57" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (45 samples, 1.84%)</title><rect x="101.7" y="53" width="21.7" height="15.0" fill="rgb(131,131,247)" rx="2" ry="2" />
<text text-anchor="" x="104.70" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >D..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ConfigManager (20 samples, 0.82%)</title><rect x="1042.8" y="85" width="9.7" height="15.0" fill="rgb(120,120,238)" rx="2" ry="2" />
<text text-anchor="" x="1045.80" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>free (40 samples, 1.64%)</title><rect x="236.8" y="101" width="19.3" height="15.0" fill="rgb(131,131,248)" rx="2" ry="2" />
<text text-anchor="" x="239.83" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handleException (35 samples, 1.43%)</title><rect x="536.1" y="85" width="16.8" height="15.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="539.05" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (15 samples, 0.61%)</title><rect x="948.7" y="53" width="7.2" height="15.0" fill="rgb(93,93,216)" rx="2" ry="2" />
<text text-anchor="" x="951.69" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (95 samples, 3.89%)</title><rect x="367.1" y="37" width="45.9" height="15.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="370.14" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>writeLog (50 samples, 2.04%)</title><rect x="164.4" y="85" width="24.2" height="15.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="167.44" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (110 samples, 4.50%)</title><rect x="413.0" y="37" width="53.1" height="15.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="415.99" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (75 samples, 3.07%)</title><rect x="499.9" y="53" width="36.2" height="15.0" fill="rgb(93,93,216)" rx="2" ry="2" />
<text text-anchor="" x="502.86" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Dat..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (70 samples, 2.86%)</title><rect x="466.1" y="37" width="33.8" height="15.0" fill="rgb(118,118,236)" rx="2" ry="2" />
<text text-anchor="" x="469.07" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (100 samples, 4.09%)</title><rect x="188.6" y="53" width="48.2" height="15.0" fill="rgb(105,105,226)" rx="2" ry="2" />
<text text-anchor="" x="191.57" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Data..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataProcessor (200 samples, 8.18%)</title><rect x="1052.5" y="69" width="96.5" height="15.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="1055.45" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataProcessor</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>processRecords (180 samples, 7.36%)</title><rect x="955.9" y="85" width="86.9" height="15.0" fill="rgb(89,89,212)" rx="2" ry="2" />
<text text-anchor="" x="958.93" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >processRec..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (130 samples, 5.32%)</title><rect x="791.8" y="37" width="62.8" height="15.0" fill="rgb(119,119,238)" rx="2" ry="2" />
<text text-anchor="" x="794.84" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>setup_database (25 samples, 1.02%)</title><rect x="936.6" y="101" width="12.1" height="15.0" fill="rgb(128,128,245)" rx="2" ry="2" />
<text text-anchor="" x="939.63" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sendRequest (110 samples, 4.50%)</title><rect x="413.0" y="85" width="53.1" height="15.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="415.99" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sendR..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>garbage_collect (35 samples, 1.43%)</title><rect x="299.6" y="101" width="16.9" height="15.0" fill="rgb(113,113,233)" rx="2" ry="2" />
<text text-anchor="" x="302.57" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>FileHandler (90 samples, 3.68%)</title><rect x="256.1" y="69" width="43.5" height="15.0" fill="rgb(104,104,225)" rx="2" ry="2" />
<text text-anchor="" x="259.13" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >File..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>yaml_parse (40 samples, 1.64%)</title><rect x="1170.7" y="101" width="19.3" height="15.0" fill="rgb(89,89,212)" rx="2" ry="2" />
<text text-anchor="" x="1173.70" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (150 samples, 6.13%)</title><rect x="651.9" y="37" width="72.4" height="15.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="654.88" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>file_write (50 samples, 2.04%)</title><rect x="164.4" y="101" width="24.2" height="15.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="167.44" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >f..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mutex_lock (25 samples, 1.02%)</title><rect x="581.9" y="101" width="12.1" height="15.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="584.90" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>csv_parser (85 samples, 3.48%)</title><rect x="123.4" y="101" width="41.0" height="15.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="126.42" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >csv..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (200 samples, 8.18%)</title><rect x="1052.5" y="37" width="96.5" height="15.0" fill="rgb(124,124,242)" rx="2" ry="2" />
<text text-anchor="" x="1055.45" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (140 samples, 5.73%)</title><rect x="724.3" y="53" width="67.5" height="15.0" fill="rgb(94,94,216)" rx="2" ry="2" />
<text text-anchor="" x="727.27" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataSta..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>parseCSV (85 samples, 3.48%)</title><rect x="123.4" y="85" width="41.0" height="15.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="126.42" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >par..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Logger (50 samples, 2.04%)</title><rect x="164.4" y="69" width="24.2" height="15.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="167.44" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Metrics (25 samples, 1.02%)</title><rect x="355.1" y="69" width="12.0" height="15.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="358.07" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (50 samples, 2.04%)</title><rect x="164.4" y="37" width="24.2" height="15.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="167.44" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_query (150 samples, 6.13%)</title><rect x="651.9" y="101" width="72.4" height="15.0" fill="rgb(126,126,243)" rx="2" ry="2" />
<text text-anchor="" x="654.88" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql_qu..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (160 samples, 6.54%)</title><rect x="10.0" y="37" width="77.2" height="15.0" fill="rgb(123,123,240)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (40 samples, 1.64%)</title><rect x="236.8" y="53" width="19.3" height="15.0" fill="rgb(116,116,235)" rx="2" ry="2" />
<text text-anchor="" x="239.83" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>get_connection (80 samples, 3.27%)</title><rect x="316.5" y="101" width="38.6" height="15.0" fill="rgb(83,83,208)" rx="2" ry="2" />
<text text-anchor="" x="319.46" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >get..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>readFile (100 samples, 4.09%)</title><rect x="188.6" y="85" width="48.2" height="15.0" fill="rgb(99,99,221)" rx="2" ry="2" />
<text text-anchor="" x="191.57" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >read..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>counter_increment (30 samples, 1.23%)</title><rect x="87.2" y="101" width="14.5" height="15.0" fill="rgb(138,138,254)" rx="2" ry="2" />
<text text-anchor="" x="90.22" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>MemoryManager (40 samples, 1.64%)</title><rect x="236.8" y="69" width="19.3" height="15.0" fill="rgb(108,108,228)" rx="2" ry="2" />
<text text-anchor="" x="239.83" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>recordMetric (30 samples, 1.23%)</title><rect x="87.2" y="85" width="14.5" height="15.0" fill="rgb(83,83,207)" rx="2" ry="2" />
<text text-anchor="" x="90.22" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>writeFile (90 samples, 3.68%)</title><rect x="256.1" y="85" width="43.5" height="15.0" fill="rgb(96,96,218)" rx="2" ry="2" />
<text text-anchor="" x="259.13" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >writ..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>synchronize (25 samples, 1.02%)</title><rect x="581.9" y="85" width="12.1" height="15.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="584.90" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (25 samples, 1.02%)</title><rect x="581.9" y="37" width="12.1" height="15.0" fill="rgb(108,108,228)" rx="2" ry="2" />
<text text-anchor="" x="584.90" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>get (65 samples, 2.66%)</title><rect x="869.1" y="85" width="31.3" height="15.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="872.06" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >get</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>deallocate (40 samples, 1.64%)</title><rect x="236.8" y="85" width="19.3" height="15.0" fill="rgb(108,108,228)" rx="2" ry="2" />
<text text-anchor="" x="239.83" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (90 samples, 3.68%)</title><rect x="256.1" y="37" width="43.5" height="15.0" fill="rgb(97,97,219)" rx="2" ry="2" />
<text text-anchor="" x="259.13" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (200 samples, 8.18%)</title><rect x="1052.5" y="53" width="96.5" height="15.0" fill="rgb(123,123,240)" rx="2" ry="2" />
<text text-anchor="" x="1055.45" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataStaging..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (90 samples, 3.68%)</title><rect x="256.1" y="53" width="43.5" height="15.0" fill="rgb(93,93,216)" rx="2" ry="2" />
<text text-anchor="" x="259.13" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Data..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DatabaseConnector (150 samples, 6.13%)</title><rect x="651.9" y="69" width="72.4" height="15.0" fill="rgb(96,96,219)" rx="2" ry="2" />
<text text-anchor="" x="654.88" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Database..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>APIClient (70 samples, 2.86%)</title><rect x="466.1" y="69" width="33.8" height="15.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="469.07" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >AP..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (65 samples, 2.66%)</title><rect x="869.1" y="37" width="31.3" height="15.0" fill="rgb(111,111,231)" rx="2" ry="2" />
<text text-anchor="" x="872.06" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>recordMetric (25 samples, 1.02%)</title><rect x="355.1" y="85" width="12.0" height="15.0" fill="rgb(107,107,227)" rx="2" ry="2" />
<text text-anchor="" x="358.07" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (150 samples, 6.13%)</title><rect x="651.9" y="53" width="72.4" height="15.0" fill="rgb(105,105,225)" rx="2" ry="2" />
<text text-anchor="" x="654.88" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataStag..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (30 samples, 1.23%)</title><rect x="854.6" y="37" width="14.5" height="15.0" fill="rgb(93,93,216)" rx="2" ry="2" />
<text text-anchor="" x="857.58" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ErrorHandler (20 samples, 0.82%)</title><rect x="927.0" y="69" width="9.6" height="15.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="929.97" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>validateConfig (20 samples, 0.82%)</title><rect x="1042.8" y="101" width="9.7" height="15.0" fill="rgb(113,113,233)" rx="2" ry="2" />
<text text-anchor="" x="1045.80" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handleException (20 samples, 0.82%)</title><rect x="927.0" y="85" width="9.6" height="15.0" fill="rgb(107,107,227)" rx="2" ry="2" />
<text text-anchor="" x="929.97" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>redis_set (55 samples, 2.25%)</title><rect x="900.4" y="101" width="26.6" height="15.0" fill="rgb(105,105,226)" rx="2" ry="2" />
<text text-anchor="" x="903.43" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >r..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>log_error (35 samples, 1.43%)</title><rect x="536.1" y="101" width="16.8" height="15.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="539.05" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (25 samples, 1.02%)</title><rect x="581.9" y="53" width="12.1" height="15.0" fill="rgb(111,111,231)" rx="2" ry="2" />
<text text-anchor="" x="584.90" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fwrite (90 samples, 3.68%)</title><rect x="256.1" y="101" width="43.5" height="15.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="259.13" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >fwrite</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (20 samples, 0.82%)</title><rect x="1042.8" y="69" width="9.7" height="15.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="1045.80" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>loadConfig (40 samples, 1.64%)</title><rect x="1170.7" y="85" width="19.3" height="15.0" fill="rgb(125,125,242)" rx="2" ry="2" />
<text text-anchor="" x="1173.70" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_task (130 samples, 5.32%)</title><rect x="791.8" y="101" width="62.8" height="15.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="794.84" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >proces..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataProcessor (180 samples, 7.36%)</title><rect x="955.9" y="69" width="86.9" height="15.0" fill="rgb(101,101,222)" rx="2" ry="2" />
<text text-anchor="" x="958.93" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataProces..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>executeQuery (150 samples, 6.13%)</title><rect x="651.9" y="85" width="72.4" height="15.0" fill="rgb(126,126,243)" rx="2" ry="2" />
<text text-anchor="" x="654.88" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >executeQ..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DatabaseConnector (80 samples, 3.27%)</title><rect x="316.5" y="69" width="38.6" height="15.0" fill="rgb(115,115,234)" rx="2" ry="2" />
<text text-anchor="" x="319.46" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Dat..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ErrorHandler (35 samples, 1.43%)</title><rect x="536.1" y="69" width="16.8" height="15.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="539.05" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>APIClient (110 samples, 4.50%)</title><rect x="413.0" y="69" width="53.1" height="15.0" fill="rgb(97,97,219)" rx="2" ry="2" />
<text text-anchor="" x="415.99" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >APICl..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>MemoryManager (35 samples, 1.43%)</title><rect x="299.6" y="85" width="16.9" height="15.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="302.57" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (60 samples, 2.45%)</title><rect x="552.9" y="53" width="29.0" height="15.0" fill="rgb(90,90,213)" rx="2" ry="2" />
<text text-anchor="" x="555.94" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Da..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (95 samples, 3.89%)</title><rect x="367.1" y="53" width="45.9" height="15.0" fill="rgb(86,86,210)" rx="2" ry="2" />
<text text-anchor="" x="370.14" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Data..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (30 samples, 1.23%)</title><rect x="854.6" y="53" width="14.5" height="15.0" fill="rgb(111,111,231)" rx="2" ry="2" />
<text text-anchor="" x="857.58" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (60 samples, 2.45%)</title><rect x="552.9" y="37" width="29.0" height="15.0" fill="rgb(100,100,221)" rx="2" ry="2" />
<text text-anchor="" x="555.94" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (80 samples, 3.27%)</title><rect x="316.5" y="53" width="38.6" height="15.0" fill="rgb(126,126,244)" rx="2" ry="2" />
<text text-anchor="" x="319.46" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Dat..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>processRecords (160 samples, 6.54%)</title><rect x="10.0" y="85" width="77.2" height="15.0" fill="rgb(115,115,234)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >processR..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (25 samples, 1.02%)</title><rect x="355.1" y="53" width="12.0" height="15.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="358.07" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (35 samples, 1.43%)</title><rect x="299.6" y="53" width="16.9" height="15.0" fill="rgb(136,136,252)" rx="2" ry="2" />
<text text-anchor="" x="302.57" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (65 samples, 2.66%)</title><rect x="869.1" y="53" width="31.3" height="15.0" fill="rgb(125,125,242)" rx="2" ry="2" />
<text text-anchor="" x="872.06" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Da..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (45 samples, 1.84%)</title><rect x="1149.0" y="37" width="21.7" height="15.0" fill="rgb(88,88,211)" rx="2" ry="2" />
<text text-anchor="" x="1151.98" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (20 samples, 0.82%)</title><rect x="927.0" y="53" width="9.6" height="15.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="929.97" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DatabaseConnector (45 samples, 1.84%)</title><rect x="101.7" y="69" width="21.7" height="15.0" fill="rgb(110,110,230)" rx="2" ry="2" />
<text text-anchor="" x="104.70" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >D..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sendRequest (95 samples, 3.89%)</title><rect x="367.1" y="85" width="45.9" height="15.0" fill="rgb(116,116,235)" rx="2" ry="2" />
<text text-anchor="" x="370.14" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >send..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (85 samples, 3.48%)</title><rect x="123.4" y="37" width="41.0" height="15.0" fill="rgb(132,132,248)" rx="2" ry="2" />
<text text-anchor="" x="126.42" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (15 samples, 0.61%)</title><rect x="948.7" y="69" width="7.2" height="15.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="951.69" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DatabaseConnector (120 samples, 4.91%)</title><rect x="594.0" y="69" width="57.9" height="15.0" fill="rgb(111,111,231)" rx="2" ry="2" />
<text text-anchor="" x="596.97" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Databa..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (120 samples, 4.91%)</title><rect x="594.0" y="53" width="57.9" height="15.0" fill="rgb(129,129,246)" rx="2" ry="2" />
<text text-anchor="" x="596.97" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataSt..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (45 samples, 1.84%)</title><rect x="1149.0" y="53" width="21.7" height="15.0" fill="rgb(117,117,236)" rx="2" ry="2" />
<text text-anchor="" x="1151.98" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >D..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>malloc (60 samples, 2.45%)</title><rect x="552.9" y="101" width="29.0" height="15.0" fill="rgb(117,117,235)" rx="2" ry="2" />
<text text-anchor="" x="555.94" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ma..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>MemoryManager (60 samples, 2.45%)</title><rect x="552.9" y="69" width="29.0" height="15.0" fill="rgb(138,138,253)" rx="2" ry="2" />
<text text-anchor="" x="555.94" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Me..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ConfigManager (40 samples, 1.64%)</title><rect x="1170.7" y="69" width="19.3" height="15.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="1173.70" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (55 samples, 2.25%)</title><rect x="900.4" y="53" width="26.6" height="15.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="903.43" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >D..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>json_decode (70 samples, 2.86%)</title><rect x="466.1" y="101" width="33.8" height="15.0" fill="rgb(121,121,239)" rx="2" ry="2" />
<text text-anchor="" x="469.07" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >js..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (30 samples, 1.23%)</title><rect x="87.2" y="37" width="14.5" height="15.0" fill="rgb(111,111,231)" rx="2" ry="2" />
<text text-anchor="" x="90.22" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>allocate (60 samples, 2.45%)</title><rect x="552.9" y="85" width="29.0" height="15.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="555.94" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >al..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (70 samples, 2.86%)</title><rect x="466.1" y="53" width="33.8" height="15.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="469.07" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Da..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataProcessor (140 samples, 5.73%)</title><rect x="724.3" y="69" width="67.5" height="15.0" fill="rgb(124,124,242)" rx="2" ry="2" />
<text text-anchor="" x="727.27" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataPro..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>create_connection (45 samples, 1.84%)</title><rect x="101.7" y="101" width="21.7" height="15.0" fill="rgb(100,100,221)" rx="2" ry="2" />
<text text-anchor="" x="104.70" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >c..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>http_post (110 samples, 4.50%)</title><rect x="413.0" y="101" width="53.1" height="15.0" fill="rgb(107,107,228)" rx="2" ry="2" />
<text text-anchor="" x="415.99" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >http_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (40 samples, 1.64%)</title><rect x="1170.7" y="37" width="19.3" height="15.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="1173.70" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (20 samples, 0.82%)</title><rect x="927.0" y="37" width="9.6" height="15.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="929.97" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (45 samples, 1.84%)</title><rect x="101.7" y="37" width="21.7" height="15.0" fill="rgb(82,82,206)" rx="2" ry="2" />
<text text-anchor="" x="104.70" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (120 samples, 4.91%)</title><rect x="594.0" y="37" width="57.9" height="15.0" fill="rgb(128,128,245)" rx="2" ry="2" />
<text text-anchor="" x="596.97" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (35 samples, 1.43%)</title><rect x="536.1" y="53" width="16.8" height="15.0" fill="rgb(137,137,253)" rx="2" ry="2" />
<text text-anchor="" x="539.05" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>initialize (25 samples, 1.02%)</title><rect x="936.6" y="85" width="12.1" height="15.0" fill="rgb(81,81,206)" rx="2" ry="2" />
<text text-anchor="" x="939.63" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cleanData (160 samples, 6.54%)</title><rect x="10.0" y="101" width="77.2" height="15.0" fill="rgb(118,118,237)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cleanData</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>histogram_update (25 samples, 1.02%)</title><rect x="355.1" y="101" width="12.0" height="15.0" fill="rgb(134,134,250)" rx="2" ry="2" />
<text text-anchor="" x="358.07" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (40 samples, 1.64%)</title><rect x="236.8" y="37" width="19.3" height="15.0" fill="rgb(101,101,222)" rx="2" ry="2" />
<text text-anchor="" x="239.83" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>validateData (200 samples, 8.18%)</title><rect x="1052.5" y="101" width="96.5" height="15.0" fill="rgb(125,125,242)" rx="2" ry="2" />
<text text-anchor="" x="1055.45" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >validateData</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>set (55 samples, 2.25%)</title><rect x="900.4" y="85" width="26.6" height="15.0" fill="rgb(114,114,233)" rx="2" ry="2" />
<text text-anchor="" x="903.43" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >set</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (100 samples, 4.09%)</title><rect x="188.6" y="37" width="48.2" height="15.0" fill="rgb(104,104,225)" rx="2" ry="2" />
<text text-anchor="" x="191.57" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>all (2,445 samples, 100%)</title><rect x="10.0" y="117" width="1180.0" height="15.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="127.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>executeQuery (120 samples, 4.91%)</title><rect x="594.0" y="85" width="57.9" height="15.0" fill="rgb(100,100,222)" rx="2" ry="2" />
<text text-anchor="" x="596.97" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >execut..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (75 samples, 3.07%)</title><rect x="499.9" y="37" width="36.2" height="15.0" fill="rgb(127,127,244)" rx="2" ry="2" />
<text text-anchor="" x="502.86" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>parseJSON (75 samples, 3.07%)</title><rect x="499.9" y="85" width="36.2" height="15.0" fill="rgb(137,137,252)" rx="2" ry="2" />
<text text-anchor="" x="502.86" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >par..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>worker_thread (130 samples, 5.32%)</title><rect x="791.8" y="85" width="62.8" height="15.0" fill="rgb(105,105,225)" rx="2" ry="2" />
<text text-anchor="" x="794.84" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >worker..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (130 samples, 5.32%)</title><rect x="791.8" y="53" width="62.8" height="15.0" fill="rgb(96,96,219)" rx="2" ry="2" />
<text text-anchor="" x="794.84" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataSt..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>processBatch (140 samples, 5.73%)</title><rect x="724.3" y="101" width="67.5" height="15.0" fill="rgb(80,80,205)" rx="2" ry="2" />
<text text-anchor="" x="727.27" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >process..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (25 samples, 1.02%)</title><rect x="936.6" y="69" width="12.1" height="15.0" fill="rgb(84,84,208)" rx="2" ry="2" />
<text text-anchor="" x="939.63" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (20 samples, 0.82%)</title><rect x="1042.8" y="53" width="9.7" height="15.0" fill="rgb(113,113,232)" rx="2" ry="2" />
<text text-anchor="" x="1045.80" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>connection_pool (80 samples, 3.27%)</title><rect x="316.5" y="85" width="38.6" height="15.0" fill="rgb(92,92,215)" rx="2" ry="2" />
<text text-anchor="" x="319.46" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >con..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (55 samples, 2.25%)</title><rect x="900.4" y="37" width="26.6" height="15.0" fill="rgb(123,123,241)" rx="2" ry="2" />
<text text-anchor="" x="903.43" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >m..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_fetch_row (120 samples, 4.91%)</title><rect x="594.0" y="101" width="57.9" height="15.0" fill="rgb(139,139,254)" rx="2" ry="2" />
<text text-anchor="" x="596.97" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>invalidate (30 samples, 1.23%)</title><rect x="854.6" y="85" width="14.5" height="15.0" fill="rgb(112,112,231)" rx="2" ry="2" />
<text text-anchor="" x="857.58" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>connection_pool (45 samples, 1.84%)</title><rect x="101.7" y="85" width="21.7" height="15.0" fill="rgb(122,122,240)" rx="2" ry="2" />
<text text-anchor="" x="104.70" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >c..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>http_get (95 samples, 3.89%)</title><rect x="367.1" y="101" width="45.9" height="15.0" fill="rgb(89,89,213)" rx="2" ry="2" />
<text text-anchor="" x="370.14" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >http..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ThreadPool (25 samples, 1.02%)</title><rect x="581.9" y="69" width="12.1" height="15.0" fill="rgb(136,136,251)" rx="2" ry="2" />
<text text-anchor="" x="584.90" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ThreadPool (130 samples, 5.32%)</title><rect x="791.8" y="69" width="62.8" height="15.0" fill="rgb(89,89,213)" rx="2" ry="2" />
<text text-anchor="" x="794.84" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Thread..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>CacheManager (30 samples, 1.23%)</title><rect x="854.6" y="69" width="14.5" height="15.0" fill="rgb(109,109,229)" rx="2" ry="2" />
<text text-anchor="" x="857.58" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>initialize (15 samples, 0.61%)</title><rect x="948.7" y="85" width="7.2" height="15.0" fill="rgb(123,123,240)" rx="2" ry="2" />
<text text-anchor="" x="951.69" y="95.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (35 samples, 1.43%)</title><rect x="536.1" y="37" width="16.8" height="15.0" fill="rgb(84,84,208)" rx="2" ry="2" />
<text text-anchor="" x="539.05" y="47.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ThreadPool (45 samples, 1.84%)</title><rect x="1149.0" y="69" width="21.7" height="15.0" fill="rgb(114,114,233)" rx="2" ry="2" />
<text text-anchor="" x="1151.98" y="79.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >T..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>json_parser (75 samples, 3.07%)</title><rect x="499.9" y="101" width="36.2" height="15.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="502.86" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >jso..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fread (100 samples, 4.09%)</title><rect x="188.6" y="101" width="48.2" height="15.0" fill="rgb(103,103,224)" rx="2" ry="2" />
<text text-anchor="" x="191.57" y="111.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >fread</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (85 samples, 3.48%)</title><rect x="123.4" y="53" width="41.0" height="15.0" fill="rgb(91,91,214)" rx="2" ry="2" />
<text text-anchor="" x="126.42" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Dat..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (160 samples, 6.54%)</title><rect x="10.0" y="53" width="77.2" height="15.0" fill="rgb(117,117,235)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="63.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataStag..</text>
</g>
</svg>
