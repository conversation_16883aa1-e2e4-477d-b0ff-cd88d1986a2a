<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="4894" onload="init(evt)" viewBox="0 0 1200 4894" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details, searchbtn, matchedtxt, svg;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
	}

	// mouse-over for info
	function s(node) {		// show
		info = g_to_text(node);
		details.nodeValue = "Function: " + info;
	}
	function c() {			// clear
		details.nodeValue = ' ';
	}

	// ctrl-F for search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
	})

	// functions
	function find_child(parent, name, attr) {
		var children = parent.childNodes;
		for (var i=0; i<children.length;i++) {
			if (children[i].tagName == name)
				return (attr != undefined) ? children[i].attributes[attr].value : children[i];
		}
		return;
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_"+attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_"+attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_"+attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes["width"].value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes["x"].value = parseFloat(r.attributes["x"].value) +3;

		// Smaller than this size won't fit anything
		if (w < 2*12*0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		// Fit in full text width
		if (/^ *$/.test(txt) || t.getSubStringLength(0, txt.length) < w)
			return;

		for (var x=txt.length-2; x>0; x--) {
			if (t.getSubStringLength(0, x+2) <= w) {
				t.textContent = txt.substring(0,x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = (parseFloat(e.attributes["x"].value) - x - 10) * ratio + 10;
				if(e.tagName == "text") e.attributes["x"].value = find_child(e.parentNode, "rect", "x") + 3;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseFloat(e.attributes["width"].value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_child(c[i], x-10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes["x"] != undefined) {
				orig_save(e, "x");
				e.attributes["x"].value = 10;
			}
			if (e.attributes["width"] != undefined) {
				orig_save(e, "width");
				e.attributes["width"].value = parseInt(svg.width.baseVal.value) - (10*2);
			}
		}
		if (e.childNodes == undefined) return;
		for(var i=0, c=e.childNodes; i<c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr["width"].value);
		var xmin = parseFloat(attr["x"].value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr["y"].value);
		var ratio = (svg.width.baseVal.value - 2*10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "1.0";

		var el = document.getElementsByTagName("g");
		for(var i=0;i<el.length;i++){
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a["x"].value);
			var ew = parseFloat(a["width"].value);
			// Is it an ancestor
			if (0 == 0) {
				var upstack = parseFloat(a["y"].value) > ymin;
			} else {
				var upstack = parseFloat(a["y"].value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.style["opacity"] = "0.5";
					zoom_parent(e);
					e.onclick = function(e){unzoom(); zoom(this);};
					update_text(e);
				}
				// not in current path
				else
					e.style["display"] = "none";
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.style["display"] = "none";
				}
				else {
					zoom_child(e, xmin, ratio);
					e.onclick = function(e){zoom(this);};
					update_text(e);
				}
			}
		}
	}
	function unzoom() {
		var unzoombtn = document.getElementById("unzoom");
		unzoombtn.style["opacity"] = "0.0";

		var el = document.getElementsByTagName("g");
		for(i=0;i<el.length;i++) {
			el[i].style["display"] = "block";
			el[i].style["opacity"] = "1";
			zoom_reset(el[i]);
			update_text(el[i]);
		}
	}

	// search
	function reset_search() {
		var el = document.getElementsByTagName("rect");
		for (var i=0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)", "");
			if (term != null) {
				search(term)
			}
		} else {
			reset_search();
			searching = 0;
			searchbtn.style["opacity"] = "0.1";
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.style["opacity"] = "0.0";
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		var re = new RegExp(term);
		var el = document.getElementsByTagName("g");
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			if (e.attributes["class"].value != "func_g")
				continue;
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (rect == null) {
				// the rect might be wrapped in an anchor
				// if nameattr href is being used
				if (rect = find_child(e, "a")) {
				    rect = find_child(r, "rect");
				}
			}
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes["width"].value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes["x"].value);
				orig_save(rect, "fill");
				rect.attributes["fill"].value =
				    "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;

		searchbtn.style["opacity"] = "1.0";
		searchbtn.firstChild.nodeValue = "Reset Search"

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.style["opacity"] = "1.0";
		pct = 100 * count / maxwidth;
		if (pct == 100)
			pct = "100"
		else
			pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
	function searchover(e) {
		searchbtn.style["opacity"] = "1.0";
	}
	function searchout(e) {
		if (searching) {
			searchbtn.style["opacity"] = "1.0";
		} else {
			searchbtn.style["opacity"] = "0.1";
		}
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="4894.0" fill="url(#background)"  />
<text text-anchor="middle" x="600.00" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Blinkit Data Staging Performance Profile</text>
<text text-anchor="middle" x="600.00" y="48" font-size="12" font-family="Verdana" fill="rgb(160,160,160)"  >Generated from perf.data analysis</text>
<text text-anchor="" x="10.00" y="4877" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<text text-anchor="" x="10.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="unzoom" onclick="unzoom()" style="opacity:0.0;cursor:pointer" >Reset Zoom</text>
<text text-anchor="" x="1090.00" y="24" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="search" onmouseover="searchover()" onmouseout="searchout()" onclick="search_prompt()" style="opacity:0.1;cursor:pointer" >Search</text>
<text text-anchor="" x="1090.00" y="4877" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="matched" > </text>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mutex_lock (25 samples, 1.02%)</title><rect x="1074.2" y="61" width="12.0" height="799.0" fill="rgb(207,58,6)" rx="2" ry="2" />
<text text-anchor="" x="1077.17" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>executeQuery (270 samples, 11.04%)</title><rect x="632.6" y="861" width="130.3" height="799.0" fill="rgb(236,88,22)" rx="2" ry="2" />
<text text-anchor="" x="635.58" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >executeQuery</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>main (2,445 samples, 100.00%)</title><rect x="10.0" y="3261" width="1180.0" height="799.0" fill="rgb(233,214,25)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="3663.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >main</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>setup_database (25 samples, 1.02%)</title><rect x="1170.7" y="861" width="12.1" height="799.0" fill="rgb(252,181,22)" rx="2" ry="2" />
<text text-anchor="" x="1173.70" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>APIClient (275 samples, 11.25%)</title><rect x="10.0" y="1661" width="132.7" height="799.0" fill="rgb(231,189,54)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >APIClient</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>log_error (35 samples, 1.43%)</title><rect x="762.9" y="61" width="16.9" height="799.0" fill="rgb(214,206,22)" rx="2" ry="2" />
<text text-anchor="" x="765.88" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>initialize (40 samples, 1.64%)</title><rect x="1170.7" y="1661" width="19.3" height="799.0" fill="rgb(226,217,20)" rx="2" ry="2" />
<text text-anchor="" x="1173.70" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>recordMetric (55 samples, 2.25%)</title><rect x="1047.6" y="861" width="26.6" height="799.0" fill="rgb(212,201,3)" rx="2" ry="2" />
<text text-anchor="" x="1050.63" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >r..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>setup_logging (15 samples, 0.61%)</title><rect x="1182.8" y="861" width="7.2" height="799.0" fill="rgb(211,93,31)" rx="2" ry="2" />
<text text-anchor="" x="1185.76" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>json_parser (75 samples, 3.07%)</title><rect x="830.4" y="61" width="36.2" height="799.0" fill="rgb(212,188,41)" rx="2" ry="2" />
<text text-anchor="" x="833.45" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >jso..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fwrite (90 samples, 3.68%)</title><rect x="914.9" y="61" width="43.4" height="799.0" fill="rgb(229,69,30)" rx="2" ry="2" />
<text text-anchor="" x="917.91" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >fwrite</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>processBatch (140 samples, 5.73%)</title><rect x="244.1" y="61" width="67.5" height="799.0" fill="rgb(210,125,4)" rx="2" ry="2" />
<text text-anchor="" x="247.07" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >process..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>garbage_collect (35 samples, 1.43%)</title><rect x="1030.7" y="861" width="16.9" height="799.0" fill="rgb(216,151,9)" rx="2" ry="2" />
<text text-anchor="" x="1033.74" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>processRecords (540 samples, 22.09%)</title><rect x="311.6" y="861" width="260.6" height="799.0" fill="rgb(228,38,36)" rx="2" ry="2" />
<text text-anchor="" x="314.64" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >processRecords</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>transformData (180 samples, 7.36%)</title><rect x="388.9" y="61" width="86.8" height="799.0" fill="rgb(254,46,48)" rx="2" ry="2" />
<text text-anchor="" x="391.85" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >transformD..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>FileHandler (350 samples, 14.31%)</title><rect x="789.4" y="1661" width="168.9" height="799.0" fill="rgb(206,69,24)" rx="2" ry="2" />
<text text-anchor="" x="792.43" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >FileHandler</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>allocate (60 samples, 2.45%)</title><rect x="982.5" y="861" width="28.9" height="799.0" fill="rgb(232,217,44)" rx="2" ry="2" />
<text text-anchor="" x="985.47" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >al..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>fread (100 samples, 4.09%)</title><rect x="866.6" y="61" width="48.3" height="799.0" fill="rgb(216,202,13)" rx="2" ry="2" />
<text text-anchor="" x="869.65" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >fread</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>CacheManager (150 samples, 6.13%)</title><rect x="142.7" y="1661" width="72.4" height="799.0" fill="rgb(232,190,48)" rx="2" ry="2" />
<text text-anchor="" x="145.72" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >CacheMan..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_query (150 samples, 6.13%)</title><rect x="690.5" y="61" width="72.4" height="799.0" fill="rgb(224,161,35)" rx="2" ry="2" />
<text text-anchor="" x="693.49" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql_qu..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>set (55 samples, 2.25%)</title><rect x="188.6" y="861" width="26.5" height="799.0" fill="rgb(237,142,47)" rx="2" ry="2" />
<text text-anchor="" x="191.57" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >set</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>get (65 samples, 2.66%)</title><rect x="142.7" y="861" width="31.4" height="799.0" fill="rgb(211,205,15)" rx="2" ry="2" />
<text text-anchor="" x="145.72" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >get</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ThreadPool (200 samples, 8.18%)</title><rect x="1074.2" y="1661" width="96.5" height="799.0" fill="rgb(249,56,24)" rx="2" ry="2" />
<text text-anchor="" x="1077.17" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ThreadPool</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>get_connection (80 samples, 3.27%)</title><rect x="594.0" y="61" width="38.6" height="799.0" fill="rgb(245,131,33)" rx="2" ry="2" />
<text text-anchor="" x="596.97" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >get..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>synchronize (25 samples, 1.02%)</title><rect x="1074.2" y="861" width="12.0" height="799.0" fill="rgb(239,180,42)" rx="2" ry="2" />
<text text-anchor="" x="1077.17" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ConfigManager (60 samples, 2.45%)</title><rect x="215.1" y="1661" width="29.0" height="799.0" fill="rgb(217,158,17)" rx="2" ry="2" />
<text text-anchor="" x="218.11" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >Co..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>parseCSV (85 samples, 3.48%)</title><rect x="789.4" y="861" width="41.0" height="799.0" fill="rgb(206,35,47)" rx="2" ry="2" />
<text text-anchor="" x="792.43" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >par..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>process_task (130 samples, 5.32%)</title><rect x="1086.2" y="61" width="62.8" height="799.0" fill="rgb(235,48,44)" rx="2" ry="2" />
<text text-anchor="" x="1089.24" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >proces..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>create_connection (45 samples, 1.84%)</title><rect x="572.2" y="61" width="21.8" height="799.0" fill="rgb(226,170,23)" rx="2" ry="2" />
<text text-anchor="" x="575.25" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >c..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataProcessor (680 samples, 27.81%)</title><rect x="244.1" y="1661" width="328.1" height="799.0" fill="rgb(221,133,30)" rx="2" ry="2" />
<text text-anchor="" x="247.07" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataProcessor</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>all (2,445 samples, 100%)</title><rect x="10.0" y="4061" width="1180.0" height="799.0" fill="rgb(218,61,0)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="4463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>deallocate (40 samples, 1.64%)</title><rect x="1011.4" y="861" width="19.3" height="799.0" fill="rgb(230,100,45)" rx="2" ry="2" />
<text text-anchor="" x="1014.43" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handleResponse (70 samples, 2.86%)</title><rect x="10.0" y="861" width="33.8" height="799.0" fill="rgb(229,162,16)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ha..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>validateData (200 samples, 8.18%)</title><rect x="475.7" y="61" width="96.5" height="799.0" fill="rgb(254,169,50)" rx="2" ry="2" />
<text text-anchor="" x="478.73" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >validateData</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>sendRequest (205 samples, 8.38%)</title><rect x="43.8" y="861" width="98.9" height="799.0" fill="rgb(224,110,21)" rx="2" ry="2" />
<text text-anchor="" x="46.78" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >sendRequest</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>readFile (100 samples, 4.09%)</title><rect x="866.6" y="861" width="48.3" height="799.0" fill="rgb(234,128,13)" rx="2" ry="2" />
<text text-anchor="" x="869.65" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >read..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>counter_increment (30 samples, 1.23%)</title><rect x="1047.6" y="61" width="14.5" height="799.0" fill="rgb(220,222,23)" rx="2" ry="2" />
<text text-anchor="" x="1050.63" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>validateConfig (20 samples, 0.82%)</title><rect x="234.4" y="861" width="9.7" height="799.0" fill="rgb(216,82,41)" rx="2" ry="2" />
<text text-anchor="" x="237.42" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>writeLog (50 samples, 2.04%)</title><rect x="958.3" y="861" width="24.2" height="799.0" fill="rgb(243,77,44)" rx="2" ry="2" />
<text text-anchor="" x="961.34" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>invalidate (30 samples, 1.23%)</title><rect x="174.1" y="861" width="14.5" height="799.0" fill="rgb(244,84,48)" rx="2" ry="2" />
<text text-anchor="" x="177.09" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DataStagingApp (2,445 samples, 100.00%)</title><rect x="10.0" y="2461" width="1180.0" height="799.0" fill="rgb(233,175,11)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="2863.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DataStagingApp</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>histogram_update (25 samples, 1.02%)</title><rect x="1062.1" y="61" width="12.1" height="799.0" fill="rgb(230,64,28)" rx="2" ry="2" />
<text text-anchor="" x="1065.11" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>connection_pool (125 samples, 5.11%)</title><rect x="572.2" y="861" width="60.4" height="799.0" fill="rgb(217,72,41)" rx="2" ry="2" />
<text text-anchor="" x="575.25" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >connec..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>ErrorHandler (55 samples, 2.25%)</title><rect x="762.9" y="1661" width="26.5" height="799.0" fill="rgb(222,226,25)" rx="2" ry="2" />
<text text-anchor="" x="765.88" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >E..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>DatabaseConnector (395 samples, 16.16%)</title><rect x="572.2" y="1661" width="190.7" height="799.0" fill="rgb(219,180,29)" rx="2" ry="2" />
<text text-anchor="" x="575.25" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >DatabaseConnector</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>batchProcessor (140 samples, 5.73%)</title><rect x="244.1" y="861" width="67.5" height="799.0" fill="rgb(254,157,18)" rx="2" ry="2" />
<text text-anchor="" x="247.07" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >batchPr..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>file_write (50 samples, 2.04%)</title><rect x="958.3" y="61" width="24.2" height="799.0" fill="rgb(231,88,47)" rx="2" ry="2" />
<text text-anchor="" x="961.34" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >f..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>worker_thread (175 samples, 7.16%)</title><rect x="1086.2" y="861" width="84.5" height="799.0" fill="rgb(221,148,4)" rx="2" ry="2" />
<text text-anchor="" x="1089.24" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >worker_th..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>yaml_parse (40 samples, 1.64%)</title><rect x="215.1" y="61" width="19.3" height="799.0" fill="rgb(220,140,10)" rx="2" ry="2" />
<text text-anchor="" x="218.11" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>http_get (95 samples, 3.89%)</title><rect x="43.8" y="61" width="45.8" height="799.0" fill="rgb(217,119,6)" rx="2" ry="2" />
<text text-anchor="" x="46.78" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >http..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>redis_set (55 samples, 2.25%)</title><rect x="188.6" y="61" width="26.5" height="799.0" fill="rgb(221,43,12)" rx="2" ry="2" />
<text text-anchor="" x="191.57" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >r..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>redis_get (65 samples, 2.66%)</title><rect x="142.7" y="61" width="31.4" height="799.0" fill="rgb(223,211,53)" rx="2" ry="2" />
<text text-anchor="" x="145.72" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >re..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>mysql_fetch_row (120 samples, 4.91%)</title><rect x="632.6" y="61" width="57.9" height="799.0" fill="rgb(232,88,48)" rx="2" ry="2" />
<text text-anchor="" x="635.58" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Metrics (55 samples, 2.25%)</title><rect x="1047.6" y="1661" width="26.6" height="799.0" fill="rgb(249,191,37)" rx="2" ry="2" />
<text text-anchor="" x="1050.63" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >M..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>cleanData (160 samples, 6.54%)</title><rect x="311.6" y="61" width="77.3" height="799.0" fill="rgb(250,23,33)" rx="2" ry="2" />
<text text-anchor="" x="314.64" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >cleanData</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>Logger (50 samples, 2.04%)</title><rect x="958.3" y="1661" width="24.2" height="799.0" fill="rgb(208,134,7)" rx="2" ry="2" />
<text text-anchor="" x="961.34" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >L..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>MemoryManager (135 samples, 5.52%)</title><rect x="982.5" y="1661" width="65.1" height="799.0" fill="rgb(212,96,42)" rx="2" ry="2" />
<text text-anchor="" x="985.47" y="2063.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >MemoryM..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>csv_parser (85 samples, 3.48%)</title><rect x="789.4" y="61" width="41.0" height="799.0" fill="rgb(247,68,36)" rx="2" ry="2" />
<text text-anchor="" x="792.43" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >csv..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>parseJSON (75 samples, 3.07%)</title><rect x="830.4" y="861" width="36.2" height="799.0" fill="rgb(253,141,0)" rx="2" ry="2" />
<text text-anchor="" x="833.45" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >par..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>wait_for_task (45 samples, 1.84%)</title><rect x="1149.0" y="61" width="21.7" height="799.0" fill="rgb(218,71,4)" rx="2" ry="2" />
<text text-anchor="" x="1151.98" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >w..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>json_decode (70 samples, 2.86%)</title><rect x="10.0" y="61" width="33.8" height="799.0" fill="rgb(245,209,41)" rx="2" ry="2" />
<text text-anchor="" x="13.00" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >js..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>redis_del (30 samples, 1.23%)</title><rect x="174.1" y="61" width="14.5" height="799.0" fill="rgb(254,196,4)" rx="2" ry="2" />
<text text-anchor="" x="177.09" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>http_post (110 samples, 4.50%)</title><rect x="89.6" y="61" width="53.1" height="799.0" fill="rgb(248,86,18)" rx="2" ry="2" />
<text text-anchor="" x="92.63" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >http_..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>send_alert (20 samples, 0.82%)</title><rect x="779.8" y="61" width="9.6" height="799.0" fill="rgb(233,201,42)" rx="2" ry="2" />
<text text-anchor="" x="782.78" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>writeFile (90 samples, 3.68%)</title><rect x="914.9" y="861" width="43.4" height="799.0" fill="rgb(205,179,21)" rx="2" ry="2" />
<text text-anchor="" x="917.91" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >writ..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>handleException (55 samples, 2.25%)</title><rect x="762.9" y="861" width="26.5" height="799.0" fill="rgb(208,18,15)" rx="2" ry="2" />
<text text-anchor="" x="765.88" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >h..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>malloc (60 samples, 2.45%)</title><rect x="982.5" y="61" width="28.9" height="799.0" fill="rgb(219,205,8)" rx="2" ry="2" />
<text text-anchor="" x="985.47" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >ma..</text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>loadConfig (40 samples, 1.64%)</title><rect x="215.1" y="861" width="19.3" height="799.0" fill="rgb(216,69,41)" rx="2" ry="2" />
<text text-anchor="" x="218.11" y="1263.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s(this)" onmouseout="c()" onclick="zoom(this)">
<title>free (40 samples, 1.64%)</title><rect x="1011.4" y="61" width="19.3" height="799.0" fill="rgb(242,157,54)" rx="2" ry="2" />
<text text-anchor="" x="1014.43" y="463.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
</svg>
