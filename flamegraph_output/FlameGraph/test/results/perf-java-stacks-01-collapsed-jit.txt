ab;[unknown];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
ab;[unknown];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;local_bh_enable;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;__kfree_skb 1
ab;[unknown];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__kmalloc_reserve.isra.26 1
ab;[unknown];__write_nocancel;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_current_mss;tcp_v4_md5_lookup 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/buffer/AbstractByteBuf:.writeBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/buffer/AbstractByteBuf:.writeBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];java/lang/Thread:.blockedOn_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadHandler:.flush_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];pthread_self 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadHandler:.flush_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];[libpthread-2.19.so];system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;local_bh_enable;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_data_queue;sock_def_readable;__wake_up_sync_key;__wake_up_common;ep_poll_callback;__wake_up_locked;__wake_up_common;default_wake_function;try_to_wake_up;_raw_spin_unlock_irqrestore 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/DefaultChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadHandler:.flush_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.getBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];[libpthread-2.19.so];system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;tcp_v4_send_check;__tcp_v4_send_check 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/WrapFactory:.wrap_[j];call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:._c_anonymous_3_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_80:._c_anonymous_21_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_31:.call_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3_[j];org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:._c_anonymous_3_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_4:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_4:._c_anonymous_1_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_90:.call_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];java/lang/reflect/Method:.invoke_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_21_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_49:.call_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_49:.call_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:._c_anonymous_3_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_bench_Server_js_js_2:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_91:.call_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];java/lang/reflect/Method:.invoke_[j];io/netty/handler/codec/http/DefaultHttpHeaders:.add0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_21_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.getParamAndVarCount_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.<init>_[j];org/mozilla/javascript/NativeFunction:.initScriptFunction_[j];org/mozilla/javascript/TopLevel:.getBuiltinPrototype_[j];call_function_single_interrupt;smp_call_function_single_interrupt;generic_smp_call_function_single_interrupt;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.getParamCount_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/vertx/java/core/http/impl/ServerConnection:.handleMessage_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/BaseFunction:.construct_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_http_js_93:._c_anonymous_3_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_vert_x_2_1_sys_mods_io_vertx_lang_js_1_1_0_vertx_streams_js_47:.call_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpMethod:.valueOf_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/DefaultChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.select_[j];sun/nio/ch/SelectorImpl:.lockAndDoSelect_[j];sun/nio/ch/EPollSelectorImpl:.doSelect_[j];sun/nio/ch/EPollArrayWrapper:.poll_[j];sun/nio/ch/EPollArrayWrapper:.epollWait_[j];__libc_enable_asynccancel 1
perf;__libc_start_main;[perf];[perf];[perf];__GI___ioctl;system_call_fastpath;sys_ioctl;do_vfs_ioctl;perf_ioctl;perf_event_for_each_child;perf_event_enable;cpu_function_call;smp_call_function_single;remote_function;__perf_event_enable;group_sched_in;x86_pmu_commit_txn;perf_pmu_enable;x86_pmu_enable;intel_pmu_enable_all;native_write_msr_safe 4
perf;__libc_start_main;[perf];[perf];[perf];page_fault 1
swapper;x86_64_start_kernel;x86_64_start_reservations;start_kernel;rest_init;cpu_startup_entry;rcu_idle_enter 1
