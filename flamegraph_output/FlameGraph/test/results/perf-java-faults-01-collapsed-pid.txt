java-?;_int_malloc 2
java-?;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub;Ljava/lang/Thread:::run;Interpreter;Ljava/util/concurrent/ThreadPoolExecutor$Worker:::run;Ljava/util/concurrent/ThreadPoolExecutor:::runWorker;Lorg/apache/tomcat/util/net/AprEndpoint$SocketProcessor:::run;Lorg/apache/tomcat/util/net/AprEndpoint$SocketProcessor:::doRun;Lorg/apache/coyote/AbstractProtocol$AbstractConnectionHandler:::process;Lorg/apache/coyote/http11/AbstractHttp11Processor:::process;Lorg/apache/catalina/connector/CoyoteAdapter:::service;Lorg/apache/coyote/http11/AbstractHttp11Processor:::action;Lorg/apache/tomcat/jni/Socket:::sendbb 1
java-?;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub;Lnet/spy/memcached/EVCacheConnection:::run;Lnet/spy/memcached/MemcachedConnection:::handleIO;Lnet/spy/memcached/MemcachedConnection:::handleIO;Lnet/spy/memcached/MemcachedConnection:::handleReads;Lnet/spy/memcached/protocol/binary/OperationImpl:::readFromBuffer;Lnet/spy/memcached/protocol/binary/OperationImpl:::finishedPayload;Lnet/spy/memcached/protocol/binary/GetOperationImpl:::decodePayload;Lnet/spy/memcached/transcoders/TranscodeService$1:::call;Lcom/XXX::XXX;Ljava/util/zip/Inflater:::inflateBytes;Java_java_util_zip_Inflater_inflateBytes;inflate;__memmove_ssse3_back 1
java-?;start_thread;_ZL10java_startP6Thread;_ZN10JavaThread3runEv;_ZN10JavaThread17thread_main_innerEv;_ZL12thread_entryP10JavaThreadP6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue6Handle11KlassHandleP6SymbolS5_P6Thread;_ZN9JavaCalls12call_virtualEP9JavaValue11KlassHandleP6SymbolS4_P17JavaCallArgumentsP6Thread;_ZN9JavaCalls11call_helperEP9JavaValueP12methodHandleP17JavaCallArgumentsP6Thread;call_stub;Lnet/spy/memcached/EVCacheConnection:::run;Lnet/spy/memcached/MemcachedConnection:::handleIO;Lnet/spy/memcached/MemcachedConnection:::handleIO;Lnet/spy/memcached/MemcachedConnection:::handleReads;Lnet/spy/memcached/protocol/binary/OperationImpl:::readFromBuffer;Lnet/spy/memcached/protocol/binary/OperationImpl:::finishedPayload;Lnet/spy/memcached/protocol/binary/GetOperationImpl:::decodePayload;Lnet/spy/memcached/transcoders/TranscodeService$1:::call;XXX::XXX;Ljava/util/zip/Inflater:::inflateBytes;Java_java_util_zip_Inflater_inflateBytes;inflate;__memmove_ssse3_back 18
perf-?;__libc_start_main;main;run_builtin;cmd_record 40
perf-?;do_lookup_x 27
sleep-?;[unknown];memcmp 24
sleep-?;__execve;return_from_execve;sys_execve;do_execveat_common.isra.31;search_binary_handler;copy_user_enhanced_fast_string 1
sleep-?;__execve;return_from_execve;sys_execve;do_execveat_common.isra.31;search_binary_handler;load_elf_binary;padzero;clear_user;__clear_user 2
sleep-?;_dl_start_user;_dl_start 9
sleep-?;_start 3
sleep-?;handle_intel 72
