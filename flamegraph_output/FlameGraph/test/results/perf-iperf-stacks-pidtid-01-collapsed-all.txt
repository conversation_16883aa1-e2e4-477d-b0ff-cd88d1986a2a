iperf;[unknown];[iperf] 1
iperf;[unknown];[iperf];__vdso_gettimeofday_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];__fdget_pos_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];copy_from_iter_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];release_sock_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_push_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__alloc_skb_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];__raw_callee_save___pv_queued_spin_unlock_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];copy_user_enhanced_fast_string_[k] 24
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_page_frag_refill_[k];skb_page_frag_refill_[k];alloc_pages_current_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_page_frag_refill_[k];skb_page_frag_refill_[k];alloc_pages_current_[k];__alloc_pages_nodemask_[k];get_page_from_freelist_[k];__zone_watermark_ok_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_page_frag_refill_[k];skb_page_frag_refill_[k];alloc_pages_current_[k];policy_zonelist_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_alloc_skb_[k];__alloc_skb_[k];kmem_cache_alloc_node_[k];_cond_resched_[k];preempt_schedule_common_[k];__schedule_[k];check_events_[k];xen_hypercall_xen_version_[k] 3
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_wait_memory_[k];finish_wait_[k];_raw_spin_lock_irqsave_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];sk_stream_wait_memory_[k];schedule_timeout_[k];schedule_[k];__schedule_[k];check_events_[k];xen_hypercall_xen_version_[k] 18
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];sk_reset_timer_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k] 2
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_prequeue_[k];__wake_up_sync_key_[k];check_events_[k];xen_hypercall_xen_version_[k] 8
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_prequeue_[k];_raw_spin_lock_irqsave_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];raw_local_deliver_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];net_rx_action_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];netif_rx_[k];ktime_get_with_offset_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];sk_free_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];tcp_wfree_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];do_softirq_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_[k];__tcp_push_pending_frames_[k];tcp_write_xmit_[k];tcp_v4_send_check_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];ip_queue_xmit_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_schedule_loss_probe_[k];sk_reset_timer_[k];mod_timer_[k];_raw_spin_lock_irqsave_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];__ip_local_out_sk_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];__sk_dst_check_[k];ipv4_dst_check_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];check_events_[k];xen_hypercall_xen_version_[k] 2
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];__inet_lookup_established_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];dst_release_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_prequeue_[k];__wake_up_sync_key_[k];check_events_[k];xen_hypercall_xen_version_[k] 2
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];sk_free_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];loopback_xmit_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];validate_xmit_skb.isra.102.part.103_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];skb_clone_[k];__skb_clone_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];skb_clone_[k];__skb_clone_[k];__copy_skb_header_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_push_one_[k];tcp_write_xmit_[k];tcp_transmit_skb_[k];tcp_v4_md5_lookup_[k] 1
iperf;[unknown];[libpthread-2.19.so];entry_SYSCALL_64_fastpath_[k];sys_write_[k];vfs_write_[k];__vfs_write_[k];sock_write_iter_[k];sock_sendmsg_[k];inet_sendmsg_[k];tcp_sendmsg_[k];tcp_send_mss_[k];tcp_current_mss_[k];tcp_established_options_[k];tcp_md5_do_lookup_[k] 1
iperf;[unknown];[libpthread-2.19.so];int_ret_from_sys_call_[k];syscall_return_slowpath_[k];prepare_exit_to_usermode_[k];schedule_[k];__schedule_[k];check_events_[k];xen_hypercall_xen_version_[k] 3
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];_raw_spin_lock_bh_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];sk_wait_data_[k];schedule_timeout_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];sk_wait_data_[k];schedule_timeout_[k];schedule_[k];__schedule_[k];check_events_[k];xen_hypercall_xen_version_[k] 14
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];__kfree_skb_[k];skb_release_all_[k];skb_release_data_[k];put_page_[k];put_compound_page_[k];__put_compound_page_[k];free_compound_page_[k];check_events_[k];xen_hypercall_xen_version_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_ack_[k];tcp_rearm_rto_[k];sk_reset_timer_[k];mod_timer_[k];check_events_[k];xen_hypercall_xen_version_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_check_space_[k];sk_stream_write_space_[k];__wake_up_[k];check_events_[k];xen_hypercall_xen_version_[k] 3
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];ipv4_dst_check_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];sock_def_readable_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_event_data_recv_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];check_events_[k];xen_hypercall_xen_version_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];__memmove_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];sock_put_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];tcp_v4_rcv_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_check_space_[k];sk_stream_write_space_[k];__wake_up_[k];check_events_[k];xen_hypercall_xen_version_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];tcp_send_ack_[k];__alloc_skb_[k];__kmalloc_reserve.isra.32_[k];kmalloc_slab_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];tcp_send_ack_[k];__kmalloc_reserve.isra.32_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];tcp_send_ack_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];netif_rx_[k];ktime_get_with_offset_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];tcp_send_ack_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];dev_hard_start_xmit_[k];loopback_xmit_[k];netif_rx_[k];netif_rx_internal_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];tcp_send_ack_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];dev_hard_start_xmit_[k];netif_rx_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];kfree_skb_partial_[k];skb_release_head_state_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];kfree_skbmem_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];skb_copy_datagram_iter_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];skb_copy_datagram_iter_[k];copy_user_enhanced_fast_string_[k] 8
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_event_data_recv_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];tcp_grow_window.isra.27_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_space_adjust_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];tcp_recvmsg_[k] 1
iperf;[unknown];[unknown];__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sockfd_lookup_light_[k];__fget_light_[k] 2
iperf;__libc_recv 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];sk_wait_data_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];sk_wait_data_[k];finish_wait_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];sk_wait_data_[k];schedule_timeout_[k];schedule_[k];__schedule_[k];check_events_[k];xen_hypercall_xen_version_[k] 5
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_cleanup_rbuf_[k];tcp_send_ack_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k];ip_local_deliver_[k];ip_local_deliver_finish_[k];__inet_lookup_established_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];__local_bh_enable_ip_[k];do_softirq_[k];do_softirq_own_stack_[k];__do_softirq_[k];net_rx_action_[k];process_backlog_[k];__netif_receive_skb_[k];__netif_receive_skb_core_[k];ip_rcv_[k];ip_rcv_finish_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];__alloc_skb_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];tcp_send_ack_[k];tcp_transmit_skb_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];tcp_send_ack_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];__tcp_ack_snd_check_[k];tcp_send_ack_[k];tcp_transmit_skb_[k];ip_queue_xmit_[k];ip_local_out_sk_[k];ip_output_[k];ip_finish_output_[k];ip_finish_output2_[k];dev_queue_xmit_sk_[k];__dev_queue_xmit_[k];dev_hard_start_xmit_[k] 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_prequeue_process_[k];tcp_v4_do_rcv_[k];tcp_rcv_established_[k];skb_copy_datagram_iter_[k];copy_user_enhanced_fast_string_[k] 12
iperf;__libc_recv;entry_SYSCALL_64_fastpath_[k];sys_recvfrom_[k];SYSC_recvfrom_[k];sock_recvmsg_[k];inet_recvmsg_[k];tcp_recvmsg_[k];tcp_release_cb_[k] 1
iperf;__pthread_disable_asynccancel 1
iperf;check_events_[k];xen_hypercall_xen_version_[k] 3
iperf;xen_irq_enable_direct_end_[k];check_events_[k];xen_hypercall_xen_version_[k] 1
multilog;_dl_sysdep_start;dl_main;_dl_relocate_object;page_fault_[k];do_page_fault_[k];check_events_[k];xen_hypercall_xen_version_[k] 1
run;[unknown];__GI___strncmp_ssse3;page_fault_[k];do_page_fault_[k];__do_page_fault_[k];handle_mm_fault_[k];filemap_map_pages_[k];do_set_pte_[k];xen_hypercall_mmu_update_[k] 1
run;__execve;return_from_execve_[k];sys_execve_[k];do_execveat_common.isra.31_[k];search_binary_handler_[k];load_script_[k];search_binary_handler_[k];load_elf_binary_[k];setup_arg_pages_[k];shift_arg_pages_[k];tlb_finish_mmu_[k];tlb_flush_mmu_free_[k];free_pages_and_swap_cache_[k];release_pages_[k];free_hot_cold_page_list_[k] 1
