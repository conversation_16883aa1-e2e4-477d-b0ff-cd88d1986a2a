iperf;[unknown <0>];[unknown <7f258c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 1
iperf;[unknown <0>];[unknown <7f258c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;sk_wait_data;schedule_timeout;schedule;__schedule;check_events;xen_hypercall_xen_version 1
iperf;[unknown <0>];[unknown <7f258c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_event_data_recv 1
iperf;[unknown <0>];[unknown <7f258c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;skb_copy_datagram_iter;copy_user_enhanced_fast_string 2
iperf;[unknown <0>];[unknown <7f258c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_space_adjust 1
iperf;[unknown <0>];[unknown <7f258c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;tcp_recvmsg 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;_raw_spin_lock_bh 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;sk_wait_data;schedule_timeout 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;sk_wait_data;schedule_timeout;schedule;__schedule;check_events;xen_hypercall_xen_version 2
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_rearm_rto;sk_reset_timer;mod_timer;check_events;xen_hypercall_xen_version 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;ipv4_dst_check 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;check_events;xen_hypercall_xen_version 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;tcp_send_ack;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;dev_hard_start_xmit;netif_rx 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;kfree_skb_partial;skb_release_head_state 1
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;skb_copy_datagram_iter;copy_user_enhanced_fast_string 2
iperf;[unknown <0>];[unknown <7f25900008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sockfd_lookup_light;__fget_light 1
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;sk_wait_data;schedule_timeout;schedule;__schedule;check_events;xen_hypercall_xen_version 7
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;__kfree_skb;skb_release_all;skb_release_data;put_page;put_compound_page;__put_compound_page;free_compound_page;check_events;xen_hypercall_xen_version 1
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_check_space;sk_stream_write_space;__wake_up;check_events;xen_hypercall_xen_version 2
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;sock_def_readable 1
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action 1
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;__memmove 1
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;tcp_send_ack;__kmalloc_reserve.isra.32 1
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;kfree_skbmem 1
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;skb_copy_datagram_iter;copy_user_enhanced_fast_string 3
iperf;[unknown <0>];[unknown <7f25980008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;tcp_grow_window.isra.27 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;sk_wait_data;schedule_timeout;schedule;__schedule;check_events;xen_hypercall_xen_version 4
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_check_space;sk_stream_write_space;__wake_up;check_events;xen_hypercall_xen_version 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;sock_put 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_check_space;sk_stream_write_space;__wake_up;check_events;xen_hypercall_xen_version 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;tcp_send_ack;__alloc_skb;__kmalloc_reserve.isra.32;kmalloc_slab 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;tcp_send_ack;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;netif_rx;ktime_get_with_offset 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;tcp_send_ack;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;netif_rx;netif_rx_internal 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;skb_copy_datagram_iter 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;skb_copy_datagram_iter;copy_user_enhanced_fast_string 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;tcp_event_data_recv 1
iperf;[unknown <0>];[unknown <7f259c0008f0>];__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sockfd_lookup_light;__fget_light 1
iperf;[unknown <20000>];[iperf <2c5f>];__vdso_gettimeofday 1
iperf;[unknown <20000>];[iperf <2d13>] 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;__fdget_pos 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;copy_from_iter 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;release_sock 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_push 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;__alloc_skb 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;__raw_callee_save___pv_queued_spin_unlock 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;copy_user_enhanced_fast_string 24
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;sk_page_frag_refill;skb_page_frag_refill;alloc_pages_current 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;sk_page_frag_refill;skb_page_frag_refill;alloc_pages_current;__alloc_pages_nodemask;get_page_from_freelist;__zone_watermark_ok 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;sk_page_frag_refill;skb_page_frag_refill;alloc_pages_current;policy_zonelist 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;kmem_cache_alloc_node;_cond_resched;preempt_schedule_common;__schedule;check_events;xen_hypercall_xen_version 3
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;sk_stream_wait_memory;finish_wait;_raw_spin_lock_irqsave 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;sk_stream_wait_memory;schedule_timeout;schedule;__schedule;check_events;xen_hypercall_xen_version 18
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;sk_reset_timer 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv 2
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_prequeue;__wake_up_sync_key;check_events;xen_hypercall_xen_version 8
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_prequeue;_raw_spin_lock_irqsave 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;raw_local_deliver 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;net_rx_action 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;netif_rx;ktime_get_with_offset 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;sk_free 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;tcp_wfree 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;do_softirq 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push;__tcp_push_pending_frames;tcp_write_xmit;tcp_v4_send_check 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;ip_queue_xmit 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_schedule_loss_probe;sk_reset_timer;mod_timer;_raw_spin_lock_irqsave 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;__ip_local_out_sk 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;__sk_dst_check;ipv4_dst_check 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;check_events;xen_hypercall_xen_version 2
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;__inet_lookup_established 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;dst_release 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_prequeue;__wake_up_sync_key;check_events;xen_hypercall_xen_version 2
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;sk_free 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;loopback_xmit 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;validate_xmit_skb.isra.102.part.103 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;skb_clone;__skb_clone 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;skb_clone;__skb_clone;__copy_skb_header 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_push_one;tcp_write_xmit;tcp_transmit_skb;tcp_v4_md5_lookup 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];entry_SYSCALL_64_fastpath;sys_write;vfs_write;__vfs_write;sock_write_iter;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_current_mss;tcp_established_options;tcp_md5_do_lookup 1
iperf;[unknown <20000>];[libpthread-2.19.so <f35d>];int_ret_from_sys_call;syscall_return_slowpath;prepare_exit_to_usermode;schedule;__schedule;check_events;xen_hypercall_xen_version 3
iperf;__libc_recv 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;sk_wait_data 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;sk_wait_data;finish_wait 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;sk_wait_data;schedule_timeout;schedule;__schedule;check_events;xen_hypercall_xen_version 5
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_send_ack;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;__inet_lookup_established 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;__local_bh_enable_ip;do_softirq;do_softirq_own_stack;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;__alloc_skb 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;tcp_send_ack;tcp_transmit_skb 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;tcp_send_ack;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;__tcp_ack_snd_check;tcp_send_ack;tcp_transmit_skb;ip_queue_xmit;ip_local_out_sk;ip_output;ip_finish_output;ip_finish_output2;dev_queue_xmit_sk;__dev_queue_xmit;dev_hard_start_xmit 1
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_prequeue_process;tcp_v4_do_rcv;tcp_rcv_established;skb_copy_datagram_iter;copy_user_enhanced_fast_string 12
iperf;__libc_recv;entry_SYSCALL_64_fastpath;sys_recvfrom;SYSC_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_release_cb 1
iperf;__pthread_disable_asynccancel 1
iperf;check_events;xen_hypercall_xen_version 3
iperf;xen_irq_enable_direct_end;check_events;xen_hypercall_xen_version 1
multilog;_dl_sysdep_start;dl_main;_dl_relocate_object;page_fault;do_page_fault;check_events;xen_hypercall_xen_version 1
run;[unknown <752f3a646e616d6d>];__GI___strncmp_ssse3;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;filemap_map_pages;do_set_pte;xen_hypercall_mmu_update 1
run;__execve;return_from_execve;sys_execve;do_execveat_common.isra.31;search_binary_handler;load_script;search_binary_handler;load_elf_binary;setup_arg_pages;shift_arg_pages;tlb_finish_mmu;tlb_flush_mmu_free;free_pages_and_swap_cache;release_pages;free_hot_cold_page_list 1
