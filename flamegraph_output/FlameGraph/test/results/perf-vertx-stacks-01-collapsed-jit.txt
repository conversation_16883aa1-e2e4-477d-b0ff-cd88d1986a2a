java;read;check_events;hypercall_page 1
java;start_thread;java_start;GCTaskThread::run;ScavengeRootsTask::do_it;ClassLoaderDataGraph::oops_do;ClassLoaderData::oops_do;PSScavengeKlassClosure::do_klass 1
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;PSPromotionManager::drain_stacks_depth;oopDesc* PSPromotionManager::copy_to_survivor_space<false>;InstanceKlass::oop_push_contents 1
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;ParallelTaskTerminator::offer_termination 5
java;start_thread;java_start;GCTaskThread::run;StealTask::do_it;SpinPause 7
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/buffer/AbstractByteBufAllocator:.directBuffer_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/AbstractReferenceCountedByteBuf:.release_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];io/netty/buffer/PooledByteBuf:.internalNioBuffer_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/NativeThread:.current_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];  3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];Java_sun_nio_ch_FileDispatcherImpl_write0 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;sys_write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;fget_light 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;__srcu_read_lock 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;__tcp_push_pending_frames 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;ktime_get_real 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;skb_clone 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_set_skb_tso_segs 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_hard_start_xmit 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_pick_tx 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;dev_hard_start_xmit;dev_queue_xmit_nit 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;dev_hard_start_xmit;loopback_xmit 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;netif_rx;netif_rx.part.82;xen_restore_fl_direct 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;dev_hard_start_xmit;loopback_xmit;netif_rx;netif_rx.part.82;xen_restore_fl_direct_end 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;dma_issue_pending_all 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;__inet_lookup_established 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_event_data_recv 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;sock_def_readable;__wake_up_sync_key;check_events;hypercall_page 19
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;bictcp_acked 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;ktime_get_real;getnstimeofday 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;ktime_get_real;getnstimeofday;xen_clocksource_get_cycles;xen_clocksource_read 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;tcp_rtt_estimator 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver;ip_local_deliver_finish;tcp_v4_rcv;tcp_v4_do_rcv;tcp_rcv_established;tcp_ack;tcp_clean_rtx_queue;tcp_valid_rtt_meas;tcp_rtt_estimator 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;__do_softirq;net_rx_action;process_backlog;__netif_receive_skb;ip_rcv;ip_rcv_finish;ip_local_deliver_finish 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;local_bh_enable;do_softirq;call_softirq;rcu_bh_qs 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_local_out;ip_output;ip_finish_output;dev_queue_xmit;netif_skb_features 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ktime_get_real;getnstimeofday;xen_clocksource_get_cycles;pvclock_clocksource_read 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ktime_get_real;getnstimeofday;xen_clocksource_get_cycles;xen_clocksource_read;pvclock_clocksource_read 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;ktime_get_real;xen_clocksource_get_cycles 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;__tcp_push_pending_frames;tcp_write_xmit;tcp_transmit_skb;skb_dst_set_noref 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;lock_sock_nested;_raw_spin_lock_bh;local_bh_disable 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__kmalloc_node_track_caller 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__kmalloc_node_track_caller;arch_local_irq_save 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;__phys_addr 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;get_slab 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;__alloc_skb;kmem_cache_alloc_node 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;sk_stream_alloc_skb;ksize 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_current_mss;ipv4_mtu 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_current_mss;tcp_established_options 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;tcp_send_mss;tcp_xmit_size_goal 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;do_sync_write;sock_aio_write;do_sock_write.isra.10;inet_sendmsg;tcp_sendmsg;tcp_xmit_size_goal 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;fsnotify 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;fsnotify;__srcu_read_lock 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;rw_verify_area 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;rw_verify_area;apparmor_file_permission 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;rw_verify_area;security_file_permission;apparmor_file_permission;common_file_perm 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/FileDispatcherImpl:.write0_[j];write;system_call_fastpath;sys_write;vfs_write;sock_aio_write 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.write_[j];sun/nio/ch/SocketChannelImpl:.writerCleanup_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/buffer/PooledUnsafeDirectByteBuf:.readBytes_[j];sun/nio/ch/SocketChannelImpl:.writerCleanup_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/ChannelOutboundHandlerAdapter:.flush_[j];io/netty/channel/AbstractChannelHandlerContext:.flush_[j];io/netty/channel/DefaultChannelPipeline$HeadContext:.flush_[j];io/netty/channel/AbstractChannel$AbstractUnsafe:.flush0_[j];io/netty/channel/nio/AbstractNioByteChannel:.doWrite_[j];io/netty/util/Recycler:.recycle_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j];io/netty/channel/ChannelDuplexHandler:.flush_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelReadComplete_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j];org/vertx/java/core/net/impl/VertxHandler:.channelReadComplete_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/buffer/AbstractReferenceCountedByteBuf:.release_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];java/util/concurrent/ConcurrentHashMap:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/mozilla/javascript/Context:.getWrapFactory_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/ScriptableObject:.getParentScope_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/WrapFactory:.wrapAsJavaObject_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/WrapFactory:.wrapAsJavaObject_[j];java/util/HashMap:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/WrapFactory:.wrap_[j];java/util/HashMap:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j];org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j];vtable chunks_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];org/mozilla/javascript/ScriptableObject$Slot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.name_[j];org/mozilla/javascript/IdScriptableObject:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.has_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.setAttributes_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/MemberBox:.invoke_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/WrapFactory:.wrap_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.findFunction_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaObject:.get_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.setAttributes_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j];org/mozilla/javascript/ScriptableObject$Slot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getPropFunctionAndThis_[j];org/mozilla/javascript/NativeJavaObject:.get_[j];java/util/HashMap:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];jint_disjoint_arraycopy_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.get_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.setAttributes_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.getObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.findInstanceIdInfo_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];vtable chunks_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/NativeFunction:.initScriptFunction_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 6
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.newObject_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptableObject:.getParentScope_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j] 4
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 5
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 6
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptableObject:.getPrototype_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptableObject:.getParentScope_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/TopLevel:.getBuiltinPrototype_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/ScriptRuntime:.name_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];vtable chunks_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.has_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/IdScriptableObject:.setAttributes_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.createFunctionActivation_[j];org/mozilla/javascript/TopLevel:.getBuiltinPrototype_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/optimizer/OptRuntime:.call2_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.setObjectProp_[j];org/mozilla/javascript/IdScriptableObject:.put_[j];org/mozilla/javascript/ScriptableObject:.getSlot_[j];org/mozilla/javascript/ScriptableObject:.createSlot_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/ScriptRuntime:.indexFromString_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/ScriptRuntime:.setObjectElem_[j];org/mozilla/javascript/ScriptRuntime:.indexFromString_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];io/netty/handler/codec/http/DefaultHttpHeaders:.set_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j] 3
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/buffer/AbstractByteBuf:.writeBytes_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/handler/codec/http/HttpObjectEncoder:.encode_[j];io/netty/buffer/AbstractByteBuf:.writeBytes_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/handler/codec/http/HttpObjectEncoder:.encode_[j];io/netty/buffer/AbstractByteBufAllocator:.directBuffer_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/handler/codec/http/HttpObjectEncoder:.encode_[j];io/netty/buffer/AbstractByteBufAllocator:.directBuffer_[j];io/netty/util/concurrent/FastThreadLocal:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/handler/codec/http/HttpObjectEncoder:.encode_[j];java/util/ArrayList:.add_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];io/netty/util/internal/RecyclableArrayList:.newInstance_[j];io/netty/util/concurrent/FastThreadLocal:.get_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];java/util/ArrayList:.ensureExplicitCapacity_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];io/netty/handler/codec/MessageToMessageEncoder:.write_[j];vtable chunks_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/channel/AbstractChannelHandlerContext:.write_[j];org/vertx/java/core/http/impl/VertxHttpHandler:.write_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/handler/codec/http/DefaultHttpHeaders:.add0_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];io/netty/handler/codec/http/DefaultHttpHeaders:.set_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/NativeJavaMethod:.call_[j];org/mozilla/javascript/MemberBox:.invoke_[j];sun/reflect/DelegatingMethodAccessorImpl:.invoke_[j];sun/nio/cs/UTF_8$Encoder:.<init>_[j];jbyte_disjoint_arraycopy_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];org/vertx/java/core/net/impl/VertxHandler:.channelRead_[j];org/vertx/java/core/http/impl/DefaultHttpServer$ServerHandler:.doMessageReceived_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vhello_js_1:.call_[j];org/mozilla/javascript/gen/file__home_bgregg_testtest_vert_x_2_1_4_sys_mods_io_vertx_lang_js_1_1_0_[j];org/mozilla/javascript/ScriptRuntime:.name_[j];org/mozilla/javascript/ScriptRuntime:.nameOrFunction_[j];org/mozilla/javascript/IdScriptableObject:.get_[j];org/mozilla/javascript/ScriptableObject$RelinkedSlot:.getValue_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/buffer/AbstractByteBuf:.forEachByteAsc0_[j];io/netty/util/internal/AppendableCharSequence:.append_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpHeaders:.isTransferEncodingChunked_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.findWhitespace_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j];io/netty/buffer/AbstractByteBuf:.forEachByteAsc0_[j] 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j];io/netty/handler/codec/http/HttpHeaders:.hash_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j];io/netty/handler/codec/http/HttpObjectDecoder:.splitHeader_[j] 5
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/AbstractChannelHandlerContext:.fireChannelRead_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelRead_[j];io/netty/handler/codec/http/HttpObjectDecoder:.decode_[j];io/netty/handler/codec/http/HttpObjectDecoder:.readHeaders_[j];java/util/Arrays:.fill_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];java/nio/channels/spi/AbstractInterruptibleChannel:.end_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read 2
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;sys_read 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;do_sync_read 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;__kfree_skb 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_rcv_space_adjust 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;__kfree_skb;skb_release_data 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;__kfree_skb;skb_release_head_state;dst_release 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;_raw_spin_lock_bh 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;skb_copy_datagram_iovec 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;skb_copy_datagram_iovec;copy_user_enhanced_fast_string 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;do_sync_read;sock_aio_read;sock_aio_read.part.13;do_sock_read.isra.12;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;__tcp_select_window 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j];sun/nio/ch/SocketChannelImpl:.read_[j];sun/nio/ch/FileDispatcherImpl:.read0_[j];read;system_call_fastpath;sys_read;vfs_read;rw_verify_area 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/nio/AbstractNioByteChannel$NioByteUnsafe:.read_[j];io/netty/handler/codec/ByteToMessageDecoder:.channelReadComplete_[j] 1
java;start_thread;java_start;JavaThread::run;JavaThread::thread_main_inner;thread_entry;JavaCalls::call_virtual;JavaCalls::call_virtual;JavaCalls::call_helper;call_stub_[j];Interpreter_[j];Interpreter_[j];io/netty/channel/nio/NioEventLoop:.run_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeys_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKeysOptimized_[j];io/netty/channel/nio/NioEventLoop:.processSelectedKey_[j];io/netty/channel/socket/nio/NioSocketChannel:.doReadBytes_[j] 1
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;PSIsAliveClosure::do_object_b 1
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;StringTable::unlink_or_oops_do 2
java;start_thread;java_start;VMThread::run;VMThread::loop;VMThread::evaluate_operation;VM_Operation::evaluate;VM_ParallelGCFailedAllocation::doit;ParallelScavengeHeap::failed_mem_allocate;PSScavenge::invoke;PSScavenge::invoke_no_policy;pthread_cond_signal@@GLIBC_2.3.2;system_call_fastpath;sys_futex;do_futex;futex_wake_op 1
java;write;check_events;hypercall_page 3
