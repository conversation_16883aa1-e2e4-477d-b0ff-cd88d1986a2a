<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1000" height="626" onload="init(evt)" viewBox="0 0 1000 626" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details;
	function init(evt) { details = document.getElementById("details").firstChild; }
	function s(info) { details.nodeValue = "Function: " + info; }
	function c() { details.nodeValue = ' '; }
]]>
</script>
<rect x="0.0" y="0" width="1000.0" height="626.0" fill="url(#background)"  />
<text text-anchor="middle" x="500" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Flame Graph</text>
<text text-anchor="" x="10" y="609" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<g class="func_g" onmouseover="s('unix`threadp (13 samples, 0.03%)')" onmouseout="c()">
<title>unix`threadp (13 samples, 0.03%)</title><rect x="881.3" y="337" width="0.3" height="15.0" fill="rgb(237,12,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigdiffset (11 samples, 0.02%)')" onmouseout="c()">
<title>genunix`sigdiffset (11 samples, 0.02%)</title><rect x="22.4" y="289" width="0.2" height="15.0" fill="rgb(249,76,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_emulate_pio (70 samples, 0.15%)')" onmouseout="c()">
<title>kvm`kvm_emulate_pio (70 samples, 0.15%)</title><rect x="850.8" y="273" width="1.5" height="15.0" fill="rgb(225,176,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mapping_level (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`mapping_level (7 samples, 0.02%)</title><rect x="819.5" y="257" width="0.2" height="15.0" fill="rgb(214,129,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`dispatch_hilevel (452 samples, 0.98%)')" onmouseout="c()">
<title>unix`dispatch_hilevel (452 samples, 0.98%)</title><rect x="859.3" y="273" width="9.7" height="15.0" fill="rgb(254,65,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_protmode (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`is_protmode (5 samples, 0.01%)</title><rect x="802.5" y="145" width="0.1" height="15.0" fill="rgb(229,174,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (21 samples, 0.05%)')" onmouseout="c()">
<title>unix`mutex_exit (21 samples, 0.05%)</title><rect x="799.0" y="81" width="0.4" height="15.0" fill="rgb(240,216,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_read_msr (29 samples, 0.06%)')" onmouseout="c()">
<title>kvm`native_read_msr (29 samples, 0.06%)</title><rect x="856.7" y="289" width="0.7" height="15.0" fill="rgb(216,209,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_update_ppr (49 samples, 0.11%)')" onmouseout="c()">
<title>kvm`apic_update_ppr (49 samples, 0.11%)</title><rect x="807.9" y="97" width="1.0" height="15.0" fill="rgb(217,65,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__sigtimedwait (336 samples, 0.73%)')" onmouseout="c()">
<title>libc.so.1`__sigtimedwait (336 samples, 0.73%)</title><rect x="967.6" y="481" width="7.2" height="15.0" fill="rgb(238,53,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`sys_syscall (10 samples, 0.02%)</title><rect x="934.3" y="401" width="0.2" height="15.0" fill="rgb(231,22,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syslwp_park (79 samples, 0.17%)')" onmouseout="c()">
<title>genunix`syslwp_park (79 samples, 0.17%)</title><rect x="980.4" y="401" width="1.7" height="15.0" fill="rgb(240,114,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`qemu_notify_event (5 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`qemu_notify_event (5 samples, 0.01%)</title><rect x="913.0" y="337" width="0.1" height="15.0" fill="rgb(243,167,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (19 samples, 0.04%)')" onmouseout="c()">
<title>kvm`vmcs_readl (19 samples, 0.04%)</title><rect x="736.8" y="209" width="0.4" height="15.0" fill="rgb(241,219,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`do_splx (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`do_splx (5 samples, 0.01%)</title><rect x="892.6" y="353" width="0.1" height="15.0" fill="rgb(231,135,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`queue_lock (7 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`queue_lock (7 samples, 0.02%)</title><rect x="964.5" y="417" width="0.2" height="15.0" fill="rgb(234,69,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`disp_getwork (90 samples, 0.20%)')" onmouseout="c()">
<title>unix`disp_getwork (90 samples, 0.20%)</title><rect x="926.0" y="257" width="1.9" height="15.0" fill="rgb(234,55,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (61 samples, 0.13%)')" onmouseout="c()">
<title>unix`mutex_enter (61 samples, 0.13%)</title><rect x="767.0" y="81" width="1.3" height="15.0" fill="rgb(226,143,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_readl (8 samples, 0.02%)</title><rect x="721.3" y="305" width="0.1" height="15.0" fill="rgb(205,205,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`emulator_write_emulated_onepage (17 samples, 0.04%)')" onmouseout="c()">
<title>kvm`emulator_write_emulated_onepage (17 samples, 0.04%)</title><rect x="816.6" y="209" width="0.4" height="15.0" fill="rgb(251,167,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_try (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`lock_try (10 samples, 0.02%)</title><rect x="36.6" y="273" width="0.2" height="15.0" fill="rgb(228,115,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`virtio_pci_config_writew (5 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`virtio_pci_config_writew (5 samples, 0.01%)</title><rect x="916.2" y="433" width="0.1" height="15.0" fill="rgb(209,20,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot_unaliased (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot_unaliased (7 samples, 0.02%)</title><rect x="799.4" y="97" width="0.2" height="15.0" fill="rgb(206,171,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`vmcs_readl (18 samples, 0.04%)</title><rect x="735.8" y="209" width="0.4" height="15.0" fill="rgb(224,105,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (83 samples, 0.18%)')" onmouseout="c()">
<title>unix`sys_syscall (83 samples, 0.18%)</title><rect x="980.4" y="417" width="1.8" height="15.0" fill="rgb(241,202,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`splr (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`splr (6 samples, 0.01%)</title><rect x="36.3" y="257" width="0.1" height="15.0" fill="rgb(233,158,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`do_fetch_insn_byte (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`do_fetch_insn_byte (9 samples, 0.02%)</title><rect x="744.6" y="209" width="0.2" height="15.0" fill="rgb(252,110,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`splr (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`splr (5 samples, 0.01%)</title><rect x="37.2" y="209" width="0.1" height="15.0" fill="rgb(237,80,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_ldt (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_read_ldt (5 samples, 0.01%)</title><rect x="719.9" y="305" width="0.1" height="15.0" fill="rgb(222,154,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigcheck (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`sigcheck (5 samples, 0.01%)</title><rect x="973.5" y="433" width="0.1" height="15.0" fill="rgb(219,212,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`gethrestime_sec (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`gethrestime_sec (12 samples, 0.03%)</title><rect x="911.1" y="193" width="0.2" height="15.0" fill="rgb(230,46,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_vcpu_put (56 samples, 0.12%)')" onmouseout="c()">
<title>kvm`vmx_vcpu_put (56 samples, 0.12%)</title><rect x="878.0" y="305" width="1.2" height="15.0" fill="rgb(246,198,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (17 samples, 0.04%)')" onmouseout="c()">
<title>unix`atomic_add_64 (17 samples, 0.04%)</title><rect x="977.0" y="433" width="0.3" height="15.0" fill="rgb(212,75,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syslwp_park (569 samples, 1.24%)')" onmouseout="c()">
<title>genunix`syslwp_park (569 samples, 1.24%)</title><rect x="919.3" y="369" width="12.2" height="15.0" fill="rgb(248,121,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (51 samples, 0.11%)')" onmouseout="c()">
<title>- (51 samples, 0.11%)</title><rect x="976.2" y="465" width="1.1" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`clear_bit (60 samples, 0.13%)')" onmouseout="c()">
<title>kvm`clear_bit (60 samples, 0.13%)</title><rect x="871.0" y="337" width="1.3" height="15.0" fill="rgb(214,199,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`schedctl_sigblock (36 samples, 0.08%)')" onmouseout="c()">
<title>genunix`schedctl_sigblock (36 samples, 0.08%)</title><rect x="21.6" y="289" width="0.8" height="15.0" fill="rgb(219,58,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`pit_has_pending_timer (44 samples, 0.10%)')" onmouseout="c()">
<title>kvm`pit_has_pending_timer (44 samples, 0.10%)</title><rect x="29.4" y="305" width="0.9" height="15.0" fill="rgb(247,90,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest_page (102 samples, 0.22%)')" onmouseout="c()">
<title>kvm`kvm_read_guest_page (102 samples, 0.22%)</title><rect x="750.5" y="145" width="2.1" height="15.0" fill="rgb(210,122,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_cpu_exec (44,756 samples, 97.49%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_cpu_exec (44,756 samples, 97.49%)</title><rect x="11.6" y="497" width="955.5" height="15.0" fill="rgb(237,74,33)" rx="2" ry="2" />
<text text-anchor="" x="14.6437938395852" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >qemu-system-x86_64`kvm_cpu_exec</text>
</g>
<g class="func_g" onmouseover="s('unix`pc_gethrestime (15 samples, 0.03%)')" onmouseout="c()">
<title>unix`pc_gethrestime (15 samples, 0.03%)</title><rect x="970.9" y="401" width="0.4" height="15.0" fill="rgb(225,38,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`atomic_add_64 (12 samples, 0.03%)</title><rect x="885.5" y="449" width="0.3" height="15.0" fill="rgb(232,222,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cpu_grow (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`cpu_grow (9 samples, 0.02%)</title><rect x="930.3" y="305" width="0.2" height="15.0" fill="rgb(214,41,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigorset (8 samples, 0.02%)')" onmouseout="c()">
<title>genunix`sigorset (8 samples, 0.02%)</title><rect x="23.1" y="305" width="0.2" height="15.0" fill="rgb(254,164,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_lock_queue (126 samples, 0.27%)')" onmouseout="c()">
<title>libc.so.1`mutex_lock_queue (126 samples, 0.27%)</title><rect x="934.5" y="417" width="2.7" height="15.0" fill="rgb(233,172,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`gdt_ucode_model (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`gdt_ucode_model (5 samples, 0.01%)</title><rect x="933.4" y="369" width="0.1" height="15.0" fill="rgb(246,43,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`restorectx (26 samples, 0.06%)')" onmouseout="c()">
<title>genunix`restorectx (26 samples, 0.06%)</title><rect x="933.3" y="385" width="0.6" height="15.0" fill="rgb(234,207,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_try (15 samples, 0.03%)')" onmouseout="c()">
<title>unix`lock_try (15 samples, 0.03%)</title><rect x="973.9" y="433" width="0.4" height="15.0" fill="rgb(251,25,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_walk_addr (639 samples, 1.39%)')" onmouseout="c()">
<title>kvm`paging64_walk_addr (639 samples, 1.39%)</title><rect x="788.7" y="145" width="13.7" height="15.0" fill="rgb(237,61,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_vector_enter (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`mutex_vector_enter (5 samples, 0.01%)</title><rect x="908.7" y="145" width="0.2" height="15.0" fill="rgb(214,162,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_set_eoi (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`apic_set_eoi (12 samples, 0.03%)</title><rect x="815.1" y="129" width="0.3" height="15.0" fill="rgb(232,90,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (201 samples, 0.44%)')" onmouseout="c()">
<title>unix`sys_syscall (201 samples, 0.44%)</title><rect x="968.2" y="449" width="4.3" height="15.0" fill="rgb(223,118,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_cr0_bits (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_read_cr0_bits (6 samples, 0.01%)</title><rect x="733.2" y="241" width="0.2" height="15.0" fill="rgb(205,13,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`next_segment (20 samples, 0.04%)')" onmouseout="c()">
<title>kvm`next_segment (20 samples, 0.04%)</title><rect x="771.7" y="129" width="0.4" height="15.0" fill="rgb(246,203,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_clear (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vcpu_clear (10 samples, 0.02%)</title><rect x="888.5" y="385" width="0.2" height="15.0" fill="rgb(237,154,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (6 samples, 0.01%)</title><rect x="887.7" y="433" width="0.1" height="15.0" fill="rgb(215,126,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_writel (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_writel (7 samples, 0.02%)</title><rect x="735.1" y="209" width="0.2" height="15.0" fill="rgb(236,154,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`tsc_read (6 samples, 0.01%)</title><rect x="971.1" y="369" width="0.1" height="15.0" fill="rgb(211,20,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`preempt (16 samples, 0.03%)')" onmouseout="c()">
<title>unix`preempt (16 samples, 0.03%)</title><rect x="887.1" y="433" width="0.3" height="15.0" fill="rgb(214,49,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mmu_topup_memory_cache_page (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`mmu_topup_memory_cache_page (5 samples, 0.01%)</title><rect x="846.7" y="225" width="0.1" height="15.0" fill="rgb(207,160,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_iodevice_write (21 samples, 0.05%)')" onmouseout="c()">
<title>kvm`kvm_iodevice_write (21 samples, 0.05%)</title><rect x="851.3" y="225" width="0.4" height="15.0" fill="rgb(237,210,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_cr0_bits (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`kvm_read_cr0_bits (18 samples, 0.04%)</title><rect x="852.8" y="273" width="0.4" height="15.0" fill="rgb(212,147,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gpte_to_gfn_lvl (20 samples, 0.04%)')" onmouseout="c()">
<title>kvm`paging64_gpte_to_gfn_lvl (20 samples, 0.04%)</title><rect x="788.3" y="145" width="0.4" height="15.0" fill="rgb(239,109,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_cr0_bits (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_read_cr0_bits (6 samples, 0.01%)</title><rect x="853.6" y="289" width="0.1" height="15.0" fill="rgb(232,98,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`virtio_net_handle_tx_bh (9 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`virtio_net_handle_tx_bh (9 samples, 0.02%)</title><rect x="897.5" y="369" width="0.2" height="15.0" fill="rgb(252,43,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb85a673 (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`0xfffffffffb85a673 (12 samples, 0.03%)</title><rect x="882.0" y="353" width="0.3" height="15.0" fill="rgb(208,165,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_tryenter (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_tryenter (11 samples, 0.02%)</title><rect x="909.2" y="161" width="0.3" height="15.0" fill="rgb(224,194,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bcopy (35 samples, 0.08%)')" onmouseout="c()">
<title>unix`bcopy (35 samples, 0.08%)</title><rect x="751.8" y="129" width="0.7" height="15.0" fill="rgb(248,20,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_wakeup (175 samples, 0.38%)')" onmouseout="c()">
<title>FSS`fss_wakeup (175 samples, 0.38%)</title><rect x="904.7" y="97" width="3.8" height="15.0" fill="rgb(243,154,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`cpu_outw (6 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`cpu_outw (6 samples, 0.01%)</title><rect x="895.7" y="465" width="0.1" height="15.0" fill="rgb(210,11,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (10 samples, 0.02%)')" onmouseout="c()">
<title>genunix`syscall_mstate (10 samples, 0.02%)</title><rect x="933.1" y="385" width="0.2" height="15.0" fill="rgb(229,184,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`swtch (7 samples, 0.02%)')" onmouseout="c()">
<title>unix`swtch (7 samples, 0.02%)</title><rect x="887.3" y="417" width="0.1" height="15.0" fill="rgb(237,36,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`virtio_net_handle_tx_bh (827 samples, 1.80%)')" onmouseout="c()">
<title>qemu-system-x86_64`virtio_net_handle_tx_bh (827 samples, 1.80%)</title><rect x="898.4" y="353" width="17.6" height="15.0" fill="rgb(209,193,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_cpu_has_interrupt (38 samples, 0.08%)')" onmouseout="c()">
<title>kvm`kvm_cpu_has_interrupt (38 samples, 0.08%)</title><rect x="42.0" y="289" width="0.8" height="15.0" fill="rgb(214,48,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`queue_lock (10 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`queue_lock (10 samples, 0.02%)</title><rect x="982.7" y="433" width="0.2" height="15.0" fill="rgb(243,67,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`irqchip_in_kernel (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`irqchip_in_kernel (5 samples, 0.01%)</title><rect x="43.4" y="305" width="0.1" height="15.0" fill="rgb(250,36,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (15 samples, 0.03%)')" onmouseout="c()">
<title>unix`sys_syscall (15 samples, 0.03%)</title><rect x="889.3" y="465" width="0.3" height="15.0" fill="rgb(215,148,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (9 samples, 0.02%)</title><rect x="972.0" y="417" width="0.2" height="15.0" fill="rgb(244,117,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_readl (10 samples, 0.02%)</title><rect x="774.7" y="145" width="0.2" height="15.0" fill="rgb(245,57,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_long_mode (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`is_long_mode (5 samples, 0.01%)</title><rect x="39.3" y="177" width="0.1" height="15.0" fill="rgb(246,181,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_get_gdt (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_get_gdt (5 samples, 0.01%)</title><rect x="878.1" y="273" width="0.1" height="15.0" fill="rgb(246,193,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`schedctl_sigblock (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`schedctl_sigblock (6 samples, 0.01%)</title><rect x="22.9" y="305" width="0.1" height="15.0" fill="rgb(233,114,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`ioctl (41,104 samples, 89.54%)')" onmouseout="c()">
<title>libc.so.1`ioctl (41,104 samples, 89.54%)</title><rect x="12.1" y="481" width="877.5" height="15.0" fill="rgb(237,66,26)" rx="2" ry="2" />
<text text-anchor="" x="15.1347971942665" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`ioctl</text>
</g>
<g class="func_g" onmouseover="s('kvm`kvm_arch_vcpu_runnable (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_arch_vcpu_runnable (8 samples, 0.02%)</title><rect x="25.0" y="321" width="0.2" height="15.0" fill="rgb(219,49,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`mutex_enter (5 samples, 0.01%)</title><rect x="877.6" y="289" width="0.1" height="15.0" fill="rgb(239,198,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_ctx_save (62 samples, 0.14%)')" onmouseout="c()">
<title>kvm`kvm_ctx_save (62 samples, 0.14%)</title><rect x="38.9" y="225" width="1.3" height="15.0" fill="rgb(241,53,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`tsc_read (6 samples, 0.01%)</title><rect x="975.4" y="401" width="0.1" height="15.0" fill="rgb(206,229,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`get_highest_priority_int (12 samples, 0.03%)')" onmouseout="c()">
<title>qemu-system-x86_64`get_highest_priority_int (12 samples, 0.03%)</title><rect x="966.2" y="385" width="0.2" height="15.0" fill="rgb(243,192,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_cr0_bits (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_read_cr0_bits (5 samples, 0.01%)</title><rect x="802.5" y="129" width="0.1" height="15.0" fill="rgb(240,86,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c91 (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`0xfffffffffb800c91 (8 samples, 0.02%)</title><rect x="982.2" y="433" width="0.2" height="15.0" fill="rgb(230,170,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mmu_topup_memory_cache (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`mmu_topup_memory_cache (5 samples, 0.01%)</title><rect x="819.7" y="257" width="0.1" height="15.0" fill="rgb(233,67,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb85a6b6 (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`0xfffffffffb85a6b6 (5 samples, 0.01%)</title><rect x="817.4" y="225" width="0.1" height="15.0" fill="rgb(210,226,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_block (103 samples, 0.22%)')" onmouseout="c()">
<title>genunix`cv_block (103 samples, 0.22%)</title><rect x="33.8" y="273" width="2.2" height="15.0" fill="rgb(239,78,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`hva_to_pfn (391 samples, 0.85%)')" onmouseout="c()">
<title>kvm`hva_to_pfn (391 samples, 0.85%)</title><rect x="827.3" y="225" width="8.3" height="15.0" fill="rgb(223,45,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_set_rflags (17 samples, 0.04%)')" onmouseout="c()">
<title>kvm`vmx_set_rflags (17 samples, 0.04%)</title><rect x="734.9" y="225" width="0.4" height="15.0" fill="rgb(248,148,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_main_loop_cpu (45,902 samples, 99.99%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_main_loop_cpu (45,902 samples, 99.99%)</title><rect x="10.0" y="513" width="980.0" height="15.0" fill="rgb(251,106,41)" rx="2" ry="2" />
<text text-anchor="" x="13.0426959438853" y="523.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >qemu-system-x86_64`kvm_main_loop_cpu</text>
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (9 samples, 0.02%)')" onmouseout="c()">
<title>unix`sys_syscall (9 samples, 0.02%)</title><rect x="975.8" y="465" width="0.2" height="15.0" fill="rgb(227,193,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`kcopy (17 samples, 0.04%)')" onmouseout="c()">
<title>unix`kcopy (17 samples, 0.04%)</title><rect x="801.0" y="97" width="0.4" height="15.0" fill="rgb(221,61,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (38 samples, 0.08%)')" onmouseout="c()">
<title>- (38 samples, 0.08%)</title><rect x="974.9" y="465" width="0.9" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_arch_push_nmi (5 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_arch_push_nmi (5 samples, 0.01%)</title><rect x="967.0" y="449" width="0.1" height="15.0" fill="rgb(244,179,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`sys_syscall (12 samples, 0.03%)</title><rect x="912.6" y="273" width="0.2" height="15.0" fill="rgb(234,212,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`seg_base (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`seg_base (8 samples, 0.02%)</title><rect x="776.4" y="225" width="0.2" height="15.0" fill="rgb(215,132,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`fpxsave_ctxt (18 samples, 0.04%)')" onmouseout="c()">
<title>unix`fpxsave_ctxt (18 samples, 0.04%)</title><rect x="929.2" y="257" width="0.3" height="15.0" fill="rgb(248,53,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`installctx (17 samples, 0.04%)')" onmouseout="c()">
<title>genunix`installctx (17 samples, 0.04%)</title><rect x="875.3" y="321" width="0.4" height="15.0" fill="rgb(220,12,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (40,837 samples, 88.96%)')" onmouseout="c()">
<title>- (40,837 samples, 88.96%)</title><rect x="13.3" y="465" width="871.8" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
<text text-anchor="" x="16.2875876791705" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >-</text>
</g>
<g class="func_g" onmouseover="s('kvm`tdp_page_fault (1,225 samples, 2.67%)')" onmouseout="c()">
<title>kvm`tdp_page_fault (1,225 samples, 2.67%)</title><rect x="820.6" y="257" width="26.2" height="15.0" fill="rgb(233,38,17)" rx="2" ry="2" />
<text text-anchor="" x="823.646538578835" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s('kvm`is_rsvd_bits_set (24 samples, 0.05%)')" onmouseout="c()">
<title>kvm`is_rsvd_bits_set (24 samples, 0.05%)</title><rect x="787.0" y="145" width="0.6" height="15.0" fill="rgb(236,184,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_paging (22 samples, 0.05%)')" onmouseout="c()">
<title>kvm`is_paging (22 samples, 0.05%)</title><rect x="852.7" y="289" width="0.5" height="15.0" fill="rgb(248,61,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cpu_update_pct (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`cpu_update_pct (5 samples, 0.01%)</title><rect x="35.4" y="241" width="0.1" height="15.0" fill="rgb(230,226,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_write_msr (57 samples, 0.12%)')" onmouseout="c()">
<title>kvm`native_write_msr (57 samples, 0.12%)</title><rect x="879.7" y="305" width="1.2" height="15.0" fill="rgb(242,9,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`disp_getwork (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`disp_getwork (12 samples, 0.03%)</title><rect x="981.3" y="289" width="0.3" height="15.0" fill="rgb(253,180,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`lwp_sigmask (31 samples, 0.07%)')" onmouseout="c()">
<title>genunix`lwp_sigmask (31 samples, 0.07%)</title><rect x="873.8" y="321" width="0.6" height="15.0" fill="rgb(251,210,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`xc_common (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`xc_common (8 samples, 0.02%)</title><rect x="888.5" y="337" width="0.2" height="15.0" fill="rgb(241,121,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_get_rflags (22 samples, 0.05%)')" onmouseout="c()">
<title>kvm`kvm_get_rflags (22 samples, 0.05%)</title><rect x="818.8" y="257" width="0.5" height="15.0" fill="rgb(249,50,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`irqchip_in_kernel (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`irqchip_in_kernel (6 samples, 0.01%)</title><rect x="15.5" y="353" width="0.1" height="15.0" fill="rgb(222,25,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`decode_register_operand (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`decode_register_operand (11 samples, 0.02%)</title><rect x="731.6" y="241" width="0.2" height="15.0" fill="rgb(246,153,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('specfs`spec_ioctl (5 samples, 0.01%)')" onmouseout="c()">
<title>specfs`spec_ioctl (5 samples, 0.01%)</title><rect x="883.6" y="417" width="0.1" height="15.0" fill="rgb(224,207,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_get_rflags (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_get_rflags (6 samples, 0.01%)</title><rect x="30.4" y="321" width="0.2" height="15.0" fill="rgb(233,35,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`pic_irqchip (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`pic_irqchip (10 samples, 0.02%)</title><rect x="874.5" y="337" width="0.2" height="15.0" fill="rgb(217,198,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_mmio_write (505 samples, 1.10%)')" onmouseout="c()">
<title>kvm`apic_mmio_write (505 samples, 1.10%)</title><rect x="804.6" y="145" width="10.8" height="15.0" fill="rgb(233,166,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`uiomove (10 samples, 0.02%)')" onmouseout="c()">
<title>genunix`uiomove (10 samples, 0.02%)</title><rect x="910.9" y="193" width="0.2" height="15.0" fill="rgb(207,137,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`swtch (31 samples, 0.07%)')" onmouseout="c()">
<title>unix`swtch (31 samples, 0.07%)</title><rect x="981.2" y="321" width="0.6" height="15.0" fill="rgb(207,199,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_present_gpte (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`is_present_gpte (6 samples, 0.01%)</title><rect x="786.9" y="145" width="0.1" height="15.0" fill="rgb(243,204,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_unlock (14 samples, 0.03%)')" onmouseout="c()">
<title>libc.so.1`mutex_unlock (14 samples, 0.03%)</title><rect x="988.8" y="481" width="0.3" height="15.0" fill="rgb(245,155,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_enabled (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_enabled (6 samples, 0.01%)</title><rect x="42.1" y="257" width="0.1" height="15.0" fill="rgb(211,182,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_exit (8 samples, 0.02%)</title><rect x="834.8" y="177" width="0.2" height="15.0" fill="rgb(206,11,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`_sysconfig (41 samples, 0.09%)')" onmouseout="c()">
<title>libc.so.1`_sysconfig (41 samples, 0.09%)</title><rect x="894.4" y="465" width="0.9" height="15.0" fill="rgb(245,218,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_arch_vcpu_put (29 samples, 0.06%)')" onmouseout="c()">
<title>kvm`kvm_arch_vcpu_put (29 samples, 0.06%)</title><rect x="38.9" y="209" width="0.6" height="15.0" fill="rgb(245,210,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (22 samples, 0.05%)')" onmouseout="c()">
<title>kvm`vmcs_readl (22 samples, 0.05%)</title><rect x="803.2" y="129" width="0.5" height="15.0" fill="rgb(211,181,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`__set_bit (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`__set_bit (6 samples, 0.01%)</title><rect x="729.6" y="209" width="0.1" height="15.0" fill="rgb(208,148,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`pic_irqchip (21 samples, 0.05%)')" onmouseout="c()">
<title>kvm`pic_irqchip (21 samples, 0.05%)</title><rect x="717.3" y="289" width="0.5" height="15.0" fill="rgb(222,82,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('pcplusmp`apic_send_ipi (5 samples, 0.01%)')" onmouseout="c()">
<title>pcplusmp`apic_send_ipi (5 samples, 0.01%)</title><rect x="814.5" y="33" width="0.1" height="15.0" fill="rgb(219,204,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_rsvd_bits_set (30 samples, 0.07%)')" onmouseout="c()">
<title>kvm`is_rsvd_bits_set (30 samples, 0.07%)</title><rect x="754.6" y="145" width="0.7" height="15.0" fill="rgb(245,133,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime (7 samples, 0.02%)')" onmouseout="c()">
<title>genunix`gethrtime (7 samples, 0.02%)</title><rect x="971.1" y="385" width="0.1" height="15.0" fill="rgb(242,101,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_find_highest_irr (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`apic_find_highest_irr (14 samples, 0.03%)</title><rect x="42.2" y="257" width="0.3" height="15.0" fill="rgb(235,159,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mmu_topup_memory_caches (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`mmu_topup_memory_caches (7 samples, 0.02%)</title><rect x="848.4" y="273" width="0.1" height="15.0" fill="rgb(211,32,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_read64 (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vmcs_read64 (12 samples, 0.03%)</title><rect x="849.0" y="273" width="0.3" height="15.0" fill="rgb(232,46,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`caps_charge_adjust (35 samples, 0.08%)')" onmouseout="c()">
<title>unix`caps_charge_adjust (35 samples, 0.08%)</title><rect x="921.4" y="241" width="0.7" height="15.0" fill="rgb(254,24,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sleepq_wakeone_chan (210 samples, 0.46%)')" onmouseout="c()">
<title>genunix`sleepq_wakeone_chan (210 samples, 0.46%)</title><rect x="904.2" y="113" width="4.5" height="15.0" fill="rgb(208,155,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_rip_write (21 samples, 0.05%)')" onmouseout="c()">
<title>kvm`kvm_rip_write (21 samples, 0.05%)</title><rect x="783.9" y="225" width="0.5" height="15.0" fill="rgb(225,120,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`cpu_set_apic_tpr (25 samples, 0.05%)')" onmouseout="c()">
<title>qemu-system-x86_64`cpu_set_apic_tpr (25 samples, 0.05%)</title><rect x="965.9" y="433" width="0.5" height="15.0" fill="rgb(246,84,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`thread_lock (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`thread_lock (6 samples, 0.01%)</title><rect x="923.9" y="289" width="0.2" height="15.0" fill="rgb(214,66,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_lock (417 samples, 0.91%)')" onmouseout="c()">
<title>libc.so.1`mutex_lock (417 samples, 0.91%)</title><rect x="979.9" y="481" width="8.9" height="15.0" fill="rgb(251,79,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_writel (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_writel (9 samples, 0.02%)</title><rect x="858.9" y="273" width="0.2" height="15.0" fill="rgb(239,106,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_save_host_state (218 samples, 0.47%)')" onmouseout="c()">
<title>kvm`vmx_save_host_state (218 samples, 0.47%)</title><rect x="854.6" y="305" width="4.6" height="15.0" fill="rgb(235,40,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`find_highest_vector (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`find_highest_vector (14 samples, 0.03%)</title><rect x="807.2" y="81" width="0.3" height="15.0" fill="rgb(229,163,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_mmu_gva_to_gpa_write (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_mmu_gva_to_gpa_write (9 samples, 0.02%)</title><rect x="816.2" y="193" width="0.2" height="15.0" fill="rgb(214,7,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`disp (60 samples, 0.13%)')" onmouseout="c()">
<title>unix`disp (60 samples, 0.13%)</title><rect x="37.1" y="257" width="1.3" height="15.0" fill="rgb(235,130,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_fire_urn (26 samples, 0.06%)')" onmouseout="c()">
<title>kvm`kvm_fire_urn (26 samples, 0.06%)</title><rect x="39.5" y="209" width="0.6" height="15.0" fill="rgb(211,195,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_block (18 samples, 0.04%)')" onmouseout="c()">
<title>genunix`cv_block (18 samples, 0.04%)</title><rect x="980.5" y="321" width="0.4" height="15.0" fill="rgb(247,151,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`read_msr (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`read_msr (6 samples, 0.01%)</title><rect x="720.9" y="305" width="0.2" height="15.0" fill="rgb(222,182,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrestime (18 samples, 0.04%)')" onmouseout="c()">
<title>genunix`gethrestime (18 samples, 0.04%)</title><rect x="970.9" y="417" width="0.4" height="15.0" fill="rgb(221,156,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`msix_enabled (5 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`msix_enabled (5 samples, 0.01%)</title><rect x="897.0" y="417" width="0.1" height="15.0" fill="rgb(205,71,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (40,837 samples, 88.96%)')" onmouseout="c()">
<title>unix`sys_syscall (40,837 samples, 88.96%)</title><rect x="13.3" y="449" width="871.8" height="15.0" fill="rgb(208,197,23)" rx="2" ry="2" />
<text text-anchor="" x="16.2875876791705" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >unix`sys_syscall</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`sigaddset (6 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`sigaddset (6 samples, 0.01%)</title><rect x="989.2" y="481" width="0.1" height="15.0" fill="rgb(218,138,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`write (16 samples, 0.03%)')" onmouseout="c()">
<title>libc.so.1`write (16 samples, 0.03%)</title><rect x="899.1" y="305" width="0.3" height="15.0" fill="rgb(222,183,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_cs_db_l_bits (39 samples, 0.08%)')" onmouseout="c()">
<title>kvm`vmx_get_cs_db_l_bits (39 samples, 0.08%)</title><rect x="846.8" y="257" width="0.8" height="15.0" fill="rgb(251,163,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`getf (18 samples, 0.04%)')" onmouseout="c()">
<title>genunix`getf (18 samples, 0.04%)</title><rect x="882.9" y="417" width="0.4" height="15.0" fill="rgb(225,42,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_enter_guest (38,371 samples, 83.59%)')" onmouseout="c()">
<title>kvm`vcpu_enter_guest (38,371 samples, 83.59%)</title><rect x="49.9" y="321" width="819.2" height="15.0" fill="rgb(212,166,9)" rx="2" ry="2" />
<text text-anchor="" x="52.9420555047271" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm`vcpu_enter_guest</text>
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (576 samples, 1.25%)')" onmouseout="c()">
<title>unix`sys_syscall (576 samples, 1.25%)</title><rect x="900.1" y="257" width="12.3" height="15.0" fill="rgb(212,65,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`savectx (22 samples, 0.05%)')" onmouseout="c()">
<title>genunix`savectx (22 samples, 0.05%)</title><rect x="928.7" y="257" width="0.4" height="15.0" fill="rgb(229,205,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`kcopy (19 samples, 0.04%)')" onmouseout="c()">
<title>unix`kcopy (19 samples, 0.04%)</title><rect x="770.9" y="97" width="0.4" height="15.0" fill="rgb(221,155,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`__apic_accept_irq (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`__apic_accept_irq (8 samples, 0.02%)</title><rect x="30.8" y="273" width="0.2" height="15.0" fill="rgb(254,177,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`_resume_from_idle (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`_resume_from_idle (8 samples, 0.02%)</title><rect x="36.4" y="273" width="0.2" height="15.0" fill="rgb(232,173,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_cache_reg (17 samples, 0.04%)')" onmouseout="c()">
<title>kvm`vmx_cache_reg (17 samples, 0.04%)</title><rect x="731.0" y="225" width="0.4" height="15.0" fill="rgb(233,16,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_update_ppr (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`apic_update_ppr (8 samples, 0.02%)</title><rect x="42.6" y="257" width="0.1" height="15.0" fill="rgb(219,2,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`unalias_gfn_instantiation (25 samples, 0.05%)')" onmouseout="c()">
<title>kvm`unalias_gfn_instantiation (25 samples, 0.05%)</title><rect x="766.5" y="81" width="0.5" height="15.0" fill="rgb(220,222,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_exit (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`disp_lock_exit (5 samples, 0.01%)</title><rect x="37.3" y="241" width="0.2" height="15.0" fill="rgb(230,41,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`_interrupt (461 samples, 1.00%)')" onmouseout="c()">
<title>unix`_interrupt (461 samples, 1.00%)</title><rect x="859.2" y="305" width="9.9" height="15.0" fill="rgb(235,55,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_io (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`handle_io (7 samples, 0.02%)</title><rect x="711.7" y="305" width="0.2" height="15.0" fill="rgb(219,110,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`cpu_physical_memory_is_dirty (9 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`cpu_physical_memory_is_dirty (9 samples, 0.02%)</title><rect x="913.4" y="305" width="0.2" height="15.0" fill="rgb(240,94,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`thread_lock (8 samples, 0.02%)')" onmouseout="c()">
<title>genunix`thread_lock (8 samples, 0.02%)</title><rect x="892.7" y="369" width="0.2" height="15.0" fill="rgb(246,102,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`vmcs_readl (18 samples, 0.04%)</title><rect x="849.3" y="273" width="0.4" height="15.0" fill="rgb(242,190,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`thread_lock (13 samples, 0.03%)')" onmouseout="c()">
<title>genunix`thread_lock (13 samples, 0.03%)</title><rect x="973.6" y="433" width="0.3" height="15.0" fill="rgb(220,17,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_rflags (15 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vmx_get_rflags (15 samples, 0.03%)</title><rect x="774.6" y="161" width="0.3" height="15.0" fill="rgb(216,30,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cpu_grow (7 samples, 0.02%)')" onmouseout="c()">
<title>genunix`cpu_grow (7 samples, 0.02%)</title><rect x="922.3" y="241" width="0.1" height="15.0" fill="rgb(254,103,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`post_kvm_run (2,348 samples, 5.11%)')" onmouseout="c()">
<title>qemu-system-x86_64`post_kvm_run (2,348 samples, 5.11%)</title><rect x="916.5" y="465" width="50.1" height="15.0" fill="rgb(253,189,19)" rx="2" ry="2" />
<text text-anchor="" x="919.47758462946" y="475.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >qemu-..</text>
</g>
<g class="func_g" onmouseover="s('unix`x86pte_access_pagetable (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`x86pte_access_pagetable (5 samples, 0.01%)</title><rect x="831.9" y="161" width="0.1" height="15.0" fill="rgb(253,178,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`splr (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`splr (10 samples, 0.02%)</title><rect x="973.7" y="417" width="0.2" height="15.0" fill="rgb(232,177,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`x86_emulate_insn (1,762 samples, 3.84%)')" onmouseout="c()">
<title>kvm`x86_emulate_insn (1,762 samples, 3.84%)</title><rect x="780.3" y="241" width="37.6" height="15.0" fill="rgb(208,100,5)" rx="2" ry="2" />
<text text-anchor="" x="783.277523635255" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm..</text>
</g>
<g class="func_g" onmouseover="s('genunix`cpu_update_pct (16 samples, 0.03%)')" onmouseout="c()">
<title>genunix`cpu_update_pct (16 samples, 0.03%)</title><rect x="907.1" y="65" width="0.4" height="15.0" fill="rgb(218,21,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_write_msr (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`native_write_msr (11 samples, 0.02%)</title><rect x="878.8" y="289" width="0.3" height="15.0" fill="rgb(229,164,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (21 samples, 0.05%)')" onmouseout="c()">
<title>unix`mutex_exit (21 samples, 0.05%)</title><rect x="768.3" y="81" width="0.4" height="15.0" fill="rgb(213,101,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (9 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_exit (9 samples, 0.02%)</title><rect x="909.0" y="161" width="0.2" height="15.0" fill="rgb(212,5,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`htable_getpage (305 samples, 0.66%)')" onmouseout="c()">
<title>unix`htable_getpage (305 samples, 0.66%)</title><rect x="828.6" y="193" width="6.5" height="15.0" fill="rgb(213,148,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_read_msr (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`native_read_msr (8 samples, 0.02%)</title><rect x="878.6" y="289" width="0.2" height="15.0" fill="rgb(220,22,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (51 samples, 0.11%)')" onmouseout="c()">
<title>unix`sys_syscall (51 samples, 0.11%)</title><rect x="976.2" y="449" width="1.1" height="15.0" fill="rgb(216,190,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_external_interrupt (25 samples, 0.05%)')" onmouseout="c()">
<title>kvm`handle_external_interrupt (25 samples, 0.05%)</title><rect x="711.0" y="305" width="0.5" height="15.0" fill="rgb(254,81,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_lock (15 samples, 0.03%)')" onmouseout="c()">
<title>libc.so.1`mutex_lock (15 samples, 0.03%)</title><rect x="895.3" y="465" width="0.3" height="15.0" fill="rgb(247,37,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (112 samples, 0.24%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (112 samples, 0.24%)</title><rect x="980.1" y="449" width="2.4" height="15.0" fill="rgb(240,156,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (15 samples, 0.03%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (15 samples, 0.03%)</title><rect x="884.3" y="417" width="0.3" height="15.0" fill="rgb(241,134,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_load_tr_desc (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`native_load_tr_desc (18 samples, 0.04%)</title><rect x="878.2" y="273" width="0.4" height="15.0" fill="rgb(253,190,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_enter_guest (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vcpu_enter_guest (12 samples, 0.03%)</title><rect x="874.9" y="337" width="0.3" height="15.0" fill="rgb(254,101,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`membar_enter (21 samples, 0.05%)')" onmouseout="c()">
<title>unix`membar_enter (21 samples, 0.05%)</title><rect x="905.9" y="81" width="0.4" height="15.0" fill="rgb(218,39,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`pit_ioport_write (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`pit_ioport_write (6 samples, 0.01%)</title><rect x="851.8" y="225" width="0.1" height="15.0" fill="rgb(226,136,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`set_parking_flag (6 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`set_parking_flag (6 samples, 0.01%)</title><rect x="964.7" y="417" width="0.1" height="15.0" fill="rgb(243,92,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`qemu_get_ram_ptr (6 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`qemu_get_ram_ptr (6 samples, 0.01%)</title><rect x="915.4" y="305" width="0.2" height="15.0" fill="rgb(229,73,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_protmode (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`is_protmode (7 samples, 0.02%)</title><rect x="749.0" y="177" width="0.2" height="15.0" fill="rgb(253,220,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cpu_decay (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`cpu_decay (9 samples, 0.02%)</title><rect x="930.3" y="289" width="0.2" height="15.0" fill="rgb(241,62,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_flush_coalesced_mmio_buffer (8 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_flush_coalesced_mmio_buffer (8 samples, 0.02%)</title><rect x="889.9" y="481" width="0.2" height="15.0" fill="rgb(236,70,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (38 samples, 0.08%)')" onmouseout="c()">
<title>unix`sys_syscall (38 samples, 0.08%)</title><rect x="974.9" y="449" width="0.9" height="15.0" fill="rgb(234,34,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_gpa (17 samples, 0.04%)')" onmouseout="c()">
<title>kvm`gfn_to_gpa (17 samples, 0.04%)</title><rect x="753.4" y="145" width="0.3" height="15.0" fill="rgb(242,91,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`__vcpu_run (40,015 samples, 87.17%)')" onmouseout="c()">
<title>kvm`__vcpu_run (40,015 samples, 87.17%)</title><rect x="16.8" y="337" width="854.2" height="15.0" fill="rgb(219,50,50)" rx="2" ry="2" />
<text text-anchor="" x="19.7886550777676" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm`__vcpu_run</text>
</g>
<g class="func_g" onmouseover="s('unix`pg_ev_thread_swtch (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`pg_ev_thread_swtch (12 samples, 0.03%)</title><rect x="38.4" y="257" width="0.2" height="15.0" fill="rgb(221,133,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_lock_impl (2,248 samples, 4.90%)')" onmouseout="c()">
<title>libc.so.1`mutex_lock_impl (2,248 samples, 4.90%)</title><rect x="916.9" y="433" width="48.0" height="15.0" fill="rgb(220,71,13)" rx="2" ry="2" />
<text text-anchor="" x="919.861848124428" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc..</text>
</g>
<g class="func_g" onmouseover="s('kvm`is_protmode (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`is_protmode (14 samples, 0.03%)</title><rect x="773.8" y="161" width="0.3" height="15.0" fill="rgb(247,20,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`virtio_queue_notify_vq (863 samples, 1.88%)')" onmouseout="c()">
<title>qemu-system-x86_64`virtio_queue_notify_vq (863 samples, 1.88%)</title><rect x="897.7" y="369" width="18.4" height="15.0" fill="rgb(243,214,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`queue_lock (50 samples, 0.11%)')" onmouseout="c()">
<title>libc.so.1`queue_lock (50 samples, 0.11%)</title><rect x="935.3" y="401" width="1.1" height="15.0" fill="rgb(246,147,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_vcpu_is_bsp (32 samples, 0.07%)')" onmouseout="c()">
<title>kvm`kvm_vcpu_is_bsp (32 samples, 0.07%)</title><rect x="28.7" y="305" width="0.7" height="15.0" fill="rgb(210,148,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`hat_getpfnum (334 samples, 0.73%)')" onmouseout="c()">
<title>unix`hat_getpfnum (334 samples, 0.73%)</title><rect x="828.3" y="209" width="7.1" height="15.0" fill="rgb(246,29,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_ctx_restore (36 samples, 0.08%)')" onmouseout="c()">
<title>kvm`kvm_ctx_restore (36 samples, 0.08%)</title><rect x="888.1" y="433" width="0.8" height="15.0" fill="rgb(224,59,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`unalias_gfn_instantiation (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`unalias_gfn_instantiation (8 samples, 0.02%)</title><rect x="826.6" y="209" width="0.1" height="15.0" fill="rgb(226,98,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_emulate_pio (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_emulate_pio (5 samples, 0.01%)</title><rect x="853.2" y="289" width="0.1" height="15.0" fill="rgb(211,178,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`post_syscall (7 samples, 0.02%)')" onmouseout="c()">
<title>genunix`post_syscall (7 samples, 0.02%)</title><rect x="982.3" y="417" width="0.1" height="15.0" fill="rgb(218,38,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_irqchip_in_kernel (7 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_irqchip_in_kernel (7 samples, 0.02%)</title><rect x="966.4" y="433" width="0.2" height="15.0" fill="rgb(210,194,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`getf (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`getf (6 samples, 0.01%)</title><rect x="911.7" y="225" width="0.1" height="15.0" fill="rgb(214,212,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`dm_request_for_irq_injection (46 samples, 0.10%)')" onmouseout="c()">
<title>kvm`dm_request_for_irq_injection (46 samples, 0.10%)</title><rect x="872.4" y="337" width="0.9" height="15.0" fill="rgb(209,19,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (576 samples, 1.25%)')" onmouseout="c()">
<title>- (576 samples, 1.25%)</title><rect x="900.1" y="273" width="12.3" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_set_interrupt_shadow (30 samples, 0.07%)')" onmouseout="c()">
<title>kvm`vmx_set_interrupt_shadow (30 samples, 0.07%)</title><rect x="736.6" y="241" width="0.7" height="15.0" fill="rgb(254,210,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot_unaliased (265 samples, 0.58%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot_unaliased (265 samples, 0.58%)</title><rect x="836.9" y="209" width="5.7" height="15.0" fill="rgb(242,156,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_read16 (16 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vmcs_read16 (16 samples, 0.03%)</title><rect x="774.2" y="161" width="0.3" height="15.0" fill="rgb(246,17,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`hva_to_pfn (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`hva_to_pfn (7 samples, 0.02%)</title><rect x="836.1" y="241" width="0.1" height="15.0" fill="rgb(240,179,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_waituntil_sig (63 samples, 0.14%)')" onmouseout="c()">
<title>genunix`cv_waituntil_sig (63 samples, 0.14%)</title><rect x="980.5" y="369" width="1.3" height="15.0" fill="rgb(222,65,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_mmu_page_fault (5,798 samples, 12.63%)')" onmouseout="c()">
<title>kvm`kvm_mmu_page_fault (5,798 samples, 12.63%)</title><rect x="724.6" y="273" width="123.8" height="15.0" fill="rgb(228,149,49)" rx="2" ry="2" />
<text text-anchor="" x="727.623360780726" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm`kvm_mmu_pag..</text>
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`kmem_cache_alloc (5 samples, 0.01%)</title><rect x="875.4" y="289" width="0.1" height="15.0" fill="rgb(244,176,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_unlock (6 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`mutex_unlock (6 samples, 0.01%)</title><rect x="895.6" y="465" width="0.1" height="15.0" fill="rgb(254,41,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb85a670 (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`0xfffffffffb85a670 (5 samples, 0.01%)</title><rect x="776.7" y="225" width="0.1" height="15.0" fill="rgb(236,106,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fifofs`fifo_write (7 samples, 0.02%)')" onmouseout="c()">
<title>fifofs`fifo_write (7 samples, 0.02%)</title><rect x="901.4" y="225" width="0.2" height="15.0" fill="rgb(226,181,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot_unaliased (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot_unaliased (14 samples, 0.03%)</title><rect x="750.9" y="113" width="0.3" height="15.0" fill="rgb(211,18,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`lwp_kill (113 samples, 0.25%)')" onmouseout="c()">
<title>genunix`lwp_kill (113 samples, 0.25%)</title><rect x="891.1" y="417" width="2.4" height="15.0" fill="rgb(233,124,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_writel (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_writel (10 samples, 0.02%)</title><rect x="734.7" y="225" width="0.2" height="15.0" fill="rgb(224,149,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_ept_violation (5,921 samples, 12.90%)')" onmouseout="c()">
<title>kvm`handle_ept_violation (5,921 samples, 12.90%)</title><rect x="723.3" y="289" width="126.4" height="15.0" fill="rgb(227,224,8)" rx="2" ry="2" />
<text text-anchor="" x="726.278438548338" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm`handle_ept_..</text>
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800ca0 (13 samples, 0.03%)')" onmouseout="c()">
<title>unix`0xfffffffffb800ca0 (13 samples, 0.03%)</title><rect x="974.4" y="465" width="0.3" height="15.0" fill="rgb(221,76,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (16 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vmcs_readl (16 samples, 0.03%)</title><rect x="774.2" y="145" width="0.3" height="15.0" fill="rgb(209,116,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_hva (175 samples, 0.38%)')" onmouseout="c()">
<title>kvm`gfn_to_hva (175 samples, 0.38%)</title><rect x="795.7" y="97" width="3.7" height="15.0" fill="rgb(214,4,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`irqchip_in_kernel (42 samples, 0.09%)')" onmouseout="c()">
<title>kvm`irqchip_in_kernel (42 samples, 0.09%)</title><rect x="711.9" y="305" width="0.9" height="15.0" fill="rgb(211,70,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`do_splx (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`do_splx (8 samples, 0.02%)</title><rect x="36.0" y="257" width="0.2" height="15.0" fill="rgb(244,44,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cpu_update_pct (20 samples, 0.04%)')" onmouseout="c()">
<title>genunix`cpu_update_pct (20 samples, 0.04%)</title><rect x="930.2" y="321" width="0.5" height="15.0" fill="rgb(248,170,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigcheck (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`sigcheck (5 samples, 0.01%)</title><rect x="874.3" y="305" width="0.1" height="15.0" fill="rgb(239,128,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb85a670 (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`0xfffffffffb85a670 (5 samples, 0.01%)</title><rect x="817.0" y="225" width="0.1" height="15.0" fill="rgb(240,137,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`lwp_kill (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`lwp_kill (5 samples, 0.01%)</title><rect x="894.0" y="449" width="0.1" height="15.0" fill="rgb(239,172,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_exit (10 samples, 0.02%)')" onmouseout="c()">
<title>genunix`disp_lock_exit (10 samples, 0.02%)</title><rect x="886.4" y="433" width="0.3" height="15.0" fill="rgb(241,83,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`fop_ioctl (40,717 samples, 88.70%)')" onmouseout="c()">
<title>genunix`fop_ioctl (40,717 samples, 88.70%)</title><rect x="13.7" y="417" width="869.2" height="15.0" fill="rgb(215,149,7)" rx="2" ry="2" />
<text text-anchor="" x="16.7145471180238" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`fop_ioctl</text>
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (137 samples, 0.30%)')" onmouseout="c()">
<title>unix`sys_syscall (137 samples, 0.30%)</title><rect x="891.1" y="433" width="2.9" height="15.0" fill="rgb(227,96,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigtimedwait (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`sigtimedwait (9 samples, 0.02%)</title><rect x="972.6" y="465" width="0.2" height="15.0" fill="rgb(250,143,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`__vmx_load_host_state (13 samples, 0.03%)')" onmouseout="c()">
<title>kvm`__vmx_load_host_state (13 samples, 0.03%)</title><rect x="39.0" y="177" width="0.3" height="15.0" fill="rgb(252,34,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`memcpy (97 samples, 0.21%)')" onmouseout="c()">
<title>libc.so.1`memcpy (97 samples, 0.21%)</title><rect x="977.8" y="481" width="2.1" height="15.0" fill="rgb(223,18,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_search_irr (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`apic_search_irr (12 samples, 0.03%)</title><rect x="42.2" y="241" width="0.3" height="15.0" fill="rgb(239,33,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (8 samples, 0.02%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (8 samples, 0.02%)</title><rect x="893.6" y="401" width="0.2" height="15.0" fill="rgb(210,22,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (18 samples, 0.04%)')" onmouseout="c()">
<title>unix`sys_syscall (18 samples, 0.04%)</title><rect x="977.4" y="465" width="0.4" height="15.0" fill="rgb(219,211,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`siginfofree (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`siginfofree (9 samples, 0.02%)</title><rect x="971.4" y="417" width="0.2" height="15.0" fill="rgb(247,3,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`mutex_enter (5 samples, 0.01%)</title><rect x="881.2" y="337" width="0.1" height="15.0" fill="rgb(214,127,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_get_reg (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_get_reg (5 samples, 0.01%)</title><rect x="807.7" y="97" width="0.1" height="15.0" fill="rgb(208,155,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sep_restore (15 samples, 0.03%)')" onmouseout="c()">
<title>unix`sep_restore (15 samples, 0.03%)</title><rect x="933.9" y="385" width="0.4" height="15.0" fill="rgb(253,17,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`_lwp_kill (162 samples, 0.35%)')" onmouseout="c()">
<title>libc.so.1`_lwp_kill (162 samples, 0.35%)</title><rect x="890.9" y="465" width="3.5" height="15.0" fill="rgb(240,54,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_main_loop_wait (1,061 samples, 2.31%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_main_loop_wait (1,061 samples, 2.31%)</title><rect x="967.2" y="497" width="22.7" height="15.0" fill="rgb(223,8,49)" rx="2" ry="2" />
<text text-anchor="" x="970.221713937176" y="507.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >q..</text>
</g>
<g class="func_g" onmouseover="s('genunix`thread_lock (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`thread_lock (6 samples, 0.01%)</title><rect x="932.7" y="369" width="0.1" height="15.0" fill="rgb(238,166,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_put (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vcpu_put (5 samples, 0.01%)</title><rect x="881.9" y="353" width="0.1" height="15.0" fill="rgb(227,51,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`pit_has_pending_timer (15 samples, 0.03%)')" onmouseout="c()">
<title>kvm`pit_has_pending_timer (15 samples, 0.03%)</title><rect x="43.0" y="321" width="0.3" height="15.0" fill="rgb(218,36,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`phys_page_find_alloc (24 samples, 0.05%)')" onmouseout="c()">
<title>qemu-system-x86_64`phys_page_find_alloc (24 samples, 0.05%)</title><rect x="914.0" y="273" width="0.6" height="15.0" fill="rgb(236,44,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot_unaliased (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot_unaliased (5 samples, 0.01%)</title><rect x="827.2" y="225" width="0.1" height="15.0" fill="rgb(227,183,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_try (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`lock_try (12 samples, 0.03%)</title><rect x="893.0" y="369" width="0.2" height="15.0" fill="rgb(228,204,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_32 (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`atomic_add_32 (8 samples, 0.02%)</title><rect x="928.5" y="257" width="0.1" height="15.0" fill="rgb(250,119,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`thread_lock (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`thread_lock (6 samples, 0.01%)</title><rect x="36.3" y="273" width="0.1" height="15.0" fill="rgb(213,211,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`set_parking_flag (24 samples, 0.05%)')" onmouseout="c()">
<title>libc.so.1`set_parking_flag (24 samples, 0.05%)</title><rect x="936.6" y="401" width="0.5" height="15.0" fill="rgb(218,134,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`reload_tss (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`reload_tss (5 samples, 0.01%)</title><rect x="879.1" y="289" width="0.1" height="15.0" fill="rgb(211,89,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_is_error_hva (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_is_error_hva (6 samples, 0.01%)</title><rect x="794.4" y="113" width="0.1" height="15.0" fill="rgb(235,44,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mmu_topup_memory_cache (21 samples, 0.05%)')" onmouseout="c()">
<title>kvm`mmu_topup_memory_cache (21 samples, 0.05%)</title><rect x="846.2" y="225" width="0.5" height="15.0" fill="rgb(250,222,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_flush_tlb (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmx_flush_tlb (5 samples, 0.01%)</title><rect x="721.6" y="305" width="0.1" height="15.0" fill="rgb(244,229,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gpte_access (26 samples, 0.06%)')" onmouseout="c()">
<title>kvm`paging64_gpte_access (26 samples, 0.06%)</title><rect x="755.7" y="145" width="0.6" height="15.0" fill="rgb(226,36,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`virtio_ioport_write (881 samples, 1.92%)')" onmouseout="c()">
<title>qemu-system-x86_64`virtio_ioport_write (881 samples, 1.92%)</title><rect x="897.3" y="401" width="18.8" height="15.0" fill="rgb(232,153,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_apic_has_interrupt (34 samples, 0.07%)')" onmouseout="c()">
<title>kvm`kvm_apic_has_interrupt (34 samples, 0.07%)</title><rect x="42.0" y="273" width="0.7" height="15.0" fill="rgb(205,95,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sep_save (7 samples, 0.02%)')" onmouseout="c()">
<title>unix`sep_save (7 samples, 0.02%)</title><rect x="40.9" y="241" width="0.1" height="15.0" fill="rgb(209,162,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_get_rflags (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_get_rflags (9 samples, 0.02%)</title><rect x="43.7" y="305" width="0.2" height="15.0" fill="rgb(253,81,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (5 samples, 0.01%)</title><rect x="919.2" y="353" width="0.1" height="15.0" fill="rgb(222,208,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_io_bus_write (37 samples, 0.08%)')" onmouseout="c()">
<title>kvm`kvm_io_bus_write (37 samples, 0.08%)</title><rect x="851.1" y="241" width="0.8" height="15.0" fill="rgb(248,153,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_rip_read (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_rip_read (10 samples, 0.02%)</title><rect x="776.2" y="225" width="0.2" height="15.0" fill="rgb(222,115,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (5 samples, 0.01%)</title><rect x="921.7" y="209" width="0.1" height="15.0" fill="rgb(229,6,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`resume (54 samples, 0.12%)')" onmouseout="c()">
<title>unix`resume (54 samples, 0.12%)</title><rect x="928.7" y="273" width="1.1" height="15.0" fill="rgb(220,184,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_get_reg (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_get_reg (6 samples, 0.01%)</title><rect x="42.1" y="241" width="0.1" height="15.0" fill="rgb(245,126,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`x86_decode_insn (1,977 samples, 4.31%)')" onmouseout="c()">
<title>kvm`x86_decode_insn (1,977 samples, 4.31%)</title><rect x="738.1" y="241" width="42.2" height="15.0" fill="rgb(243,59,41)" rx="2" ry="2" />
<text text-anchor="" x="741.072583104605" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm..</text>
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmcs_readl (5 samples, 0.01%)</title><rect x="736.2" y="225" width="0.1" height="15.0" fill="rgb(245,7,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`lduw_le_p (8 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`lduw_le_p (8 samples, 0.02%)</title><rect x="913.6" y="305" width="0.2" height="15.0" fill="rgb(233,166,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot_unaliased (46 samples, 0.10%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot_unaliased (46 samples, 0.10%)</title><rect x="765.5" y="81" width="1.0" height="15.0" fill="rgb(242,158,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`atomic_add_64 (12 samples, 0.03%)</title><rect x="931.0" y="337" width="0.3" height="15.0" fill="rgb(233,39,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__write (614 samples, 1.34%)')" onmouseout="c()">
<title>libc.so.1`__write (614 samples, 1.34%)</title><rect x="899.7" y="289" width="13.1" height="15.0" fill="rgb(224,44,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_read_guest (10 samples, 0.02%)</title><rect x="787.8" y="145" width="0.2" height="15.0" fill="rgb(210,84,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sysconfig (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`sysconfig (5 samples, 0.01%)</title><rect x="976.8" y="433" width="0.2" height="15.0" fill="rgb(249,157,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mapping_level (407 samples, 0.89%)')" onmouseout="c()">
<title>kvm`mapping_level (407 samples, 0.89%)</title><rect x="836.4" y="241" width="8.7" height="15.0" fill="rgb(244,217,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`memset (11 samples, 0.02%)')" onmouseout="c()">
<title>genunix`memset (11 samples, 0.02%)</title><rect x="742.8" y="225" width="0.3" height="15.0" fill="rgb(209,5,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (29 samples, 0.06%)')" onmouseout="c()">
<title>genunix`syscall_mstate (29 samples, 0.06%)</title><rect x="976.2" y="433" width="0.6" height="15.0" fill="rgb(253,215,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_vcpu_load (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmx_vcpu_load (11 samples, 0.02%)</title><rect x="876.7" y="321" width="0.2" height="15.0" fill="rgb(229,206,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`swtch (231 samples, 0.50%)')" onmouseout="c()">
<title>unix`swtch (231 samples, 0.50%)</title><rect x="924.9" y="289" width="4.9" height="15.0" fill="rgb(212,139,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`caps_charge_adjust (22 samples, 0.05%)')" onmouseout="c()">
<title>unix`caps_charge_adjust (22 samples, 0.05%)</title><rect x="34.8" y="225" width="0.4" height="15.0" fill="rgb(210,106,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`queue_unlock (9 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`queue_unlock (9 samples, 0.02%)</title><rect x="936.4" y="401" width="0.2" height="15.0" fill="rgb(219,130,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_cache_reg (37 samples, 0.08%)')" onmouseout="c()">
<title>kvm`vmx_cache_reg (37 samples, 0.08%)</title><rect x="729.9" y="209" width="0.8" height="15.0" fill="rgb(252,65,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`qemu_get_ram_ptr (27 samples, 0.06%)')" onmouseout="c()">
<title>qemu-system-x86_64`qemu_get_ram_ptr (27 samples, 0.06%)</title><rect x="914.7" y="289" width="0.6" height="15.0" fill="rgb(233,125,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`issig (50 samples, 0.11%)')" onmouseout="c()">
<title>genunix`issig (50 samples, 0.11%)</title><rect x="969.4" y="385" width="1.0" height="15.0" fill="rgb(235,219,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`allocb (64 samples, 0.14%)')" onmouseout="c()">
<title>genunix`allocb (64 samples, 0.14%)</title><rect x="909.5" y="193" width="1.3" height="15.0" fill="rgb(217,153,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`syscall_mstate (5 samples, 0.01%)</title><rect x="885.4" y="449" width="0.1" height="15.0" fill="rgb(218,113,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_sigprocmask (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_sigprocmask (8 samples, 0.02%)</title><rect x="881.6" y="353" width="0.2" height="15.0" fill="rgb(225,105,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`new_mstate (17 samples, 0.04%)')" onmouseout="c()">
<title>genunix`new_mstate (17 samples, 0.04%)</title><rect x="35.3" y="257" width="0.3" height="15.0" fill="rgb(207,229,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`x86_emulate_insn (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`x86_emulate_insn (14 samples, 0.03%)</title><rect x="848.1" y="257" width="0.3" height="15.0" fill="rgb(249,168,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (8 samples, 0.02%)')" onmouseout="c()">
<title>genunix`syscall_mstate (8 samples, 0.02%)</title><rect x="974.4" y="449" width="0.2" height="15.0" fill="rgb(244,56,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`copyin (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`copyin (11 samples, 0.02%)</title><rect x="771.3" y="113" width="0.2" height="15.0" fill="rgb(215,57,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cyclic_reprogram (142 samples, 0.31%)')" onmouseout="c()">
<title>genunix`cyclic_reprogram (142 samples, 0.31%)</title><rect x="811.7" y="97" width="3.1" height="15.0" fill="rgb(218,82,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`phys_page_find_alloc (7 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`phys_page_find_alloc (7 samples, 0.02%)</title><rect x="914.6" y="289" width="0.1" height="15.0" fill="rgb(226,144,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__cerror (6 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`__cerror (6 samples, 0.01%)</title><rect x="12.0" y="481" width="0.1" height="15.0" fill="rgb(238,8,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_rip_read (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_rip_read (10 samples, 0.02%)</title><rect x="733.8" y="241" width="0.3" height="15.0" fill="rgb(207,96,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cpu_decay (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`cpu_decay (6 samples, 0.01%)</title><rect x="922.3" y="225" width="0.1" height="15.0" fill="rgb(250,64,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (13 samples, 0.03%)')" onmouseout="c()">
<title>genunix`syscall_mstate (13 samples, 0.03%)</title><rect x="894.5" y="417" width="0.3" height="15.0" fill="rgb(208,57,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (12 samples, 0.03%)')" onmouseout="c()">
<title>genunix`syscall_mstate (12 samples, 0.03%)</title><rect x="887.6" y="449" width="0.2" height="15.0" fill="rgb(227,222,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_get_reg (37 samples, 0.08%)')" onmouseout="c()">
<title>kvm`apic_get_reg (37 samples, 0.08%)</title><rect x="27.2" y="273" width="0.8" height="15.0" fill="rgb(232,55,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_fetch_guest_virt (1,238 samples, 2.70%)')" onmouseout="c()">
<title>kvm`kvm_fetch_guest_virt (1,238 samples, 2.70%)</title><rect x="748.6" y="193" width="26.4" height="15.0" fill="rgb(252,48,47)" rx="2" ry="2" />
<text text-anchor="" x="751.618481244282" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s('kvm`emulate_instruction (4,340 samples, 9.45%)')" onmouseout="c()">
<title>kvm`emulate_instruction (4,340 samples, 9.45%)</title><rect x="725.2" y="257" width="92.7" height="15.0" fill="rgb(232,134,10)" rx="2" ry="2" />
<text text-anchor="" x="728.242451967063" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm`emulate..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_unlock (11 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`mutex_unlock (11 samples, 0.02%)</title><rect x="966.6" y="449" width="0.3" height="15.0" fill="rgb(242,70,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`timespecadd (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`timespecadd (6 samples, 0.01%)</title><rect x="972.2" y="433" width="0.1" height="15.0" fill="rgb(221,129,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_set_shared_msr (45 samples, 0.10%)')" onmouseout="c()">
<title>kvm`kvm_set_shared_msr (45 samples, 0.10%)</title><rect x="855.7" y="289" width="1.0" height="15.0" fill="rgb(212,139,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_try (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`lock_try (8 samples, 0.02%)</title><rect x="886.9" y="433" width="0.2" height="15.0" fill="rgb(244,177,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_gpa (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`gfn_to_gpa (5 samples, 0.01%)</title><rect x="786.5" y="161" width="0.1" height="15.0" fill="rgb(242,156,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_hva (16 samples, 0.03%)')" onmouseout="c()">
<title>kvm`gfn_to_hva (16 samples, 0.03%)</title><rect x="821.3" y="241" width="0.4" height="15.0" fill="rgb(242,133,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bcopy (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`bcopy (11 samples, 0.02%)</title><rect x="777.4" y="225" width="0.2" height="15.0" fill="rgb(223,159,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb85a60d (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`0xfffffffffb85a60d (5 samples, 0.01%)</title><rect x="776.6" y="225" width="0.1" height="15.0" fill="rgb(215,27,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_trylock_adaptive (1,281 samples, 2.79%)')" onmouseout="c()">
<title>libc.so.1`mutex_trylock_adaptive (1,281 samples, 2.79%)</title><rect x="937.2" y="417" width="27.3" height="15.0" fill="rgb(210,15,13)" rx="2" ry="2" />
<text text-anchor="" x="940.185117413846" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >l..</text>
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`vring_used_flags_set_bit (124 samples, 0.27%)')" onmouseout="c()">
<title>qemu-system-x86_64`vring_used_flags_set_bit (124 samples, 0.27%)</title><rect x="913.4" y="321" width="2.6" height="15.0" fill="rgb(252,138,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`find_highest_vector (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`find_highest_vector (5 samples, 0.01%)</title><rect x="42.6" y="225" width="0.1" height="15.0" fill="rgb(254,42,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmcs_readl (5 samples, 0.01%)</title><rect x="774.5" y="161" width="0.1" height="15.0" fill="rgb(220,177,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`removectx (24 samples, 0.05%)')" onmouseout="c()">
<title>genunix`removectx (24 samples, 0.05%)</title><rect x="877.2" y="321" width="0.5" height="15.0" fill="rgb(225,197,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sleepq_unlink (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`sleepq_unlink (9 samples, 0.02%)</title><rect x="908.5" y="97" width="0.2" height="15.0" fill="rgb(244,191,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_hw_enabled (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`apic_hw_enabled (7 samples, 0.02%)</title><rect x="28.1" y="289" width="0.2" height="15.0" fill="rgb(244,73,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`to_virtio_net (6 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`to_virtio_net (6 samples, 0.01%)</title><rect x="898.2" y="353" width="0.1" height="15.0" fill="rgb(251,102,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sep_save (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`sep_save (12 samples, 0.03%)</title><rect x="929.6" y="257" width="0.2" height="15.0" fill="rgb(217,94,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_save_host_state (21 samples, 0.05%)')" onmouseout="c()">
<title>kvm`vmx_save_host_state (21 samples, 0.05%)</title><rect x="869.6" y="321" width="0.5" height="15.0" fill="rgb(231,52,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`ioctl (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`ioctl (6 samples, 0.01%)</title><rect x="885.1" y="465" width="0.1" height="15.0" fill="rgb(212,14,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_readl (9 samples, 0.02%)</title><rect x="802.7" y="129" width="0.2" height="15.0" fill="rgb(206,165,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sysconfig (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`sysconfig (5 samples, 0.01%)</title><rect x="895.0" y="449" width="0.1" height="15.0" fill="rgb(254,215,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_write16 (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_write16 (9 samples, 0.02%)</title><rect x="858.9" y="289" width="0.2" height="15.0" fill="rgb(234,55,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_set_spl (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`lock_set_spl (10 samples, 0.02%)</title><rect x="925.7" y="241" width="0.2" height="15.0" fill="rgb(249,72,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`memset (25 samples, 0.05%)')" onmouseout="c()">
<title>genunix`memset (25 samples, 0.05%)</title><rect x="728.1" y="241" width="0.6" height="15.0" fill="rgb(222,166,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`resume (112 samples, 0.24%)')" onmouseout="c()">
<title>unix`resume (112 samples, 0.24%)</title><rect x="38.6" y="257" width="2.4" height="15.0" fill="rgb(238,136,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_hva (40 samples, 0.09%)')" onmouseout="c()">
<title>kvm`gfn_to_hva (40 samples, 0.09%)</title><rect x="750.8" y="129" width="0.8" height="15.0" fill="rgb(222,114,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_exit (8 samples, 0.02%)')" onmouseout="c()">
<title>genunix`disp_lock_exit (8 samples, 0.02%)</title><rect x="892.5" y="369" width="0.2" height="15.0" fill="rgb(210,208,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`issig (28 samples, 0.06%)')" onmouseout="c()">
<title>genunix`issig (28 samples, 0.06%)</title><rect x="16.0" y="337" width="0.6" height="15.0" fill="rgb(213,191,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`write (539 samples, 1.17%)')" onmouseout="c()">
<title>genunix`write (539 samples, 1.17%)</title><rect x="900.7" y="241" width="11.5" height="15.0" fill="rgb(239,183,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`qemu_notify_event (659 samples, 1.44%)')" onmouseout="c()">
<title>qemu-system-x86_64`qemu_notify_event (659 samples, 1.44%)</title><rect x="899.0" y="321" width="14.0" height="15.0" fill="rgb(249,65,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sleepq_insert (29 samples, 0.06%)')" onmouseout="c()">
<title>genunix`sleepq_insert (29 samples, 0.06%)</title><rect x="922.5" y="273" width="0.7" height="15.0" fill="rgb(250,95,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`sysconf (10 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`sysconf (10 samples, 0.02%)</title><rect x="11.3" y="497" width="0.3" height="15.0" fill="rgb(221,196,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`apic_irq_pending (15 samples, 0.03%)')" onmouseout="c()">
<title>qemu-system-x86_64`apic_irq_pending (15 samples, 0.03%)</title><rect x="966.1" y="401" width="0.3" height="15.0" fill="rgb(221,191,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lwp_segregs_restore (16 samples, 0.03%)')" onmouseout="c()">
<title>unix`lwp_segregs_restore (16 samples, 0.03%)</title><rect x="933.5" y="369" width="0.4" height="15.0" fill="rgb(241,86,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`_syscall6 (58 samples, 0.13%)')" onmouseout="c()">
<title>libc.so.1`_syscall6 (58 samples, 0.13%)</title><rect x="974.8" y="481" width="1.2" height="15.0" fill="rgb(207,55,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_sleep (77 samples, 0.17%)')" onmouseout="c()">
<title>FSS`fss_sleep (77 samples, 0.17%)</title><rect x="920.5" y="273" width="1.6" height="15.0" fill="rgb(242,123,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_handle_exit (6,225 samples, 13.56%)')" onmouseout="c()">
<title>kvm`vmx_handle_exit (6,225 samples, 13.56%)</title><rect x="721.7" y="305" width="132.9" height="15.0" fill="rgb(235,143,18)" rx="2" ry="2" />
<text text-anchor="" x="724.677340652638" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm`vmx_handle_e..</text>
</g>
<g class="func_g" onmouseover="s('kvm`test_and_clear_bit (73 samples, 0.16%)')" onmouseout="c()">
<title>kvm`test_and_clear_bit (73 samples, 0.16%)</title><rect x="809.5" y="97" width="1.6" height="15.0" fill="rgb(239,108,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`schedctl_finish_sigblock (19 samples, 0.04%)')" onmouseout="c()">
<title>genunix`schedctl_finish_sigblock (19 samples, 0.04%)</title><rect x="873.9" y="305" width="0.4" height="15.0" fill="rgb(246,55,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_xcall (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_xcall (9 samples, 0.02%)</title><rect x="888.5" y="369" width="0.2" height="15.0" fill="rgb(240,113,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`variable_test_bit (16 samples, 0.03%)')" onmouseout="c()">
<title>kvm`variable_test_bit (16 samples, 0.03%)</title><rect x="811.1" y="97" width="0.3" height="15.0" fill="rgb(209,171,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_enabled (40 samples, 0.09%)')" onmouseout="c()">
<title>kvm`apic_enabled (40 samples, 0.09%)</title><rect x="25.5" y="305" width="0.9" height="15.0" fill="rgb(213,34,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`x86pte_access_pagetable (36 samples, 0.08%)')" onmouseout="c()">
<title>unix`x86pte_access_pagetable (36 samples, 0.08%)</title><rect x="832.8" y="145" width="0.7" height="15.0" fill="rgb(206,212,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_waituntil_sig (86 samples, 0.19%)')" onmouseout="c()">
<title>genunix`cv_waituntil_sig (86 samples, 0.19%)</title><rect x="969.0" y="417" width="1.9" height="15.0" fill="rgb(208,214,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_lapic_sync_from_vapic (41 samples, 0.09%)')" onmouseout="c()">
<title>kvm`kvm_lapic_sync_from_vapic (41 samples, 0.09%)</title><rect x="31.2" y="321" width="0.9" height="15.0" fill="rgb(210,41,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`send_dirint (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`send_dirint (5 samples, 0.01%)</title><rect x="814.5" y="49" width="0.1" height="15.0" fill="rgb(246,29,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_register_read (22 samples, 0.05%)')" onmouseout="c()">
<title>kvm`kvm_register_read (22 samples, 0.05%)</title><rect x="733.4" y="241" width="0.4" height="15.0" fill="rgb(226,26,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c91 (38 samples, 0.08%)')" onmouseout="c()">
<title>unix`0xfffffffffb800c91 (38 samples, 0.08%)</title><rect x="932.3" y="401" width="0.8" height="15.0" fill="rgb(234,180,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_ioctl (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_ioctl (10 samples, 0.02%)</title><rect x="882.7" y="385" width="0.2" height="15.0" fill="rgb(229,132,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_lock_impl (417 samples, 0.91%)')" onmouseout="c()">
<title>libc.so.1`mutex_lock_impl (417 samples, 0.91%)</title><rect x="979.9" y="465" width="8.9" height="15.0" fill="rgb(243,35,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (799 samples, 1.74%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (799 samples, 1.74%)</title><rect x="917.4" y="417" width="17.1" height="15.0" fill="rgb(248,152,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_inject_apic_timer_irqs (15 samples, 0.03%)')" onmouseout="c()">
<title>kvm`kvm_inject_apic_timer_irqs (15 samples, 0.03%)</title><rect x="30.7" y="305" width="0.3" height="15.0" fill="rgb(230,134,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (31 samples, 0.07%)')" onmouseout="c()">
<title>kvm`vmcs_readl (31 samples, 0.07%)</title><rect x="853.8" y="289" width="0.7" height="15.0" fill="rgb(233,116,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`tsc_read (5 samples, 0.01%)</title><rect x="974.5" y="417" width="0.1" height="15.0" fill="rgb(241,73,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_32 (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`atomic_add_32 (5 samples, 0.01%)</title><rect x="38.5" y="241" width="0.1" height="15.0" fill="rgb(215,184,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mmu_topup_memory_cache (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`mmu_topup_memory_cache (7 samples, 0.02%)</title><rect x="820.5" y="241" width="0.1" height="15.0" fill="rgb(241,63,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (609 samples, 1.33%)')" onmouseout="c()">
<title>- (609 samples, 1.33%)</title><rect x="919.0" y="401" width="13.0" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_sigprocmask (41 samples, 0.09%)')" onmouseout="c()">
<title>kvm`kvm_sigprocmask (41 samples, 0.09%)</title><rect x="873.6" y="337" width="0.9" height="15.0" fill="rgb(216,115,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mmu_topup_memory_cache (34 samples, 0.07%)')" onmouseout="c()">
<title>kvm`mmu_topup_memory_cache (34 samples, 0.07%)</title><rect x="845.1" y="241" width="0.7" height="15.0" fill="rgb(229,127,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_get_cr8 (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_get_cr8 (7 samples, 0.02%)</title><rect x="43.6" y="305" width="0.1" height="15.0" fill="rgb(215,33,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmcs_readl (5 samples, 0.01%)</title><rect x="802.9" y="145" width="0.1" height="15.0" fill="rgb(251,139,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_error_pfn (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`is_error_pfn (9 samples, 0.02%)</title><rect x="818.3" y="257" width="0.2" height="15.0" fill="rgb(250,72,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c86 (7 samples, 0.02%)')" onmouseout="c()">
<title>unix`0xfffffffffb800c86 (7 samples, 0.02%)</title><rect x="932.1" y="401" width="0.2" height="15.0" fill="rgb(231,218,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmcs_readl (5 samples, 0.01%)</title><rect x="43.8" y="273" width="0.1" height="15.0" fill="rgb(238,128,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_protmode (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`is_protmode (6 samples, 0.01%)</title><rect x="786.6" y="161" width="0.1" height="15.0" fill="rgb(225,82,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`mstate_thread_onproc_time (17 samples, 0.04%)')" onmouseout="c()">
<title>genunix`mstate_thread_onproc_time (17 samples, 0.04%)</title><rect x="921.6" y="225" width="0.4" height="15.0" fill="rgb(241,165,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`lwp_park (561 samples, 1.22%)')" onmouseout="c()">
<title>genunix`lwp_park (561 samples, 1.22%)</title><rect x="919.4" y="353" width="12.0" height="15.0" fill="rgb(249,122,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`phys_page_find (7 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`phys_page_find (7 samples, 0.02%)</title><rect x="915.3" y="305" width="0.1" height="15.0" fill="rgb(215,119,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`cache_all_regs (127 samples, 0.28%)')" onmouseout="c()">
<title>kvm`cache_all_regs (127 samples, 0.28%)</title><rect x="728.7" y="241" width="2.7" height="15.0" fill="rgb(243,12,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_write_msr (20 samples, 0.04%)')" onmouseout="c()">
<title>kvm`native_write_msr (20 samples, 0.04%)</title><rect x="39.7" y="193" width="0.4" height="15.0" fill="rgb(222,112,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`skip_emulated_instruction (16 samples, 0.03%)')" onmouseout="c()">
<title>kvm`skip_emulated_instruction (16 samples, 0.03%)</title><rect x="852.3" y="273" width="0.4" height="15.0" fill="rgb(215,4,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`sys_syscall (6 samples, 0.01%)</title><rect x="974.7" y="465" width="0.1" height="15.0" fill="rgb(244,20,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (17 samples, 0.04%)')" onmouseout="c()">
<title>genunix`syscall_mstate (17 samples, 0.04%)</title><rect x="900.3" y="241" width="0.4" height="15.0" fill="rgb(205,197,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_mmio_write (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vcpu_mmio_write (10 samples, 0.02%)</title><rect x="816.4" y="193" width="0.2" height="15.0" fill="rgb(217,22,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_cas_32 (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`atomic_cas_32 (6 samples, 0.01%)</title><rect x="907.5" y="65" width="0.2" height="15.0" fill="rgb(253,193,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`sigismember (5 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`sigismember (5 samples, 0.01%)</title><rect x="989.3" y="481" width="0.1" height="15.0" fill="rgb(215,103,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_active (5 samples, 0.01%)')" onmouseout="c()">
<title>FSS`fss_active (5 samples, 0.01%)</title><rect x="904.6" y="97" width="0.1" height="15.0" fill="rgb(221,111,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`lwp_park (78 samples, 0.17%)')" onmouseout="c()">
<title>genunix`lwp_park (78 samples, 0.17%)</title><rect x="980.4" y="385" width="1.7" height="15.0" fill="rgb(252,25,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800ca0 (15 samples, 0.03%)')" onmouseout="c()">
<title>unix`0xfffffffffb800ca0 (15 samples, 0.03%)</title><rect x="887.6" y="465" width="0.3" height="15.0" fill="rgb(220,120,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_fire_urn (80 samples, 0.17%)')" onmouseout="c()">
<title>kvm`kvm_fire_urn (80 samples, 0.17%)</title><rect x="879.2" y="321" width="1.7" height="15.0" fill="rgb(250,78,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_rip_read (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_rip_read (5 samples, 0.01%)</title><rect x="850.3" y="257" width="0.1" height="15.0" fill="rgb(210,72,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`undo_watch_step (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`undo_watch_step (5 samples, 0.01%)</title><rect x="970.3" y="369" width="0.1" height="15.0" fill="rgb(211,129,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`xc_sync (9 samples, 0.02%)')" onmouseout="c()">
<title>unix`xc_sync (9 samples, 0.02%)</title><rect x="888.5" y="353" width="0.2" height="15.0" fill="rgb(210,86,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`mutex_enter (5 samples, 0.01%)</title><rect x="875.5" y="289" width="0.1" height="15.0" fill="rgb(229,105,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_alloc (10 samples, 0.02%)')" onmouseout="c()">
<title>genunix`kmem_cache_alloc (10 samples, 0.02%)</title><rect x="910.6" y="177" width="0.2" height="15.0" fill="rgb(228,207,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`htable_lookup (19 samples, 0.04%)')" onmouseout="c()">
<title>unix`htable_lookup (19 samples, 0.04%)</title><rect x="833.7" y="177" width="0.4" height="15.0" fill="rgb(209,129,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (11 samples, 0.02%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (11 samples, 0.02%)</title><rect x="930.7" y="321" width="0.2" height="15.0" fill="rgb(253,73,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`dm_request_for_irq_injection (47 samples, 0.10%)')" onmouseout="c()">
<title>kvm`dm_request_for_irq_injection (47 samples, 0.10%)</title><rect x="23.5" y="321" width="1.0" height="15.0" fill="rgb(228,136,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigpending (8 samples, 0.02%)')" onmouseout="c()">
<title>genunix`sigpending (8 samples, 0.02%)</title><rect x="975.0" y="433" width="0.2" height="15.0" fill="rgb(206,13,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_get_reg (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_get_reg (6 samples, 0.01%)</title><rect x="43.6" y="289" width="0.1" height="15.0" fill="rgb(236,68,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_is_error_hva (13 samples, 0.03%)')" onmouseout="c()">
<title>kvm`kvm_is_error_hva (13 samples, 0.03%)</title><rect x="769.0" y="97" width="0.3" height="15.0" fill="rgb(213,108,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`cpu_set_apic_tpr (8 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`cpu_set_apic_tpr (8 samples, 0.02%)</title><rect x="965.2" y="449" width="0.1" height="15.0" fill="rgb(246,29,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`pc_gethrestime (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`pc_gethrestime (8 samples, 0.02%)</title><rect x="911.1" y="161" width="0.2" height="15.0" fill="rgb(243,27,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`writeback (1,525 samples, 3.32%)')" onmouseout="c()">
<title>kvm`writeback (1,525 samples, 3.32%)</title><rect x="784.4" y="225" width="32.6" height="15.0" fill="rgb(238,59,44)" rx="2" ry="2" />
<text text-anchor="" x="787.397682220189" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kv..</text>
</g>
<g class="func_g" onmouseover="s('kvm`vmx_vcpu_load (22 samples, 0.05%)')" onmouseout="c()">
<title>kvm`vmx_vcpu_load (22 samples, 0.05%)</title><rect x="888.2" y="401" width="0.5" height="15.0" fill="rgb(245,59,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`msix_enabled (6 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`msix_enabled (6 samples, 0.01%)</title><rect x="897.2" y="401" width="0.1" height="15.0" fill="rgb(248,55,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`lduw_phys (9 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`lduw_phys (9 samples, 0.02%)</title><rect x="913.2" y="321" width="0.2" height="15.0" fill="rgb(244,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`x86_decode_insn (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`x86_decode_insn (11 samples, 0.02%)</title><rect x="847.9" y="257" width="0.2" height="15.0" fill="rgb(209,70,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_cs_db_l_bits (34 samples, 0.07%)')" onmouseout="c()">
<title>kvm`vmx_get_cs_db_l_bits (34 samples, 0.07%)</title><rect x="735.6" y="241" width="0.7" height="15.0" fill="rgb(232,13,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_exit (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`disp_lock_exit (6 samples, 0.01%)</title><rect x="904.1" y="113" width="0.1" height="15.0" fill="rgb(239,189,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('specfs`spec_ioctl (40,704 samples, 88.67%)')" onmouseout="c()">
<title>specfs`spec_ioctl (40,704 samples, 88.67%)</title><rect x="14.0" y="401" width="868.9" height="15.0" fill="rgb(252,83,23)" rx="2" ry="2" />
<text text-anchor="" x="16.9920707532784" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >specfs`spec_ioctl</text>
</g>
<g class="func_g" onmouseover="s('unix`setfrontdq (99 samples, 0.22%)')" onmouseout="c()">
<title>unix`setfrontdq (99 samples, 0.22%)</title><rect x="906.3" y="81" width="2.2" height="15.0" fill="rgb(216,123,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_read16 (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_read16 (9 samples, 0.02%)</title><rect x="802.7" y="145" width="0.2" height="15.0" fill="rgb(208,117,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`do_interrupt (461 samples, 1.00%)')" onmouseout="c()">
<title>unix`do_interrupt (461 samples, 1.00%)</title><rect x="859.2" y="289" width="9.9" height="15.0" fill="rgb(229,124,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`pre_kvm_run (16 samples, 0.03%)')" onmouseout="c()">
<title>qemu-system-x86_64`pre_kvm_run (16 samples, 0.03%)</title><rect x="966.6" y="465" width="0.3" height="15.0" fill="rgb(242,200,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (9 samples, 0.02%)')" onmouseout="c()">
<title>unix`tsc_read (9 samples, 0.02%)</title><rect x="930.7" y="305" width="0.2" height="15.0" fill="rgb(211,143,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_enter (10 samples, 0.02%)</title><rect x="41.1" y="289" width="0.2" height="15.0" fill="rgb(214,149,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_register_read (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_register_read (6 samples, 0.01%)</title><rect x="776.2" y="209" width="0.1" height="15.0" fill="rgb(235,159,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`cpu_physical_memory_is_dirty (8 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`cpu_physical_memory_is_dirty (8 samples, 0.02%)</title><rect x="915.7" y="289" width="0.2" height="15.0" fill="rgb(222,171,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_lock_queue (22 samples, 0.05%)')" onmouseout="c()">
<title>libc.so.1`mutex_lock_queue (22 samples, 0.05%)</title><rect x="982.5" y="449" width="0.5" height="15.0" fill="rgb(246,60,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (83 samples, 0.18%)')" onmouseout="c()">
<title>- (83 samples, 0.18%)</title><rect x="980.4" y="433" width="1.8" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (21 samples, 0.05%)')" onmouseout="c()">
<title>- (21 samples, 0.05%)</title><rect x="894.5" y="449" width="0.5" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bcopy (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`bcopy (11 samples, 0.02%)</title><rect x="817.6" y="225" width="0.2" height="15.0" fill="rgb(247,169,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_rflags (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmx_get_rflags (7 samples, 0.02%)</title><rect x="43.8" y="289" width="0.1" height="15.0" fill="rgb(252,210,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`do_fetch_insn_byte (1,293 samples, 2.82%)')" onmouseout="c()">
<title>kvm`do_fetch_insn_byte (1,293 samples, 2.82%)</title><rect x="748.2" y="209" width="27.6" height="15.0" fill="rgb(229,178,50)" rx="2" ry="2" />
<text text-anchor="" x="751.234217749314" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`virtio_pci_config_writew (895 samples, 1.95%)')" onmouseout="c()">
<title>qemu-system-x86_64`virtio_pci_config_writew (895 samples, 1.95%)</title><rect x="897.1" y="417" width="19.1" height="15.0" fill="rgb(241,213,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`splr (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`splr (5 samples, 0.01%)</title><rect x="932.7" y="353" width="0.1" height="15.0" fill="rgb(250,15,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_arch_post_run (12 samples, 0.03%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_arch_post_run (12 samples, 0.03%)</title><rect x="895.8" y="465" width="0.3" height="15.0" fill="rgb(240,94,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (5 samples, 0.01%)</title><rect x="900.6" y="225" width="0.1" height="15.0" fill="rgb(236,74,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`ioctl (40,781 samples, 88.84%)')" onmouseout="c()">
<title>genunix`ioctl (40,781 samples, 88.84%)</title><rect x="13.3" y="433" width="870.6" height="15.0" fill="rgb(252,128,18)" rx="2" ry="2" />
<text text-anchor="" x="16.3302836230558" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`ioctl</text>
</g>
<g class="func_g" onmouseover="s('unix`do_splx (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`do_splx (8 samples, 0.02%)</title><rect x="888.5" y="321" width="0.2" height="15.0" fill="rgb(214,221,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_block (148 samples, 0.32%)')" onmouseout="c()">
<title>genunix`cv_block (148 samples, 0.32%)</title><rect x="920.4" y="289" width="3.1" height="15.0" fill="rgb(229,191,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`do_insn_fetch (1,339 samples, 2.92%)')" onmouseout="c()">
<title>kvm`do_insn_fetch (1,339 samples, 2.92%)</title><rect x="747.6" y="225" width="28.6" height="15.0" fill="rgb(250,9,20)" rx="2" ry="2" />
<text text-anchor="" x="750.572430619091" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kv..</text>
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_writel (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmcs_writel (5 samples, 0.01%)</title><rect x="859.1" y="289" width="0.1" height="15.0" fill="rgb(252,211,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_has_pending_timer (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`apic_has_pending_timer (7 samples, 0.02%)</title><rect x="23.3" y="321" width="0.2" height="15.0" fill="rgb(224,119,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (24 samples, 0.05%)')" onmouseout="c()">
<title>kvm`vmcs_readl (24 samples, 0.05%)</title><rect x="730.2" y="193" width="0.5" height="15.0" fill="rgb(219,136,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`x86pte_release_pagetable (7 samples, 0.02%)')" onmouseout="c()">
<title>unix`x86pte_release_pagetable (7 samples, 0.02%)</title><rect x="833.6" y="145" width="0.1" height="15.0" fill="rgb(239,94,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_hw_enabled (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`apic_hw_enabled (14 samples, 0.03%)</title><rect x="805.0" y="129" width="0.3" height="15.0" fill="rgb(233,6,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_interrupt_window (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`handle_interrupt_window (10 samples, 0.02%)</title><rect x="850.5" y="289" width="0.2" height="15.0" fill="rgb(244,94,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`sigpending (9 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`sigpending (9 samples, 0.02%)</title><rect x="10.9" y="497" width="0.2" height="15.0" fill="rgb(239,5,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`do_splx (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`do_splx (8 samples, 0.02%)</title><rect x="886.5" y="417" width="0.2" height="15.0" fill="rgb(240,28,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`pollnotify (289 samples, 0.63%)')" onmouseout="c()">
<title>genunix`pollnotify (289 samples, 0.63%)</title><rect x="902.5" y="145" width="6.2" height="15.0" fill="rgb(243,72,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c86 (17 samples, 0.04%)')" onmouseout="c()">
<title>unix`0xfffffffffb800c86 (17 samples, 0.04%)</title><rect x="885.4" y="465" width="0.4" height="15.0" fill="rgb(209,43,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_find_highest_isr (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`apic_find_highest_isr (7 samples, 0.02%)</title><rect x="42.6" y="241" width="0.1" height="15.0" fill="rgb(241,206,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_unlock (7 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`mutex_unlock (7 samples, 0.02%)</title><rect x="10.4" y="497" width="0.2" height="15.0" fill="rgb(244,22,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_cr0_bits (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_read_cr0_bits (9 samples, 0.02%)</title><rect x="732.2" y="225" width="0.2" height="15.0" fill="rgb(236,165,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_is_error_hva (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_is_error_hva (6 samples, 0.01%)</title><rect x="799.6" y="97" width="0.1" height="15.0" fill="rgb(241,130,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_enter (10 samples, 0.02%)</title><rect x="883.7" y="417" width="0.2" height="15.0" fill="rgb(247,123,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (201 samples, 0.44%)')" onmouseout="c()">
<title>- (201 samples, 0.44%)</title><rect x="968.2" y="465" width="4.3" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_trylock_adaptive (268 samples, 0.58%)')" onmouseout="c()">
<title>libc.so.1`mutex_trylock_adaptive (268 samples, 0.58%)</title><rect x="983.0" y="449" width="5.7" height="15.0" fill="rgb(213,142,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`unalias_gfn_instantiation (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`unalias_gfn_instantiation (5 samples, 0.01%)</title><rect x="769.3" y="97" width="0.1" height="15.0" fill="rgb(237,185,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fifofs`fifo_wakereader (336 samples, 0.73%)')" onmouseout="c()">
<title>fifofs`fifo_wakereader (336 samples, 0.73%)</title><rect x="902.3" y="193" width="7.2" height="15.0" fill="rgb(254,192,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`memcpy (11 samples, 0.02%)')" onmouseout="c()">
<title>genunix`memcpy (11 samples, 0.02%)</title><rect x="742.6" y="225" width="0.2" height="15.0" fill="rgb(220,179,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_load_guest_fpu (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`kvm_load_guest_fpu (18 samples, 0.04%)</title><rect x="32.1" y="321" width="0.3" height="15.0" fill="rgb(254,20,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_xcall (15 samples, 0.03%)')" onmouseout="c()">
<title>kvm`kvm_xcall (15 samples, 0.03%)</title><rect x="876.2" y="273" width="0.3" height="15.0" fill="rgb(213,196,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_rflags (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmx_get_rflags (5 samples, 0.01%)</title><rect x="774.9" y="177" width="0.1" height="15.0" fill="rgb(244,181,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`do_fetch_insn_byte (59 samples, 0.13%)')" onmouseout="c()">
<title>kvm`do_fetch_insn_byte (59 samples, 0.13%)</title><rect x="745.7" y="193" width="1.3" height="15.0" fill="rgb(227,107,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_read_msr (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`native_read_msr (10 samples, 0.02%)</title><rect x="720.2" y="305" width="0.2" height="15.0" fill="rgb(211,208,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`x86pte_mapin (27 samples, 0.06%)')" onmouseout="c()">
<title>unix`x86pte_mapin (27 samples, 0.06%)</title><rect x="832.9" y="129" width="0.6" height="15.0" fill="rgb(233,175,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`copyout_siginfo (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`copyout_siginfo (9 samples, 0.02%)</title><rect x="968.8" y="417" width="0.2" height="15.0" fill="rgb(249,50,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (13 samples, 0.03%)')" onmouseout="c()">
<title>genunix`syscall_mstate (13 samples, 0.03%)</title><rect x="919.0" y="369" width="0.3" height="15.0" fill="rgb(210,116,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`pollwakeup (307 samples, 0.67%)')" onmouseout="c()">
<title>genunix`pollwakeup (307 samples, 0.67%)</title><rect x="902.3" y="161" width="6.6" height="15.0" fill="rgb(252,89,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_handle_io (934 samples, 2.03%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_handle_io (934 samples, 2.03%)</title><rect x="896.5" y="465" width="20.0" height="15.0" fill="rgb(244,56,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`splr (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`splr (6 samples, 0.01%)</title><rect x="892.8" y="353" width="0.1" height="15.0" fill="rgb(243,80,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`_lwp_start (45,906 samples, 100.00%)')" onmouseout="c()">
<title>libc.so.1`_lwp_start (45,906 samples, 100.00%)</title><rect x="10.0" y="561" width="980.0" height="15.0" fill="rgb(251,73,54)" rx="2" ry="2" />
<text text-anchor="" x="13" y="571.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`_lwp_start</text>
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (20 samples, 0.04%)')" onmouseout="c()">
<title>unix`atomic_add_64 (20 samples, 0.04%)</title><rect x="884.6" y="433" width="0.5" height="15.0" fill="rgb(234,96,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_arch_vcpu_runnable (63 samples, 0.14%)')" onmouseout="c()">
<title>kvm`kvm_arch_vcpu_runnable (63 samples, 0.14%)</title><rect x="41.5" y="305" width="1.4" height="15.0" fill="rgb(247,203,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (7 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_enter (7 samples, 0.02%)</title><rect x="908.9" y="161" width="0.1" height="15.0" fill="rgb(250,45,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_ioctl (40,666 samples, 88.59%)')" onmouseout="c()">
<title>kvm`kvm_ioctl (40,666 samples, 88.59%)</title><rect x="14.6" y="369" width="868.1" height="15.0" fill="rgb(232,36,38)" rx="2" ry="2" />
<text text-anchor="" x="17.5898139676731" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm`kvm_ioctl</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`enqueue (7 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`enqueue (7 samples, 0.02%)</title><rect x="935.2" y="401" width="0.1" height="15.0" fill="rgb(233,174,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmcs_readl (6 samples, 0.01%)</title><rect x="729.7" y="209" width="0.2" height="15.0" fill="rgb(243,7,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gpte_to_gfn_lvl (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`paging64_gpte_to_gfn_lvl (18 samples, 0.04%)</title><rect x="756.3" y="145" width="0.4" height="15.0" fill="rgb(227,166,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_read32 (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`vmcs_read32 (18 samples, 0.04%)</title><rect x="735.8" y="225" width="0.4" height="15.0" fill="rgb(251,147,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gpte_access (13 samples, 0.03%)')" onmouseout="c()">
<title>kvm`paging64_gpte_access (13 samples, 0.03%)</title><rect x="788.0" y="145" width="0.3" height="15.0" fill="rgb(223,4,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_vcpu_load (27 samples, 0.06%)')" onmouseout="c()">
<title>kvm`vmx_vcpu_load (27 samples, 0.06%)</title><rect x="876.0" y="305" width="0.5" height="15.0" fill="rgb(217,89,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_walk_addr (746 samples, 1.63%)')" onmouseout="c()">
<title>kvm`paging64_walk_addr (746 samples, 1.63%)</title><rect x="756.7" y="145" width="15.9" height="15.0" fill="rgb(217,60,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_rip_read (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_rip_read (6 samples, 0.01%)</title><rect x="852.4" y="257" width="0.1" height="15.0" fill="rgb(248,152,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`__set_bit (16 samples, 0.03%)')" onmouseout="c()">
<title>kvm`__set_bit (16 samples, 0.03%)</title><rect x="784.0" y="209" width="0.3" height="15.0" fill="rgb(238,114,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`ioport_write (912 samples, 1.99%)')" onmouseout="c()">
<title>qemu-system-x86_64`ioport_write (912 samples, 1.99%)</title><rect x="896.7" y="433" width="19.5" height="15.0" fill="rgb(233,24,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c86 (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`0xfffffffffb800c86 (5 samples, 0.01%)</title><rect x="972.9" y="465" width="0.1" height="15.0" fill="rgb(240,18,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_mmio_in_range (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`apic_mmio_in_range (11 samples, 0.02%)</title><rect x="804.4" y="145" width="0.2" height="15.0" fill="rgb(224,75,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest_virt_helper (29 samples, 0.06%)')" onmouseout="c()">
<title>kvm`kvm_read_guest_virt_helper (29 samples, 0.06%)</title><rect x="775.0" y="193" width="0.7" height="15.0" fill="rgb(207,102,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`pit_ioport_write (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`pit_ioport_write (5 samples, 0.01%)</title><rect x="851.6" y="209" width="0.1" height="15.0" fill="rgb(225,171,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_sleep (11 samples, 0.02%)')" onmouseout="c()">
<title>FSS`fss_sleep (11 samples, 0.02%)</title><rect x="980.5" y="305" width="0.3" height="15.0" fill="rgb(210,13,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`_resume_from_idle (64 samples, 0.14%)')" onmouseout="c()">
<title>unix`_resume_from_idle (64 samples, 0.14%)</title><rect x="887.9" y="465" width="1.3" height="15.0" fill="rgb(240,90,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_inactive (8 samples, 0.02%)')" onmouseout="c()">
<title>FSS`fss_inactive (8 samples, 0.02%)</title><rect x="980.5" y="289" width="0.2" height="15.0" fill="rgb(214,131,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`test_and_clear_bit (273 samples, 0.59%)')" onmouseout="c()">
<title>kvm`test_and_clear_bit (273 samples, 0.59%)</title><rect x="44.0" y="321" width="5.8" height="15.0" fill="rgb(242,98,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`cpu_has_virtual_nmis (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`cpu_has_virtual_nmis (8 samples, 0.02%)</title><rect x="710.4" y="305" width="0.2" height="15.0" fill="rgb(241,126,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_cpu_has_pending_timer (241 samples, 0.52%)')" onmouseout="c()">
<title>kvm`kvm_cpu_has_pending_timer (241 samples, 0.52%)</title><rect x="25.2" y="321" width="5.1" height="15.0" fill="rgb(243,208,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`issig_justlooking (192 samples, 0.42%)')" onmouseout="c()">
<title>genunix`issig_justlooking (192 samples, 0.42%)</title><rect x="18.8" y="305" width="4.1" height="15.0" fill="rgb(233,213,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_mmio_in_range (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`apic_mmio_in_range (18 samples, 0.04%)</title><rect x="805.3" y="129" width="0.3" height="15.0" fill="rgb(217,27,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`no_preempt (5 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`no_preempt (5 samples, 0.01%)</title><rect x="936.3" y="369" width="0.1" height="15.0" fill="rgb(224,14,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cpu_decay (7 samples, 0.02%)')" onmouseout="c()">
<title>genunix`cpu_decay (7 samples, 0.02%)</title><rect x="907.3" y="49" width="0.2" height="15.0" fill="rgb(226,129,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`__vmx_load_host_state (32 samples, 0.07%)')" onmouseout="c()">
<title>kvm`__vmx_load_host_state (32 samples, 0.07%)</title><rect x="878.0" y="289" width="0.6" height="15.0" fill="rgb(205,17,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`phys_page_find (26 samples, 0.06%)')" onmouseout="c()">
<title>qemu-system-x86_64`phys_page_find (26 samples, 0.06%)</title><rect x="914.0" y="289" width="0.6" height="15.0" fill="rgb(246,219,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_preempt (9 samples, 0.02%)')" onmouseout="c()">
<title>FSS`fss_preempt (9 samples, 0.02%)</title><rect x="887.1" y="417" width="0.2" height="15.0" fill="rgb(222,172,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_exit_nopreempt (11 samples, 0.02%)')" onmouseout="c()">
<title>genunix`disp_lock_exit_nopreempt (11 samples, 0.02%)</title><rect x="923.5" y="289" width="0.3" height="15.0" fill="rgb(219,89,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sysconfig (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`sysconfig (5 samples, 0.01%)</title><rect x="894.8" y="417" width="0.1" height="15.0" fill="rgb(227,144,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`apic_update_irq (17 samples, 0.04%)')" onmouseout="c()">
<title>qemu-system-x86_64`apic_update_irq (17 samples, 0.04%)</title><rect x="966.1" y="417" width="0.3" height="15.0" fill="rgb(246,47,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`sigtimedwait (14 samples, 0.03%)')" onmouseout="c()">
<title>libc.so.1`sigtimedwait (14 samples, 0.03%)</title><rect x="989.4" y="481" width="0.3" height="15.0" fill="rgb(212,3,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`gdt_ucode_model (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`gdt_ucode_model (8 samples, 0.02%)</title><rect x="933.6" y="353" width="0.2" height="15.0" fill="rgb(217,141,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`do_fetch_insn_byte (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`do_fetch_insn_byte (6 samples, 0.01%)</title><rect x="747.4" y="225" width="0.2" height="15.0" fill="rgb(250,128,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_wait_sig_swap (373 samples, 0.81%)')" onmouseout="c()">
<title>genunix`cv_wait_sig_swap (373 samples, 0.81%)</title><rect x="33.4" y="305" width="8.0" height="15.0" fill="rgb(234,157,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_unlock_queue (5 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`mutex_unlock_queue (5 samples, 0.01%)</title><rect x="989.0" y="465" width="0.1" height="15.0" fill="rgb(237,50,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c91 (84 samples, 0.18%)')" onmouseout="c()">
<title>unix`0xfffffffffb800c91 (84 samples, 0.18%)</title><rect x="885.8" y="465" width="1.8" height="15.0" fill="rgb(209,168,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`x86pte_get (79 samples, 0.17%)')" onmouseout="c()">
<title>unix`x86pte_get (79 samples, 0.17%)</title><rect x="832.0" y="161" width="1.7" height="15.0" fill="rgb(234,184,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`write (9 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`write (9 samples, 0.02%)</title><rect x="912.8" y="289" width="0.2" height="15.0" fill="rgb(234,54,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_is_error_hva (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_is_error_hva (8 samples, 0.02%)</title><rect x="836.2" y="241" width="0.2" height="15.0" fill="rgb(248,228,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`splr (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`splr (8 samples, 0.02%)</title><rect x="886.7" y="417" width="0.2" height="15.0" fill="rgb(239,225,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`virtio_queue_set_notification (135 samples, 0.29%)')" onmouseout="c()">
<title>qemu-system-x86_64`virtio_queue_set_notification (135 samples, 0.29%)</title><rect x="913.1" y="337" width="2.9" height="15.0" fill="rgb(241,216,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_on_user_return (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_on_user_return (6 samples, 0.01%)</title><rect x="39.5" y="193" width="0.2" height="15.0" fill="rgb(251,195,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`new_mstate (46 samples, 0.10%)')" onmouseout="c()">
<title>genunix`new_mstate (46 samples, 0.10%)</title><rect x="930.1" y="337" width="0.9" height="15.0" fill="rgb(234,39,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot (303 samples, 0.66%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot (303 samples, 0.66%)</title><rect x="836.9" y="225" width="6.5" height="15.0" fill="rgb(250,111,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigaddqa (49 samples, 0.11%)')" onmouseout="c()">
<title>genunix`sigaddqa (49 samples, 0.11%)</title><rect x="892.2" y="401" width="1.0" height="15.0" fill="rgb(205,123,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_sw_enabled (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`apic_sw_enabled (11 samples, 0.02%)</title><rect x="28.3" y="289" width="0.3" height="15.0" fill="rgb(211,52,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_hva (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`gfn_to_hva (6 samples, 0.01%)</title><rect x="794.3" y="113" width="0.1" height="15.0" fill="rgb(210,48,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (20 samples, 0.04%)')" onmouseout="c()">
<title>genunix`syscall_mstate (20 samples, 0.04%)</title><rect x="975.2" y="433" width="0.4" height="15.0" fill="rgb(238,211,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot (22 samples, 0.05%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot (22 samples, 0.05%)</title><rect x="821.7" y="241" width="0.4" height="15.0" fill="rgb(239,116,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`issig (220 samples, 0.48%)')" onmouseout="c()">
<title>genunix`issig (220 samples, 0.48%)</title><rect x="18.6" y="321" width="4.7" height="15.0" fill="rgb(228,229,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_ept_violation (19 samples, 0.04%)')" onmouseout="c()">
<title>kvm`handle_ept_violation (19 samples, 0.04%)</title><rect x="710.6" y="305" width="0.4" height="15.0" fill="rgb(241,164,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`qemu_event_increment (631 samples, 1.37%)')" onmouseout="c()">
<title>qemu-system-x86_64`qemu_event_increment (631 samples, 1.37%)</title><rect x="899.5" y="305" width="13.5" height="15.0" fill="rgb(214,38,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`tsc_read (5 samples, 0.01%)</title><rect x="893.7" y="385" width="0.1" height="15.0" fill="rgb(221,47,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_set_eoi (225 samples, 0.49%)')" onmouseout="c()">
<title>kvm`apic_set_eoi (225 samples, 0.49%)</title><rect x="806.6" y="113" width="4.8" height="15.0" fill="rgb(234,166,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`pc_gethrestime (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`pc_gethrestime (5 samples, 0.01%)</title><rect x="970.6" y="385" width="0.1" height="15.0" fill="rgb(227,128,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_arch_vcpu_load (38 samples, 0.08%)')" onmouseout="c()">
<title>kvm`kvm_arch_vcpu_load (38 samples, 0.08%)</title><rect x="875.7" y="321" width="0.8" height="15.0" fill="rgb(209,177,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (19 samples, 0.04%)')" onmouseout="c()">
<title>genunix`syscall_mstate (19 samples, 0.04%)</title><rect x="971.8" y="433" width="0.4" height="15.0" fill="rgb(214,173,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`emulate_instruction (36 samples, 0.08%)')" onmouseout="c()">
<title>kvm`emulate_instruction (36 samples, 0.08%)</title><rect x="723.9" y="273" width="0.7" height="15.0" fill="rgb(211,23,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_lapic_sync_from_vapic (227 samples, 0.49%)')" onmouseout="c()">
<title>kvm`kvm_lapic_sync_from_vapic (227 samples, 0.49%)</title><rect x="713.0" y="305" width="4.8" height="15.0" fill="rgb(205,29,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`decode_register (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`decode_register (8 samples, 0.02%)</title><rect x="747.0" y="225" width="0.1" height="15.0" fill="rgb(229,217,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gpte_access (22 samples, 0.05%)')" onmouseout="c()">
<title>kvm`paging64_gpte_access (22 samples, 0.05%)</title><rect x="801.9" y="129" width="0.5" height="15.0" fill="rgb(242,197,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_rflags (30 samples, 0.07%)')" onmouseout="c()">
<title>kvm`vmx_get_rflags (30 samples, 0.07%)</title><rect x="803.0" y="145" width="0.7" height="15.0" fill="rgb(217,119,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`fpxsave_ctxt (28 samples, 0.06%)')" onmouseout="c()">
<title>unix`fpxsave_ctxt (28 samples, 0.06%)</title><rect x="40.3" y="241" width="0.6" height="15.0" fill="rgb(237,172,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_read16 (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmcs_read16 (7 samples, 0.02%)</title><rect x="773.6" y="177" width="0.1" height="15.0" fill="rgb(227,185,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_mmu_gva_to_gpa_write (814 samples, 1.77%)')" onmouseout="c()">
<title>kvm`kvm_mmu_gva_to_gpa_write (814 samples, 1.77%)</title><rect x="786.3" y="177" width="17.4" height="15.0" fill="rgb(223,218,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`seg_base (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`seg_base (6 samples, 0.01%)</title><rect x="735.3" y="241" width="0.1" height="15.0" fill="rgb(243,125,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_rip_write (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_rip_write (11 samples, 0.02%)</title><rect x="734.1" y="241" width="0.2" height="15.0" fill="rgb(209,78,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`mutex_enter (5 samples, 0.01%)</title><rect x="870.2" y="321" width="0.1" height="15.0" fill="rgb(225,127,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`irqchip_in_kernel (24 samples, 0.05%)')" onmouseout="c()">
<title>kvm`irqchip_in_kernel (24 samples, 0.05%)</title><rect x="24.5" y="321" width="0.5" height="15.0" fill="rgb(233,97,16)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_read_guest (11 samples, 0.02%)</title><rect x="749.2" y="177" width="0.2" height="15.0" fill="rgb(215,190,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_get_reg (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_get_reg (6 samples, 0.01%)</title><rect x="28.0" y="289" width="0.1" height="15.0" fill="rgb(216,136,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`issig_forreal (41 samples, 0.09%)')" onmouseout="c()">
<title>genunix`issig_forreal (41 samples, 0.09%)</title><rect x="969.4" y="369" width="0.9" height="15.0" fill="rgb(231,186,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (15 samples, 0.03%)')" onmouseout="c()">
<title>unix`mutex_enter (15 samples, 0.03%)</title><rect x="834.5" y="177" width="0.3" height="15.0" fill="rgb(222,141,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`do_splx (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`do_splx (6 samples, 0.01%)</title><rect x="904.1" y="97" width="0.1" height="15.0" fill="rgb(229,36,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (28 samples, 0.06%)')" onmouseout="c()">
<title>unix`mutex_enter (28 samples, 0.06%)</title><rect x="842.6" y="209" width="0.6" height="15.0" fill="rgb(210,168,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`to_lapic (21 samples, 0.05%)')" onmouseout="c()">
<title>kvm`to_lapic (21 samples, 0.05%)</title><rect x="815.7" y="145" width="0.4" height="15.0" fill="rgb(218,179,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`sigtimedwait (11 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`sigtimedwait (11 samples, 0.02%)</title><rect x="11.1" y="497" width="0.2" height="15.0" fill="rgb(221,177,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_waituntil_sig (480 samples, 1.05%)')" onmouseout="c()">
<title>genunix`cv_waituntil_sig (480 samples, 1.05%)</title><rect x="919.8" y="337" width="10.2" height="15.0" fill="rgb(247,173,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_walk_addr (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`paging64_walk_addr (11 samples, 0.02%)</title><rect x="772.6" y="161" width="0.2" height="15.0" fill="rgb(209,113,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (9 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_enter (9 samples, 0.02%)</title><rect x="911.5" y="209" width="0.2" height="15.0" fill="rgb(212,205,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_guest_exit (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_guest_exit (8 samples, 0.02%)</title><rect x="30.6" y="321" width="0.1" height="15.0" fill="rgb(219,105,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`releasef (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`releasef (9 samples, 0.02%)</title><rect x="883.3" y="417" width="0.2" height="15.0" fill="rgb(224,14,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (32 samples, 0.07%)')" onmouseout="c()">
<title>genunix`syscall_mstate (32 samples, 0.07%)</title><rect x="884.0" y="433" width="0.6" height="15.0" fill="rgb(214,158,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigtoproc (44 samples, 0.10%)')" onmouseout="c()">
<title>genunix`sigtoproc (44 samples, 0.10%)</title><rect x="892.3" y="385" width="0.9" height="15.0" fill="rgb(250,171,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest (124 samples, 0.27%)')" onmouseout="c()">
<title>kvm`kvm_read_guest (124 samples, 0.27%)</title><rect x="750.0" y="161" width="2.7" height="15.0" fill="rgb(213,112,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mmu_topup_memory_caches (41 samples, 0.09%)')" onmouseout="c()">
<title>kvm`mmu_topup_memory_caches (41 samples, 0.09%)</title><rect x="819.8" y="257" width="0.8" height="15.0" fill="rgb(239,133,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`post_syscall (31 samples, 0.07%)')" onmouseout="c()">
<title>genunix`post_syscall (31 samples, 0.07%)</title><rect x="932.4" y="385" width="0.6" height="15.0" fill="rgb(223,117,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigorset (12 samples, 0.03%)')" onmouseout="c()">
<title>genunix`sigorset (12 samples, 0.03%)</title><rect x="22.6" y="289" width="0.3" height="15.0" fill="rgb(222,90,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`do_splx (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`do_splx (10 samples, 0.02%)</title><rect x="923.6" y="273" width="0.2" height="15.0" fill="rgb(224,201,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`next_segment (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`next_segment (11 samples, 0.02%)</title><rect x="801.6" y="129" width="0.3" height="15.0" fill="rgb(243,113,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_put_guest_fpu (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_put_guest_fpu (8 samples, 0.02%)</title><rect x="877.8" y="305" width="0.1" height="15.0" fill="rgb(206,6,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_write_msr (19 samples, 0.04%)')" onmouseout="c()">
<title>kvm`native_write_msr (19 samples, 0.04%)</title><rect x="720.4" y="305" width="0.4" height="15.0" fill="rgb(225,15,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_inactive (19 samples, 0.04%)')" onmouseout="c()">
<title>FSS`fss_inactive (19 samples, 0.04%)</title><rect x="920.5" y="257" width="0.4" height="15.0" fill="rgb(231,138,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_handle_exit (24 samples, 0.05%)')" onmouseout="c()">
<title>kvm`vmx_handle_exit (24 samples, 0.05%)</title><rect x="869.1" y="321" width="0.5" height="15.0" fill="rgb(253,205,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_arch_post_run (59 samples, 0.13%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_arch_post_run (59 samples, 0.13%)</title><rect x="965.3" y="449" width="1.3" height="15.0" fill="rgb(246,157,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`av_dispatch_autovect (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`av_dispatch_autovect (5 samples, 0.01%)</title><rect x="869.0" y="257" width="0.1" height="15.0" fill="rgb(222,124,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`cpucaps_charge (35 samples, 0.08%)')" onmouseout="c()">
<title>unix`cpucaps_charge (35 samples, 0.08%)</title><rect x="34.5" y="241" width="0.7" height="15.0" fill="rgb(230,134,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_external_interrupt (19 samples, 0.04%)')" onmouseout="c()">
<title>kvm`handle_external_interrupt (19 samples, 0.04%)</title><rect x="849.7" y="289" width="0.4" height="15.0" fill="rgb(236,25,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`spin_lock_set (6 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`spin_lock_set (6 samples, 0.01%)</title><rect x="982.8" y="417" width="0.1" height="15.0" fill="rgb(214,86,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cpu_update_pct (12 samples, 0.03%)')" onmouseout="c()">
<title>genunix`cpu_update_pct (12 samples, 0.03%)</title><rect x="922.2" y="257" width="0.3" height="15.0" fill="rgb(214,38,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`kmem_alloc (14 samples, 0.03%)')" onmouseout="c()">
<title>genunix`kmem_alloc (14 samples, 0.03%)</title><rect x="875.3" y="305" width="0.3" height="15.0" fill="rgb(240,130,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_wait_sig_swap_core (468 samples, 1.02%)')" onmouseout="c()">
<title>genunix`cv_wait_sig_swap_core (468 samples, 1.02%)</title><rect x="919.8" y="305" width="10.0" height="15.0" fill="rgb(235,52,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('fifofs`fifo_write (446 samples, 0.97%)')" onmouseout="c()">
<title>fifofs`fifo_write (446 samples, 0.97%)</title><rect x="901.8" y="209" width="9.6" height="15.0" fill="rgb(234,53,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gva_to_gpa (34 samples, 0.07%)')" onmouseout="c()">
<title>kvm`paging64_gva_to_gpa (34 samples, 0.07%)</title><rect x="772.8" y="177" width="0.8" height="15.0" fill="rgb(233,72,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`emulator_write_emulated_onepage (1,445 samples, 3.15%)')" onmouseout="c()">
<title>kvm`emulator_write_emulated_onepage (1,445 samples, 3.15%)</title><rect x="785.3" y="193" width="30.9" height="15.0" fill="rgb(249,156,13)" rx="2" ry="2" />
<text text-anchor="" x="788.336992985666" y="203.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kv..</text>
</g>
<g class="func_g" onmouseover="s('kvm`is_protmode (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`is_protmode (14 samples, 0.03%)</title><rect x="818.5" y="257" width="0.3" height="15.0" fill="rgb(229,178,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (70 samples, 0.15%)')" onmouseout="c()">
<title>unix`mutex_enter (70 samples, 0.15%)</title><rect x="797.5" y="81" width="1.5" height="15.0" fill="rgb(240,218,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_run (3,605 samples, 7.85%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_run (3,605 samples, 7.85%)</title><rect x="890.1" y="481" width="77.0" height="15.0" fill="rgb(252,95,31)" rx="2" ry="2" />
<text text-anchor="" x="893.112839280268" y="491.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >qemu-sys..</text>
</g>
<g class="func_g" onmouseover="s('kvm`kvm_iodevice_write (563 samples, 1.23%)')" onmouseout="c()">
<title>kvm`kvm_iodevice_write (563 samples, 1.23%)</title><rect x="804.1" y="161" width="12.0" height="15.0" fill="rgb(254,69,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`kmem_cache_free (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`kmem_cache_free (5 samples, 0.01%)</title><rect x="877.5" y="289" width="0.1" height="15.0" fill="rgb(211,11,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_signal (264 samples, 0.58%)')" onmouseout="c()">
<title>genunix`cv_signal (264 samples, 0.58%)</title><rect x="903.0" y="129" width="5.7" height="15.0" fill="rgb(220,42,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_exit_nopreempt (8 samples, 0.02%)')" onmouseout="c()">
<title>genunix`disp_lock_exit_nopreempt (8 samples, 0.02%)</title><rect x="36.0" y="273" width="0.2" height="15.0" fill="rgb(229,60,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`pic_irqchip (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`pic_irqchip (6 samples, 0.01%)</title><rect x="720.8" y="305" width="0.1" height="15.0" fill="rgb(211,47,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`sysconf (5 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`sysconf (5 samples, 0.01%)</title><rect x="889.8" y="481" width="0.1" height="15.0" fill="rgb(237,7,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrestime (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`gethrestime (6 samples, 0.01%)</title><rect x="970.5" y="401" width="0.2" height="15.0" fill="rgb(218,200,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`ap_main_loop (45,906 samples, 100.00%)')" onmouseout="c()">
<title>qemu-system-x86_64`ap_main_loop (45,906 samples, 100.00%)</title><rect x="10.0" y="529" width="980.0" height="15.0" fill="rgb(209,105,24)" rx="2" ry="2" />
<text text-anchor="" x="13" y="539.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >qemu-system-x86_64`ap_main_loop</text>
</g>
<g class="func_g" onmouseover="s('unix`lock_set_spl (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`lock_set_spl (11 samples, 0.02%)</title><rect x="37.1" y="225" width="0.2" height="15.0" fill="rgb(230,69,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_arch_vcpu_load (24 samples, 0.05%)')" onmouseout="c()">
<title>kvm`kvm_arch_vcpu_load (24 samples, 0.05%)</title><rect x="888.2" y="417" width="0.5" height="15.0" fill="rgb(228,191,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_set (14 samples, 0.03%)')" onmouseout="c()">
<title>unix`lock_set (14 samples, 0.03%)</title><rect x="923.2" y="273" width="0.3" height="15.0" fill="rgb(207,172,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_irqchip_in_kernel (8 samples, 0.02%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_irqchip_in_kernel (8 samples, 0.02%)</title><rect x="965.7" y="417" width="0.2" height="15.0" fill="rgb(253,31,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gpte_access (23 samples, 0.05%)')" onmouseout="c()">
<title>kvm`paging64_gpte_access (23 samples, 0.05%)</title><rect x="772.1" y="129" width="0.5" height="15.0" fill="rgb(236,11,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`tdp_page_fault (23 samples, 0.05%)')" onmouseout="c()">
<title>kvm`tdp_page_fault (23 samples, 0.05%)</title><rect x="848.5" y="273" width="0.5" height="15.0" fill="rgb(218,203,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_wait_sig_swap (476 samples, 1.04%)')" onmouseout="c()">
<title>genunix`cv_wait_sig_swap (476 samples, 1.04%)</title><rect x="919.8" y="321" width="10.2" height="15.0" fill="rgb(213,38,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmcs_readl (5 samples, 0.01%)</title><rect x="732.6" y="225" width="0.1" height="15.0" fill="rgb(240,174,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest_virt_helper (1,096 samples, 2.39%)')" onmouseout="c()">
<title>kvm`kvm_read_guest_virt_helper (1,096 samples, 2.39%)</title><rect x="749.4" y="177" width="23.4" height="15.0" fill="rgb(229,154,6)" rx="2" ry="2" />
<text text-anchor="" x="752.429704178103" y="187.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >k..</text>
</g>
<g class="func_g" onmouseover="s('kvm`cache_all_regs (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`cache_all_regs (8 samples, 0.02%)</title><rect x="725.1" y="257" width="0.1" height="15.0" fill="rgb(235,202,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_mmu_page_fault (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`kvm_mmu_page_fault (14 samples, 0.03%)</title><rect x="853.3" y="289" width="0.3" height="15.0" fill="rgb(229,73,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (12 samples, 0.03%)')" onmouseout="c()">
<title>genunix`kmem_free (12 samples, 0.03%)</title><rect x="877.4" y="305" width="0.3" height="15.0" fill="rgb(241,11,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot_unaliased (38 samples, 0.08%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot_unaliased (38 samples, 0.08%)</title><rect x="796.1" y="81" width="0.8" height="15.0" fill="rgb(233,28,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`find_highest_vector (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`find_highest_vector (12 samples, 0.03%)</title><rect x="42.2" y="225" width="0.3" height="15.0" fill="rgb(212,165,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`cpu_outw (919 samples, 2.00%)')" onmouseout="c()">
<title>qemu-system-x86_64`cpu_outw (919 samples, 2.00%)</title><rect x="896.7" y="449" width="19.6" height="15.0" fill="rgb(254,229,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`host_mapping_level (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`host_mapping_level (5 samples, 0.01%)</title><rect x="836.0" y="241" width="0.1" height="15.0" fill="rgb(254,145,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`ddi_get_soft_state (14 samples, 0.03%)')" onmouseout="c()">
<title>genunix`ddi_get_soft_state (14 samples, 0.03%)</title><rect x="15.1" y="353" width="0.3" height="15.0" fill="rgb(218,37,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_has_pending_timer (104 samples, 0.23%)')" onmouseout="c()">
<title>kvm`apic_has_pending_timer (104 samples, 0.23%)</title><rect x="26.4" y="305" width="2.2" height="15.0" fill="rgb(213,99,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest (456 samples, 0.99%)')" onmouseout="c()">
<title>kvm`kvm_read_guest (456 samples, 0.99%)</title><rect x="761.9" y="129" width="9.7" height="15.0" fill="rgb(221,78,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_arch_push_nmi (6 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_arch_push_nmi (6 samples, 0.01%)</title><rect x="896.1" y="465" width="0.1" height="15.0" fill="rgb(212,89,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`skip_emulated_instruction (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`skip_emulated_instruction (12 samples, 0.03%)</title><rect x="850.2" y="273" width="0.3" height="15.0" fill="rgb(213,129,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_vcpu_load (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmx_vcpu_load (5 samples, 0.01%)</title><rect x="888.8" y="417" width="0.1" height="15.0" fill="rgb(214,115,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (16 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vmcs_readl (16 samples, 0.03%)</title><rect x="732.9" y="209" width="0.3" height="15.0" fill="rgb(215,94,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_mmu_reload (46 samples, 0.10%)')" onmouseout="c()">
<title>kvm`kvm_mmu_reload (46 samples, 0.10%)</title><rect x="32.4" y="321" width="1.0" height="15.0" fill="rgb(241,35,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_enabled (57 samples, 0.12%)')" onmouseout="c()">
<title>kvm`apic_enabled (57 samples, 0.12%)</title><rect x="26.8" y="289" width="1.2" height="15.0" fill="rgb(217,92,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (6 samples, 0.01%)</title><rect x="975.4" y="417" width="0.1" height="15.0" fill="rgb(207,112,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`_resume_from_idle (15 samples, 0.03%)')" onmouseout="c()">
<title>unix`_resume_from_idle (15 samples, 0.03%)</title><rect x="924.1" y="289" width="0.3" height="15.0" fill="rgb(227,149,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (6 samples, 0.01%)</title><rect x="933.2" y="369" width="0.1" height="15.0" fill="rgb(223,216,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_mmio_write (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_mmio_write (6 samples, 0.01%)</title><rect x="804.0" y="161" width="0.1" height="15.0" fill="rgb(234,28,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_cpu_is_stopped (5 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_cpu_is_stopped (5 samples, 0.01%)</title><rect x="967.1" y="497" width="0.1" height="15.0" fill="rgb(241,123,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`_resume_from_idle (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`_resume_from_idle (5 samples, 0.01%)</title><rect x="982.4" y="433" width="0.1" height="15.0" fill="rgb(217,201,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_cpl (59 samples, 0.13%)')" onmouseout="c()">
<title>kvm`vmx_get_cpl (59 samples, 0.13%)</title><rect x="802.4" y="161" width="1.3" height="15.0" fill="rgb(226,210,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`fls (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`fls (6 samples, 0.01%)</title><rect x="807.5" y="81" width="0.2" height="15.0" fill="rgb(213,227,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_enter (11 samples, 0.02%)</title><rect x="751.3" y="113" width="0.2" height="15.0" fill="rgb(218,121,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_on_user_return (21 samples, 0.05%)')" onmouseout="c()">
<title>kvm`kvm_on_user_return (21 samples, 0.05%)</title><rect x="879.2" y="305" width="0.4" height="15.0" fill="rgb(241,218,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_hva (169 samples, 0.37%)')" onmouseout="c()">
<title>kvm`gfn_to_hva (169 samples, 0.37%)</title><rect x="765.1" y="97" width="3.6" height="15.0" fill="rgb(222,160,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigcheck (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`sigcheck (6 samples, 0.01%)</title><rect x="923.8" y="289" width="0.1" height="15.0" fill="rgb(215,203,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_active (30 samples, 0.07%)')" onmouseout="c()">
<title>FSS`fss_active (30 samples, 0.07%)</title><rect x="905.1" y="81" width="0.6" height="15.0" fill="rgb(235,75,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`unalias_gfn_instantiation (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`unalias_gfn_instantiation (8 samples, 0.02%)</title><rect x="799.7" y="97" width="0.2" height="15.0" fill="rgb(235,85,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`htable_va2entry (7 samples, 0.02%)')" onmouseout="c()">
<title>unix`htable_va2entry (7 samples, 0.02%)</title><rect x="830.9" y="161" width="0.2" height="15.0" fill="rgb(246,64,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`push_nmi (6 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`push_nmi (6 samples, 0.01%)</title><rect x="966.9" y="465" width="0.2" height="15.0" fill="rgb(223,58,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`tsc_read (11 samples, 0.02%)</title><rect x="884.3" y="401" width="0.3" height="15.0" fill="rgb(233,53,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_set_rflags (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmx_set_rflags (9 samples, 0.02%)</title><rect x="737.3" y="241" width="0.2" height="15.0" fill="rgb(235,13,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_arch_vcpu_put (69 samples, 0.15%)')" onmouseout="c()">
<title>kvm`kvm_arch_vcpu_put (69 samples, 0.15%)</title><rect x="877.7" y="321" width="1.5" height="15.0" fill="rgb(234,118,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`stw_phys (21 samples, 0.05%)')" onmouseout="c()">
<title>qemu-system-x86_64`stw_phys (21 samples, 0.05%)</title><rect x="915.6" y="305" width="0.4" height="15.0" fill="rgb(226,207,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`decode_modrm (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`decode_modrm (9 samples, 0.02%)</title><rect x="731.4" y="241" width="0.2" height="15.0" fill="rgb(246,37,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`variable_test_bit (17 samples, 0.04%)')" onmouseout="c()">
<title>kvm`variable_test_bit (17 samples, 0.04%)</title><rect x="730.7" y="225" width="0.3" height="15.0" fill="rgb(241,114,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`cbe_xcall (120 samples, 0.26%)')" onmouseout="c()">
<title>unix`cbe_xcall (120 samples, 0.26%)</title><rect x="812.0" y="65" width="2.6" height="15.0" fill="rgb(207,159,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_ioapic_update_eoi (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_ioapic_update_eoi (7 samples, 0.02%)</title><rect x="809.4" y="97" width="0.1" height="15.0" fill="rgb(221,63,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`splr (21 samples, 0.05%)')" onmouseout="c()">
<title>unix`splr (21 samples, 0.05%)</title><rect x="903.6" y="81" width="0.5" height="15.0" fill="rgb(245,199,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`syscall_mstate (9 samples, 0.02%)</title><rect x="885.2" y="465" width="0.2" height="15.0" fill="rgb(208,63,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`find_highest_vector (29 samples, 0.06%)')" onmouseout="c()">
<title>kvm`find_highest_vector (29 samples, 0.06%)</title><rect x="808.3" y="65" width="0.6" height="15.0" fill="rgb(227,23,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_set_interrupt_shadow (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmx_set_interrupt_shadow (11 samples, 0.02%)</title><rect x="847.6" y="257" width="0.3" height="15.0" fill="rgb(206,175,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`seg_override_base (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`seg_override_base (6 samples, 0.01%)</title><rect x="735.4" y="241" width="0.1" height="15.0" fill="rgb(247,52,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_inactive (25 samples, 0.05%)')" onmouseout="c()">
<title>FSS`fss_inactive (25 samples, 0.05%)</title><rect x="33.9" y="241" width="0.6" height="15.0" fill="rgb(253,48,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`atomic_add_64 (8 samples, 0.02%)</title><rect x="893.8" y="417" width="0.2" height="15.0" fill="rgb(213,176,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`tsc_read (5 samples, 0.01%)</title><rect x="976.7" y="401" width="0.1" height="15.0" fill="rgb(228,17,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_iodevice_write (13 samples, 0.03%)')" onmouseout="c()">
<title>kvm`kvm_iodevice_write (13 samples, 0.03%)</title><rect x="786.0" y="177" width="0.3" height="15.0" fill="rgb(219,2,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`unalias_gfn (54 samples, 0.12%)')" onmouseout="c()">
<title>kvm`unalias_gfn (54 samples, 0.12%)</title><rect x="843.9" y="225" width="1.2" height="15.0" fill="rgb(243,17,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`cpu_wakeup_mwait (21 samples, 0.05%)')" onmouseout="c()">
<title>unix`cpu_wakeup_mwait (21 samples, 0.05%)</title><rect x="907.7" y="65" width="0.5" height="15.0" fill="rgb(226,33,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_set (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`lock_set (8 samples, 0.02%)</title><rect x="34.3" y="225" width="0.2" height="15.0" fill="rgb(222,120,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kernel_pio (51 samples, 0.11%)')" onmouseout="c()">
<title>kvm`kernel_pio (51 samples, 0.11%)</title><rect x="851.1" y="257" width="1.1" height="15.0" fill="rgb(231,31,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_timedwait_sig_hires (66 samples, 0.14%)')" onmouseout="c()">
<title>genunix`cv_timedwait_sig_hires (66 samples, 0.14%)</title><rect x="969.1" y="401" width="1.4" height="15.0" fill="rgb(218,28,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_try (16 samples, 0.03%)')" onmouseout="c()">
<title>unix`lock_try (16 samples, 0.03%)</title><rect x="924.5" y="289" width="0.3" height="15.0" fill="rgb(252,63,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`pthread_kill (8 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`pthread_kill (8 samples, 0.02%)</title><rect x="889.6" y="481" width="0.2" height="15.0" fill="rgb(218,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`do_insn_fetch (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`do_insn_fetch (18 samples, 0.04%)</title><rect x="731.8" y="241" width="0.4" height="15.0" fill="rgb(205,156,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`sigemptyset (11 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`sigemptyset (11 samples, 0.02%)</title><rect x="10.6" y="497" width="0.2" height="15.0" fill="rgb(232,117,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800c91 (66 samples, 0.14%)')" onmouseout="c()">
<title>unix`0xfffffffffb800c91 (66 samples, 0.14%)</title><rect x="973.0" y="465" width="1.4" height="15.0" fill="rgb(209,0,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`xc_common (14 samples, 0.03%)')" onmouseout="c()">
<title>unix`xc_common (14 samples, 0.03%)</title><rect x="876.2" y="241" width="0.3" height="15.0" fill="rgb(238,38,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_hva (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`gfn_to_hva (12 samples, 0.03%)</title><rect x="763.2" y="113" width="0.3" height="15.0" fill="rgb(232,174,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_reg_write (443 samples, 0.97%)')" onmouseout="c()">
<title>kvm`apic_reg_write (443 samples, 0.97%)</title><rect x="805.6" y="129" width="9.5" height="15.0" fill="rgb(219,62,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`disp (20 samples, 0.04%)')" onmouseout="c()">
<title>unix`disp (20 samples, 0.04%)</title><rect x="981.2" y="305" width="0.4" height="15.0" fill="rgb(215,145,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_reg_write (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`apic_reg_write (12 samples, 0.03%)</title><rect x="815.4" y="145" width="0.3" height="15.0" fill="rgb(254,97,49)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (14 samples, 0.03%)')" onmouseout="c()">
<title>genunix`syscall_mstate (14 samples, 0.03%)</title><rect x="893.5" y="417" width="0.3" height="15.0" fill="rgb(237,199,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot_unaliased (177 samples, 0.39%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot_unaliased (177 samples, 0.39%)</title><rect x="822.8" y="209" width="3.8" height="15.0" fill="rgb(243,77,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`mstate_thread_onproc_time (13 samples, 0.03%)')" onmouseout="c()">
<title>genunix`mstate_thread_onproc_time (13 samples, 0.03%)</title><rect x="34.9" y="209" width="0.3" height="15.0" fill="rgb(218,28,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_apic_local_deliver (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`kvm_apic_local_deliver (12 samples, 0.03%)</title><rect x="30.8" y="289" width="0.2" height="15.0" fill="rgb(230,71,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bcopy (53 samples, 0.12%)')" onmouseout="c()">
<title>unix`bcopy (53 samples, 0.12%)</title><rect x="769.4" y="97" width="1.1" height="15.0" fill="rgb(222,83,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cyclic_reprogram_here (128 samples, 0.28%)')" onmouseout="c()">
<title>genunix`cyclic_reprogram_here (128 samples, 0.28%)</title><rect x="812.0" y="81" width="2.7" height="15.0" fill="rgb(236,38,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_find_highest_isr (28 samples, 0.06%)')" onmouseout="c()">
<title>kvm`apic_find_highest_isr (28 samples, 0.06%)</title><rect x="807.1" y="97" width="0.6" height="15.0" fill="rgb(235,95,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_read32 (20 samples, 0.04%)')" onmouseout="c()">
<title>kvm`vmcs_read32 (20 samples, 0.04%)</title><rect x="736.8" y="225" width="0.4" height="15.0" fill="rgb(224,205,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_writable_pte (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`is_writable_pte (11 samples, 0.02%)</title><rect x="787.6" y="145" width="0.2" height="15.0" fill="rgb(208,211,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_exit (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_exit (8 samples, 0.02%)</title><rect x="843.2" y="209" width="0.2" height="15.0" fill="rgb(233,191,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_mmio_write (579 samples, 1.26%)')" onmouseout="c()">
<title>kvm`vcpu_mmio_write (579 samples, 1.26%)</title><rect x="803.8" y="177" width="12.3" height="15.0" fill="rgb(211,132,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`no_preempt (10 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`no_preempt (10 samples, 0.02%)</title><rect x="935.7" y="385" width="0.2" height="15.0" fill="rgb(240,175,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`_sysconfig (83 samples, 0.18%)')" onmouseout="c()">
<title>libc.so.1`_sysconfig (83 samples, 0.18%)</title><rect x="976.0" y="481" width="1.8" height="15.0" fill="rgb(239,223,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (24 samples, 0.05%)')" onmouseout="c()">
<title>unix`atomic_add_64 (24 samples, 0.05%)</title><rect x="931.5" y="369" width="0.5" height="15.0" fill="rgb(216,138,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vapic_enter (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vapic_enter (5 samples, 0.01%)</title><rect x="874.8" y="337" width="0.1" height="15.0" fill="rgb(209,87,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_clear (15 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vcpu_clear (15 samples, 0.03%)</title><rect x="876.2" y="289" width="0.3" height="15.0" fill="rgb(233,210,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`fsig (7 samples, 0.02%)')" onmouseout="c()">
<title>genunix`fsig (7 samples, 0.02%)</title><rect x="970.0" y="353" width="0.1" height="15.0" fill="rgb(252,228,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`cpu_exit (5 samples, 0.01%)')" onmouseout="c()">
<title>qemu-system-x86_64`cpu_exit (5 samples, 0.01%)</title><rect x="899.4" y="305" width="0.1" height="15.0" fill="rgb(239,99,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_arch_vcpu_ioctl_run (40,566 samples, 88.37%)')" onmouseout="c()">
<title>kvm`kvm_arch_vcpu_ioctl_run (40,566 samples, 88.37%)</title><rect x="15.6" y="353" width="866.0" height="15.0" fill="rgb(222,16,41)" rx="2" ry="2" />
<text text-anchor="" x="18.614516620921" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kvm`kvm_arch_vcpu_ioctl_run</text>
</g>
<g class="func_g" onmouseover="s('kvm`do_insn_fetch (103 samples, 0.22%)')" onmouseout="c()">
<title>kvm`do_insn_fetch (103 samples, 0.22%)</title><rect x="744.8" y="209" width="2.2" height="15.0" fill="rgb(250,95,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_wait_sig_swap_core (63 samples, 0.14%)')" onmouseout="c()">
<title>genunix`cv_wait_sig_swap_core (63 samples, 0.14%)</title><rect x="980.5" y="337" width="1.3" height="15.0" fill="rgb(248,209,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sep_restore (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`sep_restore (6 samples, 0.01%)</title><rect x="889.1" y="449" width="0.1" height="15.0" fill="rgb(208,84,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest (393 samples, 0.86%)')" onmouseout="c()">
<title>kvm`kvm_read_guest (393 samples, 0.86%)</title><rect x="793.1" y="129" width="8.4" height="15.0" fill="rgb(212,161,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`htable_getpage (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`htable_getpage (10 samples, 0.02%)</title><rect x="835.4" y="209" width="0.2" height="15.0" fill="rgb(240,194,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`spin_lock_set (24 samples, 0.05%)')" onmouseout="c()">
<title>libc.so.1`spin_lock_set (24 samples, 0.05%)</title><rect x="935.9" y="385" width="0.5" height="15.0" fill="rgb(219,33,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`lwp_hash_lookup (26 samples, 0.06%)')" onmouseout="c()">
<title>genunix`lwp_hash_lookup (26 samples, 0.06%)</title><rect x="891.3" y="385" width="0.5" height="15.0" fill="rgb(231,12,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bcopy (42 samples, 0.09%)')" onmouseout="c()">
<title>unix`bcopy (42 samples, 0.09%)</title><rect x="799.9" y="97" width="0.9" height="15.0" fill="rgb(219,134,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`x86pte_get (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`x86pte_get (6 samples, 0.01%)</title><rect x="835.0" y="177" width="0.1" height="15.0" fill="rgb(227,198,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_interrupt_window (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`handle_interrupt_window (7 samples, 0.02%)</title><rect x="711.6" y="305" width="0.1" height="15.0" fill="rgb(236,86,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_nx (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`is_nx (6 samples, 0.01%)</title><rect x="793.0" y="129" width="0.1" height="15.0" fill="rgb(249,63,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`idtot (26 samples, 0.06%)')" onmouseout="c()">
<title>genunix`idtot (26 samples, 0.06%)</title><rect x="891.3" y="401" width="0.5" height="15.0" fill="rgb(232,141,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`_resume_from_idle (44 samples, 0.10%)')" onmouseout="c()">
<title>unix`_resume_from_idle (44 samples, 0.10%)</title><rect x="933.3" y="401" width="1.0" height="15.0" fill="rgb(249,163,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_set_rflags (45 samples, 0.10%)')" onmouseout="c()">
<title>kvm`kvm_set_rflags (45 samples, 0.10%)</title><rect x="734.3" y="241" width="1.0" height="15.0" fill="rgb(218,115,42)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`decode_register_operand (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`decode_register_operand (14 samples, 0.03%)</title><rect x="747.1" y="225" width="0.3" height="15.0" fill="rgb(246,178,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`htable_lookup (63 samples, 0.14%)')" onmouseout="c()">
<title>unix`htable_lookup (63 samples, 0.14%)</title><rect x="829.6" y="161" width="1.3" height="15.0" fill="rgb(245,45,13)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`htable_getpte (212 samples, 0.46%)')" onmouseout="c()">
<title>unix`htable_getpte (212 samples, 0.46%)</title><rect x="829.2" y="177" width="4.5" height="15.0" fill="rgb(248,76,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`post_syscall (58 samples, 0.13%)')" onmouseout="c()">
<title>genunix`post_syscall (58 samples, 0.13%)</title><rect x="973.0" y="449" width="1.3" height="15.0" fill="rgb(213,177,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (17 samples, 0.04%)')" onmouseout="c()">
<title>unix`mutex_enter (17 samples, 0.04%)</title><rect x="826.7" y="209" width="0.4" height="15.0" fill="rgb(225,72,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lwp_segregs_restore (7 samples, 0.02%)')" onmouseout="c()">
<title>unix`lwp_segregs_restore (7 samples, 0.02%)</title><rect x="888.9" y="433" width="0.2" height="15.0" fill="rgb(228,83,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`spin_lock_clear (8 samples, 0.02%)')" onmouseout="c()">
<title>libc.so.1`spin_lock_clear (8 samples, 0.02%)</title><rect x="936.4" y="385" width="0.2" height="15.0" fill="rgb(229,17,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`swtch (198 samples, 0.43%)')" onmouseout="c()">
<title>unix`swtch (198 samples, 0.43%)</title><rect x="36.8" y="273" width="4.2" height="15.0" fill="rgb(241,13,30)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_set (7 samples, 0.02%)')" onmouseout="c()">
<title>unix`lock_set (7 samples, 0.02%)</title><rect x="905.6" y="65" width="0.1" height="15.0" fill="rgb(241,17,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`unalias_gfn_instantiation (27 samples, 0.06%)')" onmouseout="c()">
<title>kvm`unalias_gfn_instantiation (27 samples, 0.06%)</title><rect x="796.9" y="81" width="0.6" height="15.0" fill="rgb(235,185,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`emulator_write_emulated (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`emulator_write_emulated (5 samples, 0.01%)</title><rect x="783.8" y="225" width="0.1" height="15.0" fill="rgb(226,116,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_vcpu_block (447 samples, 0.97%)')" onmouseout="c()">
<title>kvm`kvm_vcpu_block (447 samples, 0.97%)</title><rect x="33.4" y="321" width="9.6" height="15.0" fill="rgb(224,108,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`qemu_bh_schedule (680 samples, 1.48%)')" onmouseout="c()">
<title>qemu-system-x86_64`qemu_bh_schedule (680 samples, 1.48%)</title><rect x="898.5" y="337" width="14.5" height="15.0" fill="rgb(246,161,44)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_get_rflags (39 samples, 0.08%)')" onmouseout="c()">
<title>kvm`kvm_get_rflags (39 samples, 0.08%)</title><rect x="732.4" y="241" width="0.8" height="15.0" fill="rgb(251,35,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`splr (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`splr (8 samples, 0.02%)</title><rect x="928.0" y="257" width="0.1" height="15.0" fill="rgb(231,158,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_set (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`lock_set (5 samples, 0.01%)</title><rect x="920.8" y="241" width="0.1" height="15.0" fill="rgb(223,124,40)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (21 samples, 0.05%)')" onmouseout="c()">
<title>unix`sys_syscall (21 samples, 0.05%)</title><rect x="894.5" y="433" width="0.5" height="15.0" fill="rgb(233,26,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bzero (112 samples, 0.24%)')" onmouseout="c()">
<title>unix`bzero (112 samples, 0.24%)</title><rect x="777.9" y="225" width="2.4" height="15.0" fill="rgb(242,78,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`emulator_write_emulated (1,488 samples, 3.24%)')" onmouseout="c()">
<title>kvm`emulator_write_emulated (1,488 samples, 3.24%)</title><rect x="784.8" y="209" width="31.8" height="15.0" fill="rgb(215,52,38)" rx="2" ry="2" />
<text text-anchor="" x="787.824641659042" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >kv..</text>
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_memslot_unaliased (11 samples, 0.02%)')" onmouseout="c()">
<title>kvm`gfn_to_memslot_unaliased (11 samples, 0.02%)</title><rect x="768.7" y="97" width="0.3" height="15.0" fill="rgb(243,112,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`resume (6 samples, 0.01%)')" onmouseout="c()">
<title>unix`resume (6 samples, 0.01%)</title><rect x="981.7" y="305" width="0.1" height="15.0" fill="rgb(205,148,38)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`htable_release (14 samples, 0.03%)')" onmouseout="c()">
<title>unix`htable_release (14 samples, 0.03%)</title><rect x="834.1" y="177" width="0.3" height="15.0" fill="rgb(225,15,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`decode_modrm (183 samples, 0.40%)')" onmouseout="c()">
<title>kvm`decode_modrm (183 samples, 0.40%)</title><rect x="743.1" y="225" width="3.9" height="15.0" fill="rgb(229,156,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_enter (36 samples, 0.08%)')" onmouseout="c()">
<title>genunix`disp_lock_enter (36 samples, 0.08%)</title><rect x="903.3" y="113" width="0.8" height="15.0" fill="rgb(215,202,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_pfn (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`gfn_to_pfn (18 samples, 0.04%)</title><rect x="817.9" y="257" width="0.4" height="15.0" fill="rgb(223,0,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (609 samples, 1.33%)')" onmouseout="c()">
<title>unix`sys_syscall (609 samples, 1.33%)</title><rect x="919.0" y="385" width="13.0" height="15.0" fill="rgb(207,158,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`lduw_phys (71 samples, 0.15%)')" onmouseout="c()">
<title>qemu-system-x86_64`lduw_phys (71 samples, 0.15%)</title><rect x="913.8" y="305" width="1.5" height="15.0" fill="rgb(214,95,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_clear_vector (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_clear_vector (5 samples, 0.01%)</title><rect x="806.4" y="113" width="0.1" height="15.0" fill="rgb(228,65,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`atomic_add_64 (5 samples, 0.01%)</title><rect x="974.6" y="449" width="0.1" height="15.0" fill="rgb(222,83,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`mutex_lock (2,255 samples, 4.91%)')" onmouseout="c()">
<title>libc.so.1`mutex_lock (2,255 samples, 4.91%)</title><rect x="916.9" y="449" width="48.1" height="15.0" fill="rgb(233,6,36)" rx="2" ry="2" />
<text text-anchor="" x="919.861848124428" y="459.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc..</text>
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_cr0_bits (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_read_cr0_bits (9 samples, 0.02%)</title><rect x="773.9" y="145" width="0.2" height="15.0" fill="rgb(244,34,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_halt (17 samples, 0.04%)')" onmouseout="c()">
<title>kvm`handle_halt (17 samples, 0.04%)</title><rect x="850.1" y="289" width="0.4" height="15.0" fill="rgb(229,123,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`pg_ev_thread_swtch (22 samples, 0.05%)')" onmouseout="c()">
<title>unix`pg_ev_thread_swtch (22 samples, 0.05%)</title><rect x="928.2" y="273" width="0.5" height="15.0" fill="rgb(229,178,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('- (137 samples, 0.30%)')" onmouseout="c()">
<title>- (137 samples, 0.30%)</title><rect x="891.1" y="449" width="2.9" height="15.0" fill="rgb(160,160,160)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`fop_write (471 samples, 1.03%)')" onmouseout="c()">
<title>genunix`fop_write (471 samples, 1.03%)</title><rect x="901.6" y="225" width="10.1" height="15.0" fill="rgb(248,196,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrestime (10 samples, 0.02%)')" onmouseout="c()">
<title>genunix`gethrestime (10 samples, 0.02%)</title><rect x="911.1" y="177" width="0.2" height="15.0" fill="rgb(216,183,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_wait_sig_swap (63 samples, 0.14%)')" onmouseout="c()">
<title>genunix`cv_wait_sig_swap (63 samples, 0.14%)</title><rect x="980.5" y="353" width="1.3" height="15.0" fill="rgb(205,192,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`___errno (5 samples, 0.01%)')" onmouseout="c()">
<title>libc.so.1`___errno (5 samples, 0.01%)</title><rect x="11.9" y="481" width="0.1" height="15.0" fill="rgb(248,116,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`syscall_mstate (6 samples, 0.01%)</title><rect x="932.1" y="385" width="0.2" height="15.0" fill="rgb(223,165,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`handle_io (95 samples, 0.21%)')" onmouseout="c()">
<title>kvm`handle_io (95 samples, 0.21%)</title><rect x="850.7" y="289" width="2.0" height="15.0" fill="rgb(243,228,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`thread_lock (10 samples, 0.02%)')" onmouseout="c()">
<title>genunix`thread_lock (10 samples, 0.02%)</title><rect x="886.7" y="433" width="0.2" height="15.0" fill="rgb(248,11,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gva_to_gpa (731 samples, 1.59%)')" onmouseout="c()">
<title>kvm`paging64_gva_to_gpa (731 samples, 1.59%)</title><rect x="786.7" y="161" width="15.7" height="15.0" fill="rgb(205,15,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_enter (11 samples, 0.02%)')" onmouseout="c()">
<title>genunix`disp_lock_enter (11 samples, 0.02%)</title><rect x="925.6" y="257" width="0.3" height="15.0" fill="rgb(219,197,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_pfn (648 samples, 1.41%)')" onmouseout="c()">
<title>kvm`gfn_to_pfn (648 samples, 1.41%)</title><rect x="822.1" y="241" width="13.9" height="15.0" fill="rgb(237,204,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`tsc_read (5 samples, 0.01%)</title><rect x="887.7" y="417" width="0.1" height="15.0" fill="rgb(229,172,21)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_load_guest_fpu (81 samples, 0.18%)')" onmouseout="c()">
<title>kvm`kvm_load_guest_fpu (81 samples, 0.18%)</title><rect x="717.8" y="305" width="1.8" height="15.0" fill="rgb(208,161,43)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('all (45,906 samples, 100%)')" onmouseout="c()">
<title>all (45,906 samples, 100%)</title><rect x="10.0" y="577" width="980.0" height="15.0" fill="rgb(215,197,47)" rx="2" ry="2" />
<text text-anchor="" x="13" y="587.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('fifofs`fifo_rwunlock (5 samples, 0.01%)')" onmouseout="c()">
<title>fifofs`fifo_rwunlock (5 samples, 0.01%)</title><rect x="901.3" y="225" width="0.1" height="15.0" fill="rgb(240,53,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`atomic_add_64 (8 samples, 0.02%)</title><rect x="35.7" y="257" width="0.1" height="15.0" fill="rgb(246,181,7)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_protmode (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`is_protmode (10 samples, 0.02%)</title><rect x="732.2" y="241" width="0.2" height="15.0" fill="rgb(242,202,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_register_read (83 samples, 0.18%)')" onmouseout="c()">
<title>kvm`kvm_register_read (83 samples, 0.18%)</title><rect x="728.9" y="225" width="1.8" height="15.0" fill="rgb(237,174,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_sleep (61 samples, 0.13%)')" onmouseout="c()">
<title>FSS`fss_sleep (61 samples, 0.13%)</title><rect x="33.9" y="257" width="1.3" height="15.0" fill="rgb(253,224,31)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`strpollwakeup (335 samples, 0.73%)')" onmouseout="c()">
<title>genunix`strpollwakeup (335 samples, 0.73%)</title><rect x="902.3" y="177" width="7.2" height="15.0" fill="rgb(210,206,39)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest_page (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_read_guest_page (6 samples, 0.01%)</title><rect x="801.5" y="129" width="0.1" height="15.0" fill="rgb(211,23,53)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`paging64_gva_to_gpa (928 samples, 2.02%)')" onmouseout="c()">
<title>kvm`paging64_gva_to_gpa (928 samples, 2.02%)</title><rect x="752.8" y="161" width="19.8" height="15.0" fill="rgb(242,94,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`lwp_sigmask (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`lwp_sigmask (5 samples, 0.01%)</title><rect x="16.6" y="337" width="0.1" height="15.0" fill="rgb(235,38,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_vcpu_put (26 samples, 0.06%)')" onmouseout="c()">
<title>kvm`vmx_vcpu_put (26 samples, 0.06%)</title><rect x="39.0" y="193" width="0.5" height="15.0" fill="rgb(253,225,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`kvm_flush_coalesced_mmio_buffer (13 samples, 0.03%)')" onmouseout="c()">
<title>qemu-system-x86_64`kvm_flush_coalesced_mmio_buffer (13 samples, 0.03%)</title><rect x="896.2" y="465" width="0.3" height="15.0" fill="rgb(239,179,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`set_active_fd (12 samples, 0.03%)')" onmouseout="c()">
<title>genunix`set_active_fd (12 samples, 0.03%)</title><rect x="883.1" y="401" width="0.2" height="15.0" fill="rgb(242,119,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (5 samples, 0.01%)</title><rect x="974.5" y="433" width="0.1" height="15.0" fill="rgb(245,203,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`cpucaps_charge (54 samples, 0.12%)')" onmouseout="c()">
<title>unix`cpucaps_charge (54 samples, 0.12%)</title><rect x="921.0" y="257" width="1.1" height="15.0" fill="rgb(240,208,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_set_spl (35 samples, 0.08%)')" onmouseout="c()">
<title>unix`lock_set_spl (35 samples, 0.08%)</title><rect x="903.3" y="97" width="0.8" height="15.0" fill="rgb(254,24,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`writeback (29 samples, 0.06%)')" onmouseout="c()">
<title>kvm`writeback (29 samples, 0.06%)</title><rect x="737.5" y="241" width="0.6" height="15.0" fill="rgb(234,108,9)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_long_mode (10 samples, 0.02%)')" onmouseout="c()">
<title>kvm`is_long_mode (10 samples, 0.02%)</title><rect x="753.7" y="145" width="0.2" height="15.0" fill="rgb(227,71,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`switch_sp_and_call (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`switch_sp_and_call (5 samples, 0.01%)</title><rect x="869.0" y="273" width="0.1" height="15.0" fill="rgb(232,137,35)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`gfn_to_hva (220 samples, 0.48%)')" onmouseout="c()">
<title>kvm`gfn_to_hva (220 samples, 0.48%)</title><rect x="822.5" y="225" width="4.7" height="15.0" fill="rgb(249,15,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_inject_pending_timer_irqs (20 samples, 0.04%)')" onmouseout="c()">
<title>kvm`kvm_inject_pending_timer_irqs (20 samples, 0.04%)</title><rect x="30.7" y="321" width="0.5" height="15.0" fill="rgb(245,167,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (38 samples, 0.08%)')" onmouseout="c()">
<title>unix`mutex_enter (38 samples, 0.08%)</title><rect x="831.1" y="161" width="0.8" height="15.0" fill="rgb(224,191,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`is_nx (29 samples, 0.06%)')" onmouseout="c()">
<title>kvm`is_nx (29 samples, 0.06%)</title><rect x="753.9" y="145" width="0.7" height="15.0" fill="rgb(205,46,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`htable_release (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`htable_release (10 samples, 0.02%)</title><rect x="835.2" y="193" width="0.2" height="15.0" fill="rgb(215,89,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`memcpy (37 samples, 0.08%)')" onmouseout="c()">
<title>genunix`memcpy (37 samples, 0.08%)</title><rect x="727.4" y="241" width="0.7" height="15.0" fill="rgb(248,215,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`cpu_set_apic_base (18 samples, 0.04%)')" onmouseout="c()">
<title>qemu-system-x86_64`cpu_set_apic_base (18 samples, 0.04%)</title><rect x="965.5" y="433" width="0.4" height="15.0" fill="rgb(235,75,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`disp_lock_enter (12 samples, 0.03%)')" onmouseout="c()">
<title>genunix`disp_lock_enter (12 samples, 0.03%)</title><rect x="37.1" y="241" width="0.2" height="15.0" fill="rgb(244,172,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`gdt_ucode_model (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`gdt_ucode_model (5 samples, 0.01%)</title><rect x="888.9" y="417" width="0.1" height="15.0" fill="rgb(216,205,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`copyin (17 samples, 0.04%)')" onmouseout="c()">
<title>unix`copyin (17 samples, 0.04%)</title><rect x="770.5" y="97" width="0.4" height="15.0" fill="rgb(216,9,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_enter (8 samples, 0.02%)</title><rect x="881.0" y="321" width="0.2" height="15.0" fill="rgb(228,206,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`restorectx (56 samples, 0.12%)')" onmouseout="c()">
<title>genunix`restorectx (56 samples, 0.12%)</title><rect x="887.9" y="449" width="1.2" height="15.0" fill="rgb(216,55,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_request_guest_time_update (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_request_guest_time_update (7 samples, 0.02%)</title><rect x="876.5" y="321" width="0.2" height="15.0" fill="rgb(212,31,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_cpl (58 samples, 0.13%)')" onmouseout="c()">
<title>kvm`vmx_get_cpl (58 samples, 0.13%)</title><rect x="773.7" y="177" width="1.2" height="15.0" fill="rgb(242,180,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`sys_syscall (12 samples, 0.03%)</title><rect x="894.1" y="449" width="0.3" height="15.0" fill="rgb(211,9,54)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_cpl (8 samples, 0.02%)')" onmouseout="c()">
<title>kvm`vmx_get_cpl (8 samples, 0.02%)</title><rect x="775.7" y="193" width="0.1" height="15.0" fill="rgb(217,145,25)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_rflags (25 samples, 0.05%)')" onmouseout="c()">
<title>kvm`vmx_get_rflags (25 samples, 0.05%)</title><rect x="732.7" y="225" width="0.5" height="15.0" fill="rgb(237,100,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_lvt_enabled (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_lvt_enabled (6 samples, 0.01%)</title><rect x="28.6" y="305" width="0.1" height="15.0" fill="rgb(231,198,28)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`start_apic_timer (160 samples, 0.35%)')" onmouseout="c()">
<title>kvm`start_apic_timer (160 samples, 0.35%)</title><rect x="811.7" y="113" width="3.4" height="15.0" fill="rgb(248,78,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`disp (130 samples, 0.28%)')" onmouseout="c()">
<title>unix`disp (130 samples, 0.28%)</title><rect x="925.4" y="273" width="2.7" height="15.0" fill="rgb(208,204,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`unalias_gfn_instantiation (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`unalias_gfn_instantiation (9 samples, 0.02%)</title><rect x="835.7" y="225" width="0.2" height="15.0" fill="rgb(239,57,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`post_syscall (77 samples, 0.17%)')" onmouseout="c()">
<title>genunix`post_syscall (77 samples, 0.17%)</title><rect x="885.8" y="449" width="1.6" height="15.0" fill="rgb(252,143,41)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (5 samples, 0.01%)')" onmouseout="c()">
<title>unix`mutex_enter (5 samples, 0.01%)</title><rect x="972.4" y="433" width="0.1" height="15.0" fill="rgb(213,221,17)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`kmem_free (7 samples, 0.02%)')" onmouseout="c()">
<title>genunix`kmem_free (7 samples, 0.02%)</title><rect x="971.5" y="401" width="0.1" height="15.0" fill="rgb(216,20,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`native_write_msr (71 samples, 0.15%)')" onmouseout="c()">
<title>kvm`native_write_msr (71 samples, 0.15%)</title><rect x="857.4" y="289" width="1.5" height="15.0" fill="rgb(252,23,1)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cdev_ioctl (40,688 samples, 88.63%)')" onmouseout="c()">
<title>genunix`cdev_ioctl (40,688 samples, 88.63%)</title><rect x="14.1" y="385" width="868.6" height="15.0" fill="rgb(235,34,45)" rx="2" ry="2" />
<text text-anchor="" x="17.1201585849344" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >genunix`cdev_ioctl</text>
</g>
<g class="func_g" onmouseover="s('genunix`savectx (71 samples, 0.15%)')" onmouseout="c()">
<title>genunix`savectx (71 samples, 0.15%)</title><rect x="38.7" y="241" width="1.5" height="15.0" fill="rgb(218,86,26)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`xc_sync (14 samples, 0.03%)')" onmouseout="c()">
<title>unix`xc_sync (14 samples, 0.03%)</title><rect x="876.2" y="257" width="0.3" height="15.0" fill="rgb(215,152,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_get_rflags (14 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vmx_get_rflags (14 samples, 0.03%)</title><rect x="736.3" y="241" width="0.3" height="15.0" fill="rgb(205,125,20)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest_page (360 samples, 0.78%)')" onmouseout="c()">
<title>kvm`kvm_read_guest_page (360 samples, 0.78%)</title><rect x="763.6" y="113" width="7.7" height="15.0" fill="rgb(236,59,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`0xfffffffffb800ca0 (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`0xfffffffffb800ca0 (11 samples, 0.02%)</title><rect x="933.1" y="401" width="0.2" height="15.0" fill="rgb(224,201,8)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`threadp (32 samples, 0.07%)')" onmouseout="c()">
<title>unix`threadp (32 samples, 0.07%)</title><rect x="870.3" y="321" width="0.7" height="15.0" fill="rgb(217,74,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bzero (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`bzero (12 samples, 0.03%)</title><rect x="893.3" y="401" width="0.2" height="15.0" fill="rgb(210,178,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`new_mstate (20 samples, 0.04%)')" onmouseout="c()">
<title>genunix`new_mstate (20 samples, 0.04%)</title><rect x="922.1" y="273" width="0.4" height="15.0" fill="rgb(226,57,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`cv_wait_sig_swap_core (355 samples, 0.77%)')" onmouseout="c()">
<title>genunix`cv_wait_sig_swap_core (355 samples, 0.77%)</title><rect x="33.5" y="289" width="7.5" height="15.0" fill="rgb(221,224,37)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (9 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_enter (9 samples, 0.02%)</title><rect x="851.9" y="241" width="0.2" height="15.0" fill="rgb(248,220,3)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`post_kvm_run_save (33 samples, 0.07%)')" onmouseout="c()">
<title>kvm`post_kvm_run_save (33 samples, 0.07%)</title><rect x="43.3" y="321" width="0.7" height="15.0" fill="rgb(232,167,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest_page (323 samples, 0.70%)')" onmouseout="c()">
<title>kvm`kvm_read_guest_page (323 samples, 0.70%)</title><rect x="794.5" y="113" width="6.9" height="15.0" fill="rgb(241,75,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`to_vmx (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`to_vmx (9 samples, 0.02%)</title><rect x="721.1" y="305" width="0.2" height="15.0" fill="rgb(219,133,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_find_highest_isr (40 samples, 0.09%)')" onmouseout="c()">
<title>kvm`apic_find_highest_isr (40 samples, 0.09%)</title><rect x="808.0" y="81" width="0.9" height="15.0" fill="rgb(252,223,4)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`sys_syscall (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`sys_syscall (8 samples, 0.02%)</title><rect x="895.1" y="449" width="0.2" height="15.0" fill="rgb(244,133,34)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`clear_bit (18 samples, 0.04%)')" onmouseout="c()">
<title>kvm`clear_bit (18 samples, 0.04%)</title><rect x="808.9" y="97" width="0.4" height="15.0" fill="rgb(249,114,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`memcpy (9 samples, 0.02%)')" onmouseout="c()">
<title>genunix`memcpy (9 samples, 0.02%)</title><rect x="783.6" y="225" width="0.2" height="15.0" fill="rgb(251,171,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`syscall_mstate (5 samples, 0.01%)')" onmouseout="c()">
<title>genunix`syscall_mstate (5 samples, 0.01%)</title><rect x="932.0" y="401" width="0.1" height="15.0" fill="rgb(232,210,6)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_read_guest (21 samples, 0.05%)')" onmouseout="c()">
<title>kvm`kvm_read_guest (21 samples, 0.05%)</title><rect x="755.3" y="145" width="0.4" height="15.0" fill="rgb(234,155,2)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_fetch_guest_virt (15 samples, 0.03%)')" onmouseout="c()">
<title>kvm`kvm_fetch_guest_virt (15 samples, 0.03%)</title><rect x="775.8" y="209" width="0.4" height="15.0" fill="rgb(251,122,46)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`gethrtime_unscaled (7 samples, 0.02%)')" onmouseout="c()">
<title>genunix`gethrtime_unscaled (7 samples, 0.02%)</title><rect x="976.7" y="417" width="0.1" height="15.0" fill="rgb(231,36,23)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bcopy_ck_size (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`bcopy_ck_size (12 samples, 0.03%)</title><rect x="777.6" y="225" width="0.3" height="15.0" fill="rgb(233,90,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`do_splx (13 samples, 0.03%)')" onmouseout="c()">
<title>unix`do_splx (13 samples, 0.03%)</title><rect x="876.2" y="225" width="0.3" height="15.0" fill="rgb(231,68,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_put (201 samples, 0.44%)')" onmouseout="c()">
<title>kvm`vcpu_put (201 samples, 0.44%)</title><rect x="876.9" y="337" width="4.3" height="15.0" fill="rgb(253,184,15)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`tsc_read (8 samples, 0.02%)')" onmouseout="c()">
<title>unix`tsc_read (8 samples, 0.02%)</title><rect x="972.0" y="401" width="0.2" height="15.0" fill="rgb(234,50,5)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`lock_set (10 samples, 0.02%)')" onmouseout="c()">
<title>unix`lock_set (10 samples, 0.02%)</title><rect x="908.2" y="65" width="0.2" height="15.0" fill="rgb(208,19,19)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`_thrp_setup (45,906 samples, 100.00%)')" onmouseout="c()">
<title>libc.so.1`_thrp_setup (45,906 samples, 100.00%)</title><rect x="10.0" y="545" width="980.0" height="15.0" fill="rgb(228,29,36)" rx="2" ry="2" />
<text text-anchor="" x="13" y="555.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`_thrp_setup</text>
</g>
<g class="func_g" onmouseover="s('genunix`new_mstate (12 samples, 0.03%)')" onmouseout="c()">
<title>genunix`new_mstate (12 samples, 0.03%)</title><rect x="981.8" y="369" width="0.3" height="15.0" fill="rgb(252,197,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('FSS`fss_wakeup (10 samples, 0.02%)')" onmouseout="c()">
<title>FSS`fss_wakeup (10 samples, 0.02%)</title><rect x="903.1" y="113" width="0.2" height="15.0" fill="rgb(238,197,36)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_arch_vcpu_load (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`kvm_arch_vcpu_load (5 samples, 0.01%)</title><rect x="873.3" y="337" width="0.1" height="15.0" fill="rgb(224,144,11)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_mmu_reload (15 samples, 0.03%)')" onmouseout="c()">
<title>kvm`kvm_mmu_reload (15 samples, 0.03%)</title><rect x="719.6" y="305" width="0.3" height="15.0" fill="rgb(253,190,24)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('qemu-system-x86_64`virtio_queue_notify (874 samples, 1.90%)')" onmouseout="c()">
<title>qemu-system-x86_64`virtio_queue_notify (874 samples, 1.90%)</title><rect x="897.4" y="385" width="18.7" height="15.0" fill="rgb(252,150,0)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`scalehrtime (6 samples, 0.01%)')" onmouseout="c()">
<title>genunix`scalehrtime (6 samples, 0.01%)</title><rect x="930.5" y="305" width="0.1" height="15.0" fill="rgb(206,72,51)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_set_rflags (9 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_set_rflags (9 samples, 0.02%)</title><rect x="819.3" y="257" width="0.2" height="15.0" fill="rgb(252,98,10)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`disp_getwork (39 samples, 0.08%)')" onmouseout="c()">
<title>unix`disp_getwork (39 samples, 0.08%)</title><rect x="37.5" y="241" width="0.8" height="15.0" fill="rgb(242,67,47)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vcpu_load (82 samples, 0.18%)')" onmouseout="c()">
<title>kvm`vcpu_load (82 samples, 0.18%)</title><rect x="875.2" y="337" width="1.7" height="15.0" fill="rgb(224,219,14)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`bitset_in_set (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`bitset_in_set (12 samples, 0.03%)</title><rect x="907.9" y="49" width="0.3" height="15.0" fill="rgb(207,43,52)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`kvm_set_shared_msr (7 samples, 0.02%)')" onmouseout="c()">
<title>kvm`kvm_set_shared_msr (7 samples, 0.02%)</title><rect x="720.0" y="305" width="0.2" height="15.0" fill="rgb(240,194,48)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`host_mapping_level (19 samples, 0.04%)')" onmouseout="c()">
<title>kvm`host_mapping_level (19 samples, 0.04%)</title><rect x="843.4" y="225" width="0.5" height="15.0" fill="rgb(243,98,45)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`copyin (12 samples, 0.03%)')" onmouseout="c()">
<title>unix`copyin (12 samples, 0.03%)</title><rect x="800.8" y="97" width="0.2" height="15.0" fill="rgb(243,216,12)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`mutex_enter (11 samples, 0.02%)')" onmouseout="c()">
<title>unix`mutex_enter (11 samples, 0.02%)</title><rect x="911.9" y="225" width="0.2" height="15.0" fill="rgb(238,116,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`apic_find_highest_isr (6 samples, 0.01%)')" onmouseout="c()">
<title>kvm`apic_find_highest_isr (6 samples, 0.01%)</title><rect x="806.5" y="113" width="0.1" height="15.0" fill="rgb(249,139,18)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`mmu_topup_memory_caches (43 samples, 0.09%)')" onmouseout="c()">
<title>kvm`mmu_topup_memory_caches (43 samples, 0.09%)</title><rect x="845.9" y="241" width="0.9" height="15.0" fill="rgb(208,20,29)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmx_interrupt_allowed (5 samples, 0.01%)')" onmouseout="c()">
<title>kvm`vmx_interrupt_allowed (5 samples, 0.01%)</title><rect x="42.8" y="289" width="0.1" height="15.0" fill="rgb(248,43,32)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('kvm`vmcs_readl (12 samples, 0.03%)')" onmouseout="c()">
<title>kvm`vmcs_readl (12 samples, 0.03%)</title><rect x="849.0" y="257" width="0.3" height="15.0" fill="rgb(210,168,50)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`___errno (12 samples, 0.03%)')" onmouseout="c()">
<title>libc.so.1`___errno (12 samples, 0.03%)</title><rect x="10.1" y="497" width="0.3" height="15.0" fill="rgb(253,214,33)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`kmem_zalloc (13 samples, 0.03%)')" onmouseout="c()">
<title>genunix`kmem_zalloc (13 samples, 0.03%)</title><rect x="891.8" y="401" width="0.3" height="15.0" fill="rgb(212,74,27)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('unix`atomic_add_64 (9 samples, 0.02%)')" onmouseout="c()">
<title>unix`atomic_add_64 (9 samples, 0.02%)</title><rect x="912.2" y="241" width="0.2" height="15.0" fill="rgb(222,156,22)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('genunix`sigtimedwait (160 samples, 0.35%)')" onmouseout="c()">
<title>genunix`sigtimedwait (160 samples, 0.35%)</title><rect x="968.4" y="433" width="3.4" height="15.0" fill="rgb(219,142,32)" rx="2" ry="2" />
</g>
</svg>
