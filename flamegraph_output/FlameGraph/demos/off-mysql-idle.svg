<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="770" height="482" onload="init(evt)" viewBox="0 0 770 482" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs >
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#f8f8f8" offset="5%" />
		<stop stop-color="#e8e8e8" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	.func_g:hover { stroke:black; stroke-width:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	var details;
	function init(evt) { details = document.getElementById("details").firstChild; }
	function s(info) { details.nodeValue = "Function: " + info; }
	function c() { details.nodeValue = ' '; }
]]>
</script>
<rect x="0.0" y="0" width="770.0" height="482.0" fill="url(#background)"  />
<text text-anchor="middle" x="385" y="24" font-size="17" font-family="Verdana" fill="rgb(0,0,0)"  >Off-CPU Time Flame Graph</text>
<text text-anchor="" x="10" y="465" font-size="12" font-family="Verdana" fill="rgb(0,0,0)" id="details" > </text>
<g class="func_g" onmouseover="s('mysqld`_start (27,295 ms, 5.19%)')" onmouseout="c()">
<title>mysqld`_start (27,295 ms, 5.19%)</title><rect x="721.0" y="417" width="39.0" height="15.0" fill="rgb(92,92,226)" rx="2" ry="2" />
<text text-anchor="" x="724.043855914122" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`hp_delete_key (504 ms, 0.10%)')" onmouseout="c()">
<title>mysqld`hp_delete_key (504 ms, 0.10%)</title><rect x="232.9" y="97" width="0.8" height="15.0" fill="rgb(131,131,242)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (25,000 ms, 4.76%)</title><rect x="685.4" y="273" width="35.6" height="15.0" fill="rgb(126,126,202)" rx="2" ry="2" />
<text text-anchor="" x="688.36339325717" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`fts_optimize_thread (25,000 ms, 4.76%)')" onmouseout="c()">
<title>mysqld`fts_optimize_thread (25,000 ms, 4.76%)</title><rect x="79.9" y="385" width="35.7" height="15.0" fill="rgb(118,118,191)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`os_thread_sleep (29,001 ms, 5.52%)')" onmouseout="c()">
<title>mysqld`os_thread_sleep (29,001 ms, 5.52%)</title><rect x="644.0" y="369" width="41.4" height="15.0" fill="rgb(102,102,218)" rx="2" ry="2" />
<text text-anchor="" x="646.973569494013" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`net_read_raw_loop (34,742 ms, 6.61%)')" onmouseout="c()">
<title>mysqld`net_read_raw_loop (34,742 ms, 6.61%)</title><rect x="237.6" y="289" width="49.6" height="15.0" fill="rgb(99,99,220)" rx="2" ry="2" />
<text text-anchor="" x="240.588455713752" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`os_event_wait_time_low (25,000 ms, 4.76%)')" onmouseout="c()">
<title>mysqld`os_event_wait_time_low (25,000 ms, 4.76%)</title><rect x="685.4" y="369" width="35.6" height="15.0" fill="rgb(97,97,215)" rx="2" ry="2" />
<text text-anchor="" x="688.36339325717" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`row_sel_field_store_in_mysql_format_func (115 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`row_sel_field_store_in_mysql_format_func (115 ms, 0.02%)</title><rect x="236.1" y="33" width="0.1" height="15.0" fill="rgb(132,132,233)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`ha_innobase::records_in_range (100 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`ha_innobase::records_in_range (100 ms, 0.02%)</title><rect x="236.4" y="97" width="0.2" height="15.0" fill="rgb(84,84,196)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__cond_timedwait (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`__cond_timedwait (29,000 ms, 5.52%)</title><rect x="602.6" y="321" width="41.4" height="15.0" fill="rgb(114,114,193)" rx="2" ry="2" />
<text text-anchor="" x="605.582269869403" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`check_quick_select (101 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`check_quick_select (101 ms, 0.02%)</title><rect x="236.4" y="145" width="0.2" height="15.0" fill="rgb(84,84,229)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`make_join_statistics (329 ms, 0.06%)')" onmouseout="c()">
<title>mysqld`make_join_statistics (329 ms, 0.06%)</title><rect x="236.4" y="193" width="0.5" height="15.0" fill="rgb(126,126,202)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`my_charpos_mb (301 ms, 0.06%)')" onmouseout="c()">
<title>mysqld`my_charpos_mb (301 ms, 0.06%)</title><rect x="234.6" y="65" width="0.4" height="15.0" fill="rgb(93,93,230)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`ha_innobase::general_fetch (296 ms, 0.06%)')" onmouseout="c()">
<title>mysqld`ha_innobase::general_fetch (296 ms, 0.06%)</title><rect x="235.8" y="97" width="0.4" height="15.0" fill="rgb(111,111,213)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`pthread_cond_timedwait (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`pthread_cond_timedwait (25,000 ms, 4.76%)</title><rect x="79.9" y="337" width="35.7" height="15.0" fill="rgb(95,95,218)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`heap_write (1,716 ms, 0.33%)')" onmouseout="c()">
<title>mysqld`heap_write (1,716 ms, 0.33%)</title><rect x="232.9" y="113" width="2.5" height="15.0" fill="rgb(85,85,226)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`dispatch_command (5,390 ms, 1.03%)')" onmouseout="c()">
<title>mysqld`dispatch_command (5,390 ms, 1.03%)</title><rect x="229.8" y="337" width="7.7" height="15.0" fill="rgb(125,125,200)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_common (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_common (25,000 ms, 4.76%)</title><rect x="79.9" y="289" width="35.7" height="15.0" fill="rgb(100,100,222)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_net_read (34,780 ms, 6.62%)')" onmouseout="c()">
<title>mysqld`my_net_read (34,780 ms, 6.62%)</title><rect x="237.5" y="321" width="49.7" height="15.0" fill="rgb(112,112,191)" rx="2" ry="2" />
<text text-anchor="" x="240.53430441277" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`mysql_select (3,224 ms, 0.61%)')" onmouseout="c()">
<title>mysqld`mysql_select (3,224 ms, 0.61%)</title><rect x="232.7" y="225" width="4.6" height="15.0" fill="rgb(106,106,238)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`net_read_packet (34,780 ms, 6.62%)')" onmouseout="c()">
<title>mysqld`net_read_packet (34,780 ms, 6.62%)</title><rect x="237.5" y="305" width="49.7" height="15.0" fill="rgb(81,81,204)" rx="2" ry="2" />
<text text-anchor="" x="240.534703623583" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql..</text>
</g>
<g class="func_g" onmouseover="s('libsocket.so.1`send (1,562 ms, 0.30%)')" onmouseout="c()">
<title>libsocket.so.1`send (1,562 ms, 0.30%)</title><rect x="230.0" y="241" width="2.2" height="15.0" fill="rgb(135,135,210)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`mysqld_stmt_execute (3,575 ms, 0.68%)')" onmouseout="c()">
<title>mysqld`mysqld_stmt_execute (3,575 ms, 0.68%)</title><rect x="232.4" y="321" width="5.1" height="15.0" fill="rgb(120,120,232)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__pollsys (27,295 ms, 5.19%)')" onmouseout="c()">
<title>libc.so.1`__pollsys (27,295 ms, 5.19%)</title><rect x="721.0" y="353" width="39.0" height="15.0" fill="rgb(126,126,195)" rx="2" ry="2" />
<text text-anchor="" x="724.043855914122" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_ismbchar_utf8 (100 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`my_ismbchar_utf8 (100 ms, 0.02%)</title><rect x="233.0" y="49" width="0.1" height="15.0" fill="rgb(129,129,221)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`os_event_wait_time_low (25,000 ms, 4.76%)')" onmouseout="c()">
<title>mysqld`os_event_wait_time_low (25,000 ms, 4.76%)</title><rect x="79.9" y="353" width="35.7" height="15.0" fill="rgb(102,102,237)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`sub_select (2,459 ms, 0.47%)')" onmouseout="c()">
<title>mysqld`sub_select (2,459 ms, 0.47%)</title><rect x="232.8" y="193" width="3.5" height="15.0" fill="rgb(104,104,233)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`JOIN::optimize (491 ms, 0.09%)')" onmouseout="c()">
<title>mysqld`JOIN::optimize (491 ms, 0.09%)</title><rect x="236.3" y="209" width="0.7" height="15.0" fill="rgb(103,103,203)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_queue (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_queue (25,000 ms, 4.76%)</title><rect x="79.9" y="273" width="35.7" height="15.0" fill="rgb(86,86,196)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`Item_equal::compare_const (113 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`Item_equal::compare_const (113 ms, 0.02%)</title><rect x="236.7" y="129" width="0.2" height="15.0" fill="rgb(111,111,220)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`io_handler_thread (51,010 ms, 9.71%)')" onmouseout="c()">
<title>mysqld`io_handler_thread (51,010 ms, 9.71%)</title><rect x="115.6" y="385" width="72.8" height="15.0" fill="rgb(106,106,232)" rx="2" ry="2" />
<text text-anchor="" x="118.615004231609" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld`i..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_common (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_common (25,000 ms, 4.76%)</title><rect x="685.4" y="305" width="35.6" height="15.0" fill="rgb(83,83,214)" rx="2" ry="2" />
<text text-anchor="" x="688.36339325717" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_queue (220,981 ms, 42.05%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_queue (220,981 ms, 42.05%)</title><rect x="287.2" y="273" width="315.4" height="15.0" fill="rgb(109,109,191)" rx="2" ry="2" />
<text text-anchor="" x="290.179462279394" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`cond_wait_queue</text>
</g>
<g class="func_g" onmouseover="s('mysqld`SQL_SELECT::test_quick_select (114 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`SQL_SELECT::test_quick_select (114 ms, 0.02%)</title><rect x="236.4" y="177" width="0.2" height="15.0" fill="rgb(135,135,223)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`handle_one_connection (261,191 ms, 49.70%)')" onmouseout="c()">
<title>mysqld`handle_one_connection (261,191 ms, 49.70%)</title><rect x="229.8" y="369" width="372.8" height="15.0" fill="rgb(117,117,195)" rx="2" ry="2" />
<text text-anchor="" x="232.807041890178" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld`handle_one_connection</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`__so_recv (106 ms, 0.02%)')" onmouseout="c()">
<title>libc.so.1`__so_recv (106 ms, 0.02%)</title><rect x="237.6" y="241" width="0.1" height="15.0" fill="rgb(109,109,228)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`srv_monitor_thread (25,000 ms, 4.76%)')" onmouseout="c()">
<title>mysqld`srv_monitor_thread (25,000 ms, 4.76%)</title><rect x="685.4" y="385" width="35.6" height="15.0" fill="rgb(87,87,211)" rx="2" ry="2" />
<text text-anchor="" x="688.36339325717" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`ib_wqueue_timedwait (25,000 ms, 4.76%)')" onmouseout="c()">
<title>mysqld`ib_wqueue_timedwait (25,000 ms, 4.76%)</title><rect x="79.9" y="369" width="35.7" height="15.0" fill="rgb(133,133,213)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`fil_aio_wait (51,010 ms, 9.71%)')" onmouseout="c()">
<title>mysqld`fil_aio_wait (51,010 ms, 9.71%)</title><rect x="115.6" y="369" width="72.8" height="15.0" fill="rgb(114,114,196)" rx="2" ry="2" />
<text text-anchor="" x="118.615004231609" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld`f..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`hp_rec_hashnr (633 ms, 0.12%)')" onmouseout="c()">
<title>mysqld`hp_rec_hashnr (633 ms, 0.12%)</title><rect x="233.7" y="81" width="0.9" height="15.0" fill="rgb(117,117,218)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_common (20,000 ms, 3.81%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_common (20,000 ms, 3.81%)</title><rect x="51.4" y="305" width="28.5" height="15.0" fill="rgb(90,90,242)" rx="2" ry="2" />
<text text-anchor="" x="54.3900947890408" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >li..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`one_thread_per_connection_end (220,981 ms, 42.05%)')" onmouseout="c()">
<title>mysqld`one_thread_per_connection_end (220,981 ms, 42.05%)</title><rect x="287.2" y="337" width="315.4" height="15.0" fill="rgb(115,115,244)" rx="2" ry="2" />
<text text-anchor="" x="290.179436312722" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld`one_thread_per_connection_end</text>
</g>
<g class="func_g" onmouseover="s('mysqld`lock_clust_rec_cons_read_sees (102 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`lock_clust_rec_cons_read_sees (102 ms, 0.02%)</title><rect x="235.9" y="65" width="0.1" height="15.0" fill="rgb(98,98,191)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`dict_stats_thread (20,000 ms, 3.81%)')" onmouseout="c()">
<title>mysqld`dict_stats_thread (20,000 ms, 3.81%)</title><rect x="51.4" y="385" width="28.5" height="15.0" fill="rgb(80,80,203)" rx="2" ry="2" />
<text text-anchor="" x="54.3900947890408" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`pselect (29,001 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`pselect (29,001 ms, 5.52%)</title><rect x="644.0" y="337" width="41.4" height="15.0" fill="rgb(117,117,225)" rx="2" ry="2" />
<text text-anchor="" x="646.973569494013" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_utf8_uni (182 ms, 0.03%)')" onmouseout="c()">
<title>mysqld`my_utf8_uni (182 ms, 0.03%)</title><rect x="233.1" y="49" width="0.3" height="15.0" fill="rgb(90,90,241)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`Prepared_statement::execute_loop (3,572 ms, 0.68%)')" onmouseout="c()">
<title>mysqld`Prepared_statement::execute_loop (3,572 ms, 0.68%)</title><rect x="232.4" y="305" width="5.1" height="15.0" fill="rgb(113,113,208)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__pollsys (34,616 ms, 6.59%)')" onmouseout="c()">
<title>libc.so.1`__pollsys (34,616 ms, 6.59%)</title><rect x="237.7" y="209" width="49.4" height="15.0" fill="rgb(101,101,225)" rx="2" ry="2" />
<text text-anchor="" x="240.741981740154" y="219.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_ismbchar_utf8 (74 ms, 0.01%)')" onmouseout="c()">
<title>mysqld`my_ismbchar_utf8 (74 ms, 0.01%)</title><rect x="235.0" y="65" width="0.2" height="15.0" fill="rgb(125,125,239)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__cond_timedwait (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`__cond_timedwait (29,000 ms, 5.52%)</title><rect x="188.4" y="321" width="41.4" height="15.0" fill="rgb(128,128,219)" rx="2" ry="2" />
<text text-anchor="" x="191.41750356916" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (20,000 ms, 3.81%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (20,000 ms, 3.81%)</title><rect x="51.4" y="273" width="28.5" height="15.0" fill="rgb(131,131,200)" rx="2" ry="2" />
<text text-anchor="" x="54.3900947890408" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >li..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`ha_heap::write_row (1,717 ms, 0.33%)')" onmouseout="c()">
<title>mysqld`ha_heap::write_row (1,717 ms, 0.33%)</title><rect x="232.9" y="129" width="2.5" height="15.0" fill="rgb(102,102,216)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`THD::set_query (74 ms, 0.01%)')" onmouseout="c()">
<title>mysqld`THD::set_query (74 ms, 0.01%)</title><rect x="232.3" y="321" width="0.1" height="15.0" fill="rgb(97,97,234)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`handler::multi_range_read_next (395 ms, 0.08%)')" onmouseout="c()">
<title>mysqld`handler::multi_range_read_next (395 ms, 0.08%)</title><rect x="235.7" y="145" width="0.5" height="15.0" fill="rgb(138,138,198)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait (51,007 ms, 9.71%)')" onmouseout="c()">
<title>libc.so.1`cond_wait (51,007 ms, 9.71%)</title><rect x="115.6" y="305" width="72.8" height="15.0" fill="rgb(119,119,241)" rx="2" ry="2" />
<text text-anchor="" x="118.619767644354" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so...</text>
</g>
<g class="func_g" onmouseover="s('mysqld`row_search_for_mysql (284 ms, 0.05%)')" onmouseout="c()">
<title>mysqld`row_search_for_mysql (284 ms, 0.05%)</title><rect x="235.8" y="81" width="0.4" height="15.0" fill="rgb(135,135,218)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_common (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_common (29,000 ms, 5.52%)</title><rect x="602.6" y="305" width="41.4" height="15.0" fill="rgb(95,95,190)" rx="2" ry="2" />
<text text-anchor="" x="605.582269869403" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`handle_select (3,227 ms, 0.61%)')" onmouseout="c()">
<title>mysqld`handle_select (3,227 ms, 0.61%)</title><rect x="232.7" y="241" width="4.6" height="15.0" fill="rgb(105,105,236)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`select (29,001 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`select (29,001 ms, 5.52%)</title><rect x="644.0" y="353" width="41.4" height="15.0" fill="rgb(111,111,213)" rx="2" ry="2" />
<text text-anchor="" x="646.973569494013" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`net_flush (1,563 ms, 0.30%)')" onmouseout="c()">
<title>mysqld`net_flush (1,563 ms, 0.30%)</title><rect x="230.0" y="289" width="2.2" height="15.0" fill="rgb(91,91,237)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`pthread_cond_timedwait (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`pthread_cond_timedwait (29,000 ms, 5.52%)</title><rect x="602.6" y="353" width="41.4" height="15.0" fill="rgb(86,86,229)" rx="2" ry="2" />
<text text-anchor="" x="605.582269869403" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`handle_connections_sockets (27,295 ms, 5.19%)')" onmouseout="c()">
<title>mysqld`handle_connections_sockets (27,295 ms, 5.19%)</title><rect x="721.0" y="385" width="39.0" height="15.0" fill="rgb(127,127,211)" rx="2" ry="2" />
<text text-anchor="" x="724.043855914122" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_timedwait (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`cond_timedwait (29,000 ms, 5.52%)</title><rect x="188.4" y="337" width="41.4" height="15.0" fill="rgb(123,123,201)" rx="2" ry="2" />
<text text-anchor="" x="191.41750356916" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`__pollsys (29,001 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`__pollsys (29,001 ms, 5.52%)</title><rect x="644.0" y="321" width="41.4" height="15.0" fill="rgb(118,118,204)" rx="2" ry="2" />
<text text-anchor="" x="646.973569494013" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('all (525,501 ms, 100%)')" onmouseout="c()">
<title>all (525,501 ms, 100%)</title><rect x="10.0" y="433" width="750.0" height="15.0" fill="rgb(132,132,198)" rx="2" ry="2" />
<text text-anchor="" x="13" y="443.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  ></text>
</g>
<g class="func_g" onmouseover="s('mysqld`net_send_eof (1,564 ms, 0.30%)')" onmouseout="c()">
<title>mysqld`net_send_eof (1,564 ms, 0.30%)</title><rect x="230.0" y="305" width="2.2" height="15.0" fill="rgb(111,111,243)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_queue (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_queue (29,000 ms, 5.52%)</title><rect x="188.4" y="289" width="41.4" height="15.0" fill="rgb(81,81,193)" rx="2" ry="2" />
<text text-anchor="" x="191.41750356916" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`mysql_execute_command (3,479 ms, 0.66%)')" onmouseout="c()">
<title>mysqld`mysql_execute_command (3,479 ms, 0.66%)</title><rect x="232.5" y="273" width="5.0" height="15.0" fill="rgb(104,104,200)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__pollsys (28,998 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`__pollsys (28,998 ms, 5.52%)</title><rect x="10.0" y="321" width="41.4" height="15.0" fill="rgb(88,88,239)" rx="2" ry="2" />
<text text-anchor="" x="13.0032681683435" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`pthread_cond_wait (220,981 ms, 42.05%)')" onmouseout="c()">
<title>libc.so.1`pthread_cond_wait (220,981 ms, 42.05%)</title><rect x="287.2" y="321" width="315.4" height="15.0" fill="rgb(126,126,192)" rx="2" ry="2" />
<text text-anchor="" x="290.179462279394" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`pthread_cond_wait</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`_thrp_setup (498,205 ms, 94.81%)')" onmouseout="c()">
<title>libc.so.1`_thrp_setup (498,205 ms, 94.81%)</title><rect x="10.0" y="401" width="711.0" height="15.0" fill="rgb(115,115,244)" rx="2" ry="2" />
<text text-anchor="" x="13.0000317582924" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`_thrp_setup</text>
</g>
<g class="func_g" onmouseover="s('mysqld`Prepared_statement::execute (3,555 ms, 0.68%)')" onmouseout="c()">
<title>mysqld`Prepared_statement::execute (3,555 ms, 0.68%)</title><rect x="232.4" y="289" width="5.1" height="15.0" fill="rgb(101,101,225)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__cond_wait (220,981 ms, 42.05%)')" onmouseout="c()">
<title>libc.so.1`__cond_wait (220,981 ms, 42.05%)</title><rect x="287.2" y="289" width="315.4" height="15.0" fill="rgb(97,97,242)" rx="2" ry="2" />
<text text-anchor="" x="290.179462279394" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`__cond_wait</text>
</g>
<g class="func_g" onmouseover="s('mysqld`handler::multi_range_read_info_const (101 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`handler::multi_range_read_info_const (101 ms, 0.02%)</title><rect x="236.4" y="113" width="0.2" height="15.0" fill="rgb(93,93,237)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`row_sel_store_mysql_field_func (133 ms, 0.03%)')" onmouseout="c()">
<title>mysqld`row_sel_store_mysql_field_func (133 ms, 0.03%)</title><rect x="236.0" y="49" width="0.2" height="15.0" fill="rgb(102,102,214)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`btr_estimate_n_rows_in_range (94 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`btr_estimate_n_rows_in_range (94 ms, 0.02%)</title><rect x="236.4" y="81" width="0.2" height="15.0" fill="rgb(85,85,191)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`pthread_cond_wait (51,007 ms, 9.71%)')" onmouseout="c()">
<title>libc.so.1`pthread_cond_wait (51,007 ms, 9.71%)</title><rect x="115.6" y="321" width="72.8" height="15.0" fill="rgb(99,99,224)" rx="2" ry="2" />
<text text-anchor="" x="118.619767644354" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so...</text>
</g>
<g class="func_g" onmouseover="s('mysqld`get_key_scans_params (101 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`get_key_scans_params (101 ms, 0.02%)</title><rect x="236.4" y="161" width="0.2" height="15.0" fill="rgb(86,86,214)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`close_thread_tables (103 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`close_thread_tables (103 ms, 0.02%)</title><rect x="232.5" y="257" width="0.2" height="15.0" fill="rgb(132,132,229)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`srv_error_monitor_thread (29,000 ms, 5.52%)')" onmouseout="c()">
<title>mysqld`srv_error_monitor_thread (29,000 ms, 5.52%)</title><rect x="602.6" y="385" width="41.4" height="15.0" fill="rgb(90,90,202)" rx="2" ry="2" />
<text text-anchor="" x="605.582269869403" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_timedwait (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`cond_timedwait (25,000 ms, 4.76%)</title><rect x="685.4" y="337" width="35.6" height="15.0" fill="rgb(89,89,190)" rx="2" ry="2" />
<text text-anchor="" x="688.36339325717" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`handler::ha_index_next (350 ms, 0.07%)')" onmouseout="c()">
<title>mysqld`handler::ha_index_next (350 ms, 0.07%)</title><rect x="235.7" y="113" width="0.5" height="15.0" fill="rgb(112,112,223)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`my_hash_sort_utf8 (78 ms, 0.01%)')" onmouseout="c()">
<title>mysqld`my_hash_sort_utf8 (78 ms, 0.01%)</title><rect x="233.4" y="65" width="0.1" height="15.0" fill="rgb(132,132,240)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`Item_func::type (110 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`Item_func::type (110 ms, 0.02%)</title><rect x="236.7" y="97" width="0.2" height="15.0" fill="rgb(88,88,215)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`vio_write (1,563 ms, 0.30%)')" onmouseout="c()">
<title>mysqld`vio_write (1,563 ms, 0.30%)</title><rect x="230.0" y="257" width="2.2" height="15.0" fill="rgb(134,134,226)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`pthread_cond_timedwait (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`pthread_cond_timedwait (25,000 ms, 4.76%)</title><rect x="685.4" y="353" width="35.6" height="15.0" fill="rgb(138,138,211)" rx="2" ry="2" />
<text text-anchor="" x="688.36339325717" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_ismbchar_utf8 (146 ms, 0.03%)')" onmouseout="c()">
<title>mysqld`my_ismbchar_utf8 (146 ms, 0.03%)</title><rect x="233.9" y="49" width="0.2" height="15.0" fill="rgb(129,129,206)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`rr_quick (400 ms, 0.08%)')" onmouseout="c()">
<title>mysqld`rr_quick (400 ms, 0.08%)</title><rect x="235.7" y="177" width="0.5" height="15.0" fill="rgb(90,90,239)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`set_thread_state_v1 (119 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`set_thread_state_v1 (119 ms, 0.02%)</title><rect x="236.9" y="193" width="0.1" height="15.0" fill="rgb(137,137,198)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`_lwp_start (498,205 ms, 94.81%)')" onmouseout="c()">
<title>libc.so.1`_lwp_start (498,205 ms, 94.81%)</title><rect x="10.0" y="417" width="711.0" height="15.0" fill="rgb(92,92,243)" rx="2" ry="2" />
<text text-anchor="" x="13.0000317582924" y="427.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`_lwp_start</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_ismbchar_utf8 (117 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`my_ismbchar_utf8 (117 ms, 0.02%)</title><rect x="234.8" y="49" width="0.1" height="15.0" fill="rgb(87,87,196)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_common (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_common (29,000 ms, 5.52%)</title><rect x="188.4" y="305" width="41.4" height="15.0" fill="rgb(135,135,192)" rx="2" ry="2" />
<text text-anchor="" x="191.41750356916" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_charpos_mb (460 ms, 0.09%)')" onmouseout="c()">
<title>mysqld`my_charpos_mb (460 ms, 0.09%)</title><rect x="233.7" y="65" width="0.7" height="15.0" fill="rgb(97,97,233)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (29,000 ms, 5.52%)</title><rect x="188.4" y="273" width="41.4" height="15.0" fill="rgb(83,83,224)" rx="2" ry="2" />
<text text-anchor="" x="191.41750356916" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_utf8_uni (165 ms, 0.03%)')" onmouseout="c()">
<title>mysqld`my_utf8_uni (165 ms, 0.03%)</title><rect x="234.1" y="49" width="0.3" height="15.0" fill="rgb(114,114,235)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`srv_master_thread (29,002 ms, 5.52%)')" onmouseout="c()">
<title>mysqld`srv_master_thread (29,002 ms, 5.52%)</title><rect x="644.0" y="385" width="41.4" height="15.0" fill="rgb(116,116,203)" rx="2" ry="2" />
<text text-anchor="" x="646.971809743226" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`mysqld_main (27,295 ms, 5.19%)')" onmouseout="c()">
<title>mysqld`mysqld_main (27,295 ms, 5.19%)</title><rect x="721.0" y="401" width="39.0" height="15.0" fill="rgb(110,110,218)" rx="2" ry="2" />
<text text-anchor="" x="724.043855914122" y="411.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`vio_socket_io_wait (34,634 ms, 6.59%)')" onmouseout="c()">
<title>mysqld`vio_socket_io_wait (34,634 ms, 6.59%)</title><rect x="237.7" y="257" width="49.5" height="15.0" fill="rgb(115,115,220)" rx="2" ry="2" />
<text text-anchor="" x="240.741771469202" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysq..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`pthread_cond_timedwait (20,000 ms, 3.81%)')" onmouseout="c()">
<title>libc.so.1`pthread_cond_timedwait (20,000 ms, 3.81%)</title><rect x="51.4" y="353" width="28.5" height="15.0" fill="rgb(101,101,221)" rx="2" ry="2" />
<text text-anchor="" x="54.3900947890408" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >li..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`QUICK_RANGE_SELECT::get_next (400 ms, 0.08%)')" onmouseout="c()">
<title>mysqld`QUICK_RANGE_SELECT::get_next (400 ms, 0.08%)</title><rect x="235.7" y="161" width="0.5" height="15.0" fill="rgb(113,113,206)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__cond_timedwait (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`__cond_timedwait (25,000 ms, 4.76%)</title><rect x="79.9" y="305" width="35.7" height="15.0" fill="rgb(95,95,241)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`row_sel_store_mysql_rec (136 ms, 0.03%)')" onmouseout="c()">
<title>mysqld`row_sel_store_mysql_rec (136 ms, 0.03%)</title><rect x="236.0" y="65" width="0.2" height="15.0" fill="rgb(86,86,190)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`join_init_read_record (190 ms, 0.04%)')" onmouseout="c()">
<title>mysqld`join_init_read_record (190 ms, 0.04%)</title><rect x="235.4" y="177" width="0.3" height="15.0" fill="rgb(139,139,191)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`my_strnncollsp_utf8 (113 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`my_strnncollsp_utf8 (113 ms, 0.02%)</title><rect x="235.2" y="65" width="0.1" height="15.0" fill="rgb(139,139,203)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`vio_io_wait (34,634 ms, 6.59%)')" onmouseout="c()">
<title>mysqld`vio_io_wait (34,634 ms, 6.59%)</title><rect x="237.7" y="241" width="49.5" height="15.0" fill="rgb(81,81,226)" rx="2" ry="2" />
<text text-anchor="" x="240.741889830639" y="251.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysq..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_queue (20,000 ms, 3.81%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_queue (20,000 ms, 3.81%)</title><rect x="51.4" y="289" width="28.5" height="15.0" fill="rgb(89,89,233)" rx="2" ry="2" />
<text text-anchor="" x="54.3900947890408" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >li..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`DsMrr_impl::dsmrr_info_const (101 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`DsMrr_impl::dsmrr_info_const (101 ms, 0.02%)</title><rect x="236.4" y="129" width="0.2" height="15.0" fill="rgb(117,117,221)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`my_strnxfrm_unicode (83 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`my_strnxfrm_unicode (83 ms, 0.02%)</title><rect x="235.5" y="97" width="0.1" height="15.0" fill="rgb(107,107,236)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`my_utf8_uni (73 ms, 0.01%)')" onmouseout="c()">
<title>mysqld`my_utf8_uni (73 ms, 0.01%)</title><rect x="234.9" y="49" width="0.1" height="15.0" fill="rgb(136,136,195)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`st_join_table::sort_table (178 ms, 0.03%)')" onmouseout="c()">
<title>mysqld`st_join_table::sort_table (178 ms, 0.03%)</title><rect x="235.4" y="161" width="0.3" height="15.0" fill="rgb(81,81,202)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`hp_rec_hashnr (504 ms, 0.10%)')" onmouseout="c()">
<title>mysqld`hp_rec_hashnr (504 ms, 0.10%)</title><rect x="232.9" y="81" width="0.8" height="15.0" fill="rgb(126,126,238)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`Protocol::end_statement (1,577 ms, 0.30%)')" onmouseout="c()">
<title>mysqld`Protocol::end_statement (1,577 ms, 0.30%)</title><rect x="230.0" y="321" width="2.3" height="15.0" fill="rgb(97,97,240)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`Field_string::make_sort_key (101 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`Field_string::make_sort_key (101 ms, 0.02%)</title><rect x="235.5" y="113" width="0.2" height="15.0" fill="rgb(106,106,190)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`execute_sqlcom_select (3,296 ms, 0.63%)')" onmouseout="c()">
<title>mysqld`execute_sqlcom_select (3,296 ms, 0.63%)</title><rect x="232.7" y="257" width="4.7" height="15.0" fill="rgb(101,101,209)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`poll (27,295 ms, 5.19%)')" onmouseout="c()">
<title>libc.so.1`poll (27,295 ms, 5.19%)</title><rect x="721.0" y="369" width="39.0" height="15.0" fill="rgb(103,103,202)" rx="2" ry="2" />
<text text-anchor="" x="724.043855914122" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_timedwait (20,000 ms, 3.81%)')" onmouseout="c()">
<title>libc.so.1`cond_timedwait (20,000 ms, 3.81%)</title><rect x="51.4" y="337" width="28.5" height="15.0" fill="rgb(99,99,190)" rx="2" ry="2" />
<text text-anchor="" x="54.3900947890408" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >li..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (220,981 ms, 42.05%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (220,981 ms, 42.05%)</title><rect x="287.2" y="257" width="315.4" height="15.0" fill="rgb(88,88,194)" rx="2" ry="2" />
<text text-anchor="" x="290.179462279394" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`__lwp_park</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_hash_sort_utf8 (102 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`my_hash_sort_utf8 (102 ms, 0.02%)</title><rect x="234.4" y="65" width="0.1" height="15.0" fill="rgb(92,92,206)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`join_read_const (71 ms, 0.01%)')" onmouseout="c()">
<title>mysqld`join_read_const (71 ms, 0.01%)</title><rect x="236.6" y="161" width="0.1" height="15.0" fill="rgb(121,121,193)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__cond_timedwait (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`__cond_timedwait (25,000 ms, 4.76%)</title><rect x="685.4" y="321" width="35.6" height="15.0" fill="rgb(87,87,210)" rx="2" ry="2" />
<text text-anchor="" x="688.36339325717" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`make_sortkey (110 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`make_sortkey (110 ms, 0.02%)</title><rect x="235.5" y="129" width="0.2" height="15.0" fill="rgb(100,100,221)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_queue (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_queue (25,000 ms, 4.76%)</title><rect x="685.4" y="289" width="35.6" height="15.0" fill="rgb(118,118,232)" rx="2" ry="2" />
<text text-anchor="" x="688.36339325717" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`pfs_spawn_thread (261,191 ms, 49.70%)')" onmouseout="c()">
<title>mysqld`pfs_spawn_thread (261,191 ms, 49.70%)</title><rect x="229.8" y="385" width="372.8" height="15.0" fill="rgb(130,130,193)" rx="2" ry="2" />
<text text-anchor="" x="232.807041890178" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld`pfs_spawn_thread</text>
</g>
<g class="func_g" onmouseover="s('mysqld`end_write (1,722 ms, 0.33%)')" onmouseout="c()">
<title>mysqld`end_write (1,722 ms, 0.33%)</title><rect x="232.9" y="161" width="2.5" height="15.0" fill="rgb(120,120,233)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__so_send (1,560 ms, 0.30%)')" onmouseout="c()">
<title>libc.so.1`__so_send (1,560 ms, 0.30%)</title><rect x="230.0" y="225" width="2.2" height="15.0" fill="rgb(121,121,193)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`end_send (84 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`end_send (84 ms, 0.02%)</title><rect x="232.8" y="161" width="0.1" height="15.0" fill="rgb(109,109,209)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`os_event_wait_time_low (29,000 ms, 5.52%)')" onmouseout="c()">
<title>mysqld`os_event_wait_time_low (29,000 ms, 5.52%)</title><rect x="602.6" y="369" width="41.4" height="15.0" fill="rgb(83,83,191)" rx="2" ry="2" />
<text text-anchor="" x="605.582269869403" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`os_thread_sleep (28,998 ms, 5.52%)')" onmouseout="c()">
<title>mysqld`os_thread_sleep (28,998 ms, 5.52%)</title><rect x="10.0" y="369" width="41.4" height="15.0" fill="rgb(105,105,220)" rx="2" ry="2" />
<text text-anchor="" x="13.0032681683435" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`join_read_const_table (188 ms, 0.04%)')" onmouseout="c()">
<title>mysqld`join_read_const_table (188 ms, 0.04%)</title><rect x="236.6" y="177" width="0.3" height="15.0" fill="rgb(128,128,210)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`my_charpos_mb (320 ms, 0.06%)')" onmouseout="c()">
<title>mysqld`my_charpos_mb (320 ms, 0.06%)</title><rect x="233.0" y="65" width="0.4" height="15.0" fill="rgb(80,80,213)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`os_event_wait_time_low (20,000 ms, 3.81%)')" onmouseout="c()">
<title>mysqld`os_event_wait_time_low (20,000 ms, 3.81%)</title><rect x="51.4" y="369" width="28.5" height="15.0" fill="rgb(122,122,198)" rx="2" ry="2" />
<text text-anchor="" x="54.3900947890408" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >my..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`buf_flush_page_cleaner_thread (29,001 ms, 5.52%)')" onmouseout="c()">
<title>mysqld`buf_flush_page_cleaner_thread (29,001 ms, 5.52%)</title><rect x="10.0" y="385" width="41.4" height="15.0" fill="rgb(117,117,193)" rx="2" ry="2" />
<text text-anchor="" x="13.0000317582924" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`evaluate_join_record (1,843 ms, 0.35%)')" onmouseout="c()">
<title>mysqld`evaluate_join_record (1,843 ms, 0.35%)</title><rect x="232.8" y="177" width="2.6" height="15.0" fill="rgb(139,139,233)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__cond_wait (51,007 ms, 9.71%)')" onmouseout="c()">
<title>libc.so.1`__cond_wait (51,007 ms, 9.71%)</title><rect x="115.6" y="289" width="72.8" height="15.0" fill="rgb(127,127,209)" rx="2" ry="2" />
<text text-anchor="" x="118.619767644354" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so...</text>
</g>
<g class="func_g" onmouseover="s('libsocket.so.1`recv (106 ms, 0.02%)')" onmouseout="c()">
<title>libsocket.so.1`recv (106 ms, 0.02%)</title><rect x="237.6" y="257" width="0.1" height="15.0" fill="rgb(127,127,223)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`os_event_wait_time_low (29,000 ms, 5.52%)')" onmouseout="c()">
<title>mysqld`os_event_wait_time_low (29,000 ms, 5.52%)</title><rect x="188.4" y="369" width="41.4" height="15.0" fill="rgb(98,98,227)" rx="2" ry="2" />
<text text-anchor="" x="191.41750356916" y="379.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait (220,981 ms, 42.05%)')" onmouseout="c()">
<title>libc.so.1`cond_wait (220,981 ms, 42.05%)</title><rect x="287.2" y="305" width="315.4" height="15.0" fill="rgb(135,135,211)" rx="2" ry="2" />
<text text-anchor="" x="290.179462279394" y="315.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so.1`cond_wait</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`select (28,998 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`select (28,998 ms, 5.52%)</title><rect x="10.0" y="353" width="41.4" height="15.0" fill="rgb(104,104,244)" rx="2" ry="2" />
<text text-anchor="" x="13.0032681683435" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (25,000 ms, 4.76%)</title><rect x="79.9" y="257" width="35.7" height="15.0" fill="rgb(118,118,208)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_queue (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_queue (29,000 ms, 5.52%)</title><rect x="602.6" y="289" width="41.4" height="15.0" fill="rgb(107,107,228)" rx="2" ry="2" />
<text text-anchor="" x="605.582269869403" y="299.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`my_ismbchar_utf8 (103 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`my_ismbchar_utf8 (103 ms, 0.02%)</title><rect x="233.5" y="65" width="0.2" height="15.0" fill="rgb(134,134,200)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`do_command (34,780 ms, 6.62%)')" onmouseout="c()">
<title>mysqld`do_command (34,780 ms, 6.62%)</title><rect x="237.5" y="337" width="49.7" height="15.0" fill="rgb(89,89,221)" rx="2" ry="2" />
<text text-anchor="" x="240.53430441277" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_wait_queue (51,007 ms, 9.71%)')" onmouseout="c()">
<title>libc.so.1`cond_wait_queue (51,007 ms, 9.71%)</title><rect x="115.6" y="273" width="72.8" height="15.0" fill="rgb(125,125,192)" rx="2" ry="2" />
<text text-anchor="" x="118.619767644354" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so...</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`poll (34,634 ms, 6.59%)')" onmouseout="c()">
<title>libc.so.1`poll (34,634 ms, 6.59%)</title><rect x="237.7" y="225" width="49.5" height="15.0" fill="rgb(131,131,199)" rx="2" ry="2" />
<text text-anchor="" x="240.741981740154" y="235.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`os_event_wait_low (51,007 ms, 9.71%)')" onmouseout="c()">
<title>mysqld`os_event_wait_low (51,007 ms, 9.71%)</title><rect x="115.6" y="337" width="72.8" height="15.0" fill="rgb(129,129,227)" rx="2" ry="2" />
<text text-anchor="" x="118.619767644354" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld`o..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`Item_equal::update_const (114 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`Item_equal::update_const (114 ms, 0.02%)</title><rect x="236.7" y="145" width="0.2" height="15.0" fill="rgb(135,135,202)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`update_const_equal_items (114 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`update_const_equal_items (114 ms, 0.02%)</title><rect x="236.7" y="161" width="0.2" height="15.0" fill="rgb(97,97,224)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`net_write_packet (1,563 ms, 0.30%)')" onmouseout="c()">
<title>mysqld`net_write_packet (1,563 ms, 0.30%)</title><rect x="230.0" y="273" width="2.2" height="15.0" fill="rgb(95,95,198)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`lock_wait_timeout_thread (29,000 ms, 5.52%)')" onmouseout="c()">
<title>mysqld`lock_wait_timeout_thread (29,000 ms, 5.52%)</title><rect x="188.4" y="385" width="41.4" height="15.0" fill="rgb(128,128,198)" rx="2" ry="2" />
<text text-anchor="" x="191.41750356916" y="395.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mys..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`vio_read (34,742 ms, 6.61%)')" onmouseout="c()">
<title>mysqld`vio_read (34,742 ms, 6.61%)</title><rect x="237.6" y="273" width="49.6" height="15.0" fill="rgb(139,139,205)" rx="2" ry="2" />
<text text-anchor="" x="240.588931241801" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysql..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_timedwait (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`cond_timedwait (29,000 ms, 5.52%)</title><rect x="602.6" y="337" width="41.4" height="15.0" fill="rgb(84,84,204)" rx="2" ry="2" />
<text text-anchor="" x="605.582269869403" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`os_aio_simulated_handle (51,007 ms, 9.71%)')" onmouseout="c()">
<title>mysqld`os_aio_simulated_handle (51,007 ms, 9.71%)</title><rect x="115.6" y="353" width="72.8" height="15.0" fill="rgb(88,88,217)" rx="2" ry="2" />
<text text-anchor="" x="118.619767644354" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld`o..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`PROFILING::status_change (92 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`PROFILING::status_change (92 ms, 0.02%)</title><rect x="229.9" y="321" width="0.1" height="15.0" fill="rgb(111,111,195)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`Arg_comparator::set_cmp_func (111 ms, 0.02%)')" onmouseout="c()">
<title>mysqld`Arg_comparator::set_cmp_func (111 ms, 0.02%)</title><rect x="236.7" y="113" width="0.2" height="15.0" fill="rgb(93,93,229)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`pselect (28,998 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`pselect (28,998 ms, 5.52%)</title><rect x="10.0" y="337" width="41.4" height="15.0" fill="rgb(113,113,225)" rx="2" ry="2" />
<text text-anchor="" x="13.0032681683435" y="347.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`JOIN::exec (2,561 ms, 0.49%)')" onmouseout="c()">
<title>mysqld`JOIN::exec (2,561 ms, 0.49%)</title><rect x="232.7" y="209" width="3.6" height="15.0" fill="rgb(115,115,202)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`hp_write_key (1,187 ms, 0.23%)')" onmouseout="c()">
<title>mysqld`hp_write_key (1,187 ms, 0.23%)</title><rect x="233.7" y="97" width="1.7" height="15.0" fill="rgb(94,94,198)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (51,007 ms, 9.71%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (51,007 ms, 9.71%)</title><rect x="115.6" y="257" width="72.8" height="15.0" fill="rgb(109,109,231)" rx="2" ry="2" />
<text text-anchor="" x="118.619767644354" y="267.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >libc.so...</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`cond_timedwait (25,000 ms, 4.76%)')" onmouseout="c()">
<title>libc.so.1`cond_timedwait (25,000 ms, 4.76%)</title><rect x="79.9" y="321" width="35.7" height="15.0" fill="rgb(123,123,190)" rx="2" ry="2" />
<text text-anchor="" x="82.9345941331173" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`pthread_cond_timedwait (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`pthread_cond_timedwait (29,000 ms, 5.52%)</title><rect x="188.4" y="353" width="41.4" height="15.0" fill="rgb(126,126,205)" rx="2" ry="2" />
<text text-anchor="" x="191.41750356916" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`hp_rec_key_cmp (547 ms, 0.10%)')" onmouseout="c()">
<title>mysqld`hp_rec_key_cmp (547 ms, 0.10%)</title><rect x="234.6" y="81" width="0.8" height="15.0" fill="rgb(130,130,233)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`do_handle_one_connection (261,191 ms, 49.70%)')" onmouseout="c()">
<title>mysqld`do_handle_one_connection (261,191 ms, 49.70%)</title><rect x="229.8" y="353" width="372.8" height="15.0" fill="rgb(96,96,224)" rx="2" ry="2" />
<text text-anchor="" x="232.807041890178" y="363.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >mysqld`do_handle_one_connection</text>
</g>
<g class="func_g" onmouseover="s('libc.so.1`__cond_timedwait (20,000 ms, 3.81%)')" onmouseout="c()">
<title>libc.so.1`__cond_timedwait (20,000 ms, 3.81%)</title><rect x="51.4" y="321" width="28.5" height="15.0" fill="rgb(135,135,199)" rx="2" ry="2" />
<text text-anchor="" x="54.3900947890408" y="331.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >li..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`filesort (174 ms, 0.03%)')" onmouseout="c()">
<title>mysqld`filesort (174 ms, 0.03%)</title><rect x="235.4" y="145" width="0.3" height="15.0" fill="rgb(89,89,209)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('libc.so.1`__lwp_park (29,000 ms, 5.52%)')" onmouseout="c()">
<title>libc.so.1`__lwp_park (29,000 ms, 5.52%)</title><rect x="602.6" y="273" width="41.4" height="15.0" fill="rgb(116,116,241)" rx="2" ry="2" />
<text text-anchor="" x="605.582269869403" y="283.5" font-size="12" font-family="Verdana" fill="rgb(0,0,0)"  >lib..</text>
</g>
<g class="func_g" onmouseover="s('mysqld`handler::ha_write_row (1,719 ms, 0.33%)')" onmouseout="c()">
<title>mysqld`handler::ha_write_row (1,719 ms, 0.33%)</title><rect x="232.9" y="145" width="2.5" height="15.0" fill="rgb(131,131,228)" rx="2" ry="2" />
</g>
<g class="func_g" onmouseover="s('mysqld`handler::read_range_next (372 ms, 0.07%)')" onmouseout="c()">
<title>mysqld`handler::read_range_next (372 ms, 0.07%)</title><rect x="235.7" y="129" width="0.5" height="15.0" fill="rgb(113,113,207)" rx="2" ry="2" />
</g>
</svg>
