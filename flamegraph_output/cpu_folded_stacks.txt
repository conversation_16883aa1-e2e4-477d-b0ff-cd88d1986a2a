all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command 166
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command 54
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 131
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select 59
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec 78
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record 47
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next 55
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 62
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc 69
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level 42
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level;buf_page_get_gen 61
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command 238
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command 158
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 214
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert 84
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record 79
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row 88
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row 103
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 35
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql;btr_cur_optimistic_insert 28
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 49
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch 31
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low 39
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io 25
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 62
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func;io_submit 38
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;log_writer;log_write_up_to 228
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;log_writer;log_write_up_to;log_write_blocks 222
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func 110
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 121
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite;sys_pwrite64 56
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;srv_master_thread;srv_master_do_active_tasks 266
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close 164
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close;lock_rec_free_all_from_discard_page 41
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch 96
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;validate_records 113
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity 249
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 39
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match 39
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match;pcre_exec 24
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch 137
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;transform_data 82
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules 100
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 116
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations 119
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations;floating_point_calc 37
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch 215
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;clean_data 49
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates 116
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 111
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash 27
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash;crypto_hash_update 88
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;file_handler;read_csv_file 32
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;file_handler;read_csv_file;parse_csv_line 251
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields 119
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize 44
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy 65
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy;__memcpy_avx_unaligned 98
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;api_client;send_http_request 250
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;api_client;send_http_request;build_request_payload 76
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;api_client;send_http_request;build_request_payload;json_encode 46
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string 51
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string;string_append 22
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 243
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto 47
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto 95
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg 29
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg 54
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg 71
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 89
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb 102
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 52
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 19
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 69
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom 161
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom 78
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg 183
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg 82
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 14
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 50
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_rcv_established 74
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;net_rx_action;__napi_poll 105
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;net_rx_action;__napi_poll;e1000_clean 138
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq 30
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb 93
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core 12
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 141
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 37
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;dst_input 63
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;page_fault;do_page_fault 26
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;page_fault;do_page_fault;__do_page_fault 82
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 100
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault 120
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault 49
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 81
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable 102
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable;__alloc_pages_nodemask 114
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;allocate_buffer 77
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;allocate_buffer;malloc 112
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc 73
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 41
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc 73
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap;sys_mmap 84
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;free_buffer 96
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;free_buffer;free 119
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;free_buffer;free;__libc_free 155
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 122
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap 72
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap;sys_munmap 125
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 43
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 55
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read 125
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read 24
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter 121
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter 98
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 64
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead;ondemand_readahead 57
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 222
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 29
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write 187
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write 21
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter 76
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter 109
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 129
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write;ext4_da_write_begin 63
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread 120
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;wait_for_task 108
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait 63
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 65
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex 81
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex;futex_wait_queue_me 35
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread 72
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;execute_task 209
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk 224
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 85
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation 92
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation;type_check 50
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;synchronize 128
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;synchronize;acquire_lock 97
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock 192
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 30
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi;sys_futex 47
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;JavaCalls::call_helper;JavaCalls::call_virtual 194
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer 77
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke 141
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke;Reflection::invoke_method 52
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method 339
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method 29
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method;Compile::Compile 203
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;VMThread::loop;VMThread::evaluate_operation 76
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit 213
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit;GenCollectedHeap::do_collection 242
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;python;PyEval_EvalFrameEx;PyObject_Call 69
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;python;PyEval_EvalFrameEx;PyObject_Call;function_call 122
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx 93
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx;PyRun_StringFlags 33
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;python;gc_collect;collect 64
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;python;gc_collect;collect;move_unreachable 74
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;python;gc_collect;collect;move_unreachable;delete_garbage 70
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;python;gc_collect;collect;move_unreachable;delete_garbage;subtype_dealloc 122
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command 149
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command 229
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 147
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select 64
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec 49
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record 27
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next 49
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 71
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc 43
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level 142
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level;buf_page_get_gen 128
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command 136
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command 209
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 155
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert 182
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record 116
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row 21
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row 29
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 40
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql;btr_cur_optimistic_insert 116
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 213
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch 96
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low 98
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io 59
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 106
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func;io_submit 30
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;log_writer;log_write_up_to 150
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;log_writer;log_write_up_to;log_write_blocks 39
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func 196
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 44
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite;sys_pwrite64 77
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;srv_master_thread;srv_master_do_active_tasks 189
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close 104
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close;lock_rec_free_all_from_discard_page 62
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch 250
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;validate_records 230
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity 94
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 36
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match 77
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match;pcre_exec 16
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch 267
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;transform_data 148
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules 130
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 100
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations 25
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations;floating_point_calc 53
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch 192
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;clean_data 207
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates 96
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 39
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash 38
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash;crypto_hash_update 39
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;file_handler;read_csv_file 242
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;file_handler;read_csv_file;parse_csv_line 39
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields 67
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize 25
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy 77
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy;__memcpy_avx_unaligned 135
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;api_client;send_http_request 90
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;api_client;send_http_request;build_request_payload 154
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;api_client;send_http_request;build_request_payload;json_encode 175
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string 119
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string;string_append 85
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 243
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto 67
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto 226
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg 89
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg 84
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg 54
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 23
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb 66
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 59
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 91
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 38
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom 210
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom 169
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg 149
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg 21
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 117
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 52
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_rcv_established 62
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;net_rx_action;__napi_poll 113
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;net_rx_action;__napi_poll;e1000_clean 83
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq 101
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb 94
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core 125
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 14
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 51
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;dst_input 52
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;page_fault;do_page_fault 56
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;page_fault;do_page_fault;__do_page_fault 107
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 190
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault 116
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault 86
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 75
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable 27
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable;__alloc_pages_nodemask 120
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;allocate_buffer 188
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;allocate_buffer;malloc 123
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc 167
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 157
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc 150
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap 90
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap;sys_mmap 49
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;free_buffer 70
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;free_buffer;free 118
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;free_buffer;free;__libc_free 63
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 173
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap 72
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap;sys_munmap 121
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 97
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 63
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read 183
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read 64
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter 90
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter 52
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 112
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead;ondemand_readahead 83
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 276
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 128
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write 126
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write 85
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter 23
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter 34
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 51
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write;ext4_da_write_begin 29
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread 149
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;wait_for_task 140
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait 50
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 47
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex 95
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex;futex_wait_queue_me 48
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread 63
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;execute_task 200
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk 72
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 45
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation 41
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation;type_check 46
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;synchronize 134
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;synchronize;acquire_lock 152
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock 110
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 65
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi;sys_futex 33
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;JavaCalls::call_helper;JavaCalls::call_virtual 97
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer 27
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke 41
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke;Reflection::invoke_method 33
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method 104
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method 163
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method;Compile::Compile 87
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;VMThread::loop;VMThread::evaluate_operation 77
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit 198
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit;GenCollectedHeap::do_collection 210
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;python;PyEval_EvalFrameEx;PyObject_Call 151
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;python;PyEval_EvalFrameEx;PyObject_Call;function_call 50
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx 166
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx;PyRun_StringFlags 25
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;python;gc_collect;collect 195
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;python;gc_collect;collect;move_unreachable 45
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;python;gc_collect;collect;move_unreachable;delete_garbage 41
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;python;gc_collect;collect;move_unreachable;delete_garbage;subtype_dealloc 144
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command 117
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command 178
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 71
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select 42
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec 90
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record 36
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next 122
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 93
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc 80
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level 73
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level;buf_page_get_gen 114
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command 106
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command 194
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 110
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert 121
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record 61
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row 62
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row 50
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 20
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql;btr_cur_optimistic_insert 73
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 97
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch 68
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low 160
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io 21
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 98
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func;io_submit 35
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;log_writer;log_write_up_to 174
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;log_writer;log_write_up_to;log_write_blocks 71
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func 93
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 46
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite;sys_pwrite64 59
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;srv_master_thread;srv_master_do_active_tasks 88
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close 102
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close;lock_rec_free_all_from_discard_page 65
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch 159
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;validate_records 162
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity 78
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 104
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match 15
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match;pcre_exec 107
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch 228
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;transform_data 185
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules 90
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 57
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations 149
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations;floating_point_calc 93
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch 160
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;clean_data 214
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates 37
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 80
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash 39
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash;crypto_hash_update 14
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;file_handler;read_csv_file 189
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;file_handler;read_csv_file;parse_csv_line 229
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields 44
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize 122
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy 96
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy;__memcpy_avx_unaligned 70
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;api_client;send_http_request 228
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;api_client;send_http_request;build_request_payload 117
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;api_client;send_http_request;build_request_payload;json_encode 41
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string 97
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string;string_append 28
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 278
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto 112
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto 35
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg 105
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg 36
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg 37
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 73
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb 28
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 25
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 28
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 315
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom 253
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom 106
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg 41
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg 51
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 118
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 71
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_rcv_established 23
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;net_rx_action;__napi_poll 180
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;net_rx_action;__napi_poll;e1000_clean 147
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq 87
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb 148
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core 36
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 101
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 63
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;dst_input 65
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;page_fault;do_page_fault 114
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;page_fault;do_page_fault;__do_page_fault 51
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 63
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault 59
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault 70
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 93
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable 76
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable;__alloc_pages_nodemask 35
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;allocate_buffer 194
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;allocate_buffer;malloc 41
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc 150
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 106
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc 54
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap 68
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap;sys_mmap 116
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;free_buffer 170
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;free_buffer;free 139
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;free_buffer;free;__libc_free 197
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 36
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap 34
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap;sys_munmap 62
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 196
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 133
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read 36
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read 82
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter 72
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter 105
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 82
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead;ondemand_readahead 68
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 138
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 150
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write 163
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write 39
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter 52
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter 102
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 52
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write;ext4_da_write_begin 67
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread 201
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;wait_for_task 211
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait 45
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 99
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex 107
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex;futex_wait_queue_me 54
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread 87
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;execute_task 146
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk 61
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 185
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation 38
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation;type_check 57
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;synchronize 212
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;synchronize;acquire_lock 106
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock 178
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 87
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi;sys_futex 112
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;JavaCalls::call_helper;JavaCalls::call_virtual 172
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer 98
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke 62
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke;Reflection::invoke_method 49
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method 135
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method 232
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method;Compile::Compile 113
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;VMThread::loop;VMThread::evaluate_operation 170
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit 105
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit;GenCollectedHeap::do_collection 87
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;python;PyEval_EvalFrameEx;PyObject_Call 96
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;python;PyEval_EvalFrameEx;PyObject_Call;function_call 153
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx 115
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx;PyRun_StringFlags 55
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;python;gc_collect;collect 180
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;python;gc_collect;collect;move_unreachable 270
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;python;gc_collect;collect;move_unreachable;delete_garbage 66
all;kernel;irq;do_IRQ;handle_irq;handle_level_irq;python;gc_collect;collect;move_unreachable;delete_garbage;subtype_dealloc 47
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command 269
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command 236
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 139
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select 50
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec 25
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record 16
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next 37
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 25
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc 73
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level 49
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level;buf_page_get_gen 67
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command 139
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command 85
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 179
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert 37
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record 62
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row 84
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row 72
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 55
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql;btr_cur_optimistic_insert 42
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 210
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch 225
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low 111
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io 70
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 105
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func;io_submit 97
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;log_writer;log_write_up_to 179
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;log_writer;log_write_up_to;log_write_blocks 87
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func 78
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 37
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite;sys_pwrite64 75
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;srv_master_thread;srv_master_do_active_tasks 247
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close 181
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close;lock_rec_free_all_from_discard_page 62
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch 261
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;validate_records 131
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity 59
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 105
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match 72
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match;pcre_exec 110
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch 41
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;transform_data 187
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules 118
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 95
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations 20
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations;floating_point_calc 29
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch 153
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;clean_data 209
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates 90
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 101
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash 50
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash;crypto_hash_update 61
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;file_handler;read_csv_file 104
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;file_handler;read_csv_file;parse_csv_line 211
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields 45
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize 97
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy 50
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy;__memcpy_avx_unaligned 61
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;api_client;send_http_request 270
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;api_client;send_http_request;build_request_payload 47
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;api_client;send_http_request;build_request_payload;json_encode 48
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string 114
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string;string_append 107
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 43
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto 111
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto 50
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg 69
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg 47
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg 47
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 72
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb 102
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 53
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 114
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 151
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom 61
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom 86
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg 69
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg 91
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 54
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 68
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_rcv_established 38
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;net_rx_action;__napi_poll 68
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;net_rx_action;__napi_poll;e1000_clean 220
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq 231
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb 26
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core 89
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 107
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 49
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;dst_input 45
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;page_fault;do_page_fault 130
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;page_fault;do_page_fault;__do_page_fault 59
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 83
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault 37
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault 67
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 67
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable 100
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable;__alloc_pages_nodemask 68
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;allocate_buffer 146
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;allocate_buffer;malloc 122
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc 84
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 116
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc 31
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap 93
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap;sys_mmap 37
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;free_buffer 31
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;free_buffer;free 188
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;free_buffer;free;__libc_free 149
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 109
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap 22
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap;sys_munmap 14
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 247
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 162
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read 118
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read 56
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter 115
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter 85
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 35
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead;ondemand_readahead 54
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 109
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 231
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write 146
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write 132
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter 118
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter 50
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 70
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write;ext4_da_write_begin 111
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread 88
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;wait_for_task 244
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait 32
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 141
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex 43
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex;futex_wait_queue_me 96
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread 42
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;execute_task 97
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk 87
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 72
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation 98
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation;type_check 33
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;synchronize 69
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;synchronize;acquire_lock 205
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock 138
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 102
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi;sys_futex 29
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;JavaCalls::call_helper;JavaCalls::call_virtual 178
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer 117
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke 62
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke;Reflection::invoke_method 20
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method 101
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method 29
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method;Compile::Compile 143
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;VMThread::loop;VMThread::evaluate_operation 49
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit 92
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit;GenCollectedHeap::do_collection 222
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;python;PyEval_EvalFrameEx;PyObject_Call 227
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;python;PyEval_EvalFrameEx;PyObject_Call;function_call 163
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx 141
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx;PyRun_StringFlags 121
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;python;gc_collect;collect 52
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;python;gc_collect;collect;move_unreachable 63
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;python;gc_collect;collect;move_unreachable;delete_garbage 110
all;kernel;softirq;__do_softirq;net_rx_action;__napi_poll;python;gc_collect;collect;move_unreachable;delete_garbage;subtype_dealloc 201
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command 30
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command 189
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 208
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select 68
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec 60
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record 111
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next 24
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 93
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc 29
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level 64
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level;buf_page_get_gen 85
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command 87
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command 297
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 95
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert 109
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record 47
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row 39
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row 29
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 75
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql;btr_cur_optimistic_insert 52
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 122
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch 91
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low 93
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io 26
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 95
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func;io_submit 81
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;log_writer;log_write_up_to 246
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;log_writer;log_write_up_to;log_write_blocks 171
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func 196
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 49
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite;sys_pwrite64 73
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;srv_master_thread;srv_master_do_active_tasks 187
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close 231
all;mysqld;main;mysqld_main;handle_connections_sockets;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close;lock_rec_free_all_from_discard_page 188
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch 62
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;validate_records 96
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity 110
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 175
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match 41
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match;pcre_exec 80
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch 46
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;transform_data 111
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules 191
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 20
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations 47
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations;floating_point_calc 85
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch 164
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;clean_data 86
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates 145
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 94
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash 70
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash;crypto_hash_update 27
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;file_handler;read_csv_file 240
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;file_handler;read_csv_file;parse_csv_line 152
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields 175
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize 28
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy 138
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy;__memcpy_avx_unaligned 41
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;api_client;send_http_request 140
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;api_client;send_http_request;build_request_payload 232
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;api_client;send_http_request;build_request_payload;json_encode 119
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string 155
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string;string_append 40
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 168
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto 85
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto 157
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg 40
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg 29
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg 60
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 20
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb 121
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 90
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 11
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 101
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom 265
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom 115
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg 87
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg 35
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 23
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 72
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_rcv_established 17
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;net_rx_action;__napi_poll 175
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;net_rx_action;__napi_poll;e1000_clean 173
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq 118
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb 51
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core 77
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 50
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 57
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;dst_input 107
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;page_fault;do_page_fault 113
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;page_fault;do_page_fault;__do_page_fault 59
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 148
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault 92
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault 82
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 51
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable 104
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable;__alloc_pages_nodemask 44
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;allocate_buffer 170
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;allocate_buffer;malloc 128
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc 121
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 35
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc 27
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap 89
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap;sys_mmap 108
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;free_buffer 171
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;free_buffer;free 99
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;free_buffer;free;__libc_free 116
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 137
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap 41
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap;sys_munmap 88
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 152
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 135
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read 118
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read 118
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter 32
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter 57
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 69
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead;ondemand_readahead 99
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 255
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 24
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write 44
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write 60
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter 93
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter 110
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 144
all;mysqld;main;mysqld_main;handle_connections_sockets;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write;ext4_da_write_begin 100
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread 321
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;wait_for_task 106
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait 117
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 40
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex 88
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex;futex_wait_queue_me 80
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread 41
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;execute_task 162
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk 190
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 142
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation 41
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation;type_check 72
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;synchronize 132
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;synchronize;acquire_lock 109
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock 155
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 21
all;mysqld;main;mysqld_main;handle_connections_sockets;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi;sys_futex 63
all;mysqld;main;mysqld_main;handle_connections_sockets;java;JavaCalls::call_helper;JavaCalls::call_virtual 150
all;mysqld;main;mysqld_main;handle_connections_sockets;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer 198
all;mysqld;main;mysqld_main;handle_connections_sockets;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke 53
all;mysqld;main;mysqld_main;handle_connections_sockets;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke;Reflection::invoke_method 94
all;mysqld;main;mysqld_main;handle_connections_sockets;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method 142
all;mysqld;main;mysqld_main;handle_connections_sockets;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method 67
all;mysqld;main;mysqld_main;handle_connections_sockets;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method;Compile::Compile 225
all;mysqld;main;mysqld_main;handle_connections_sockets;java;VMThread::loop;VMThread::evaluate_operation 189
all;mysqld;main;mysqld_main;handle_connections_sockets;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit 135
all;mysqld;main;mysqld_main;handle_connections_sockets;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit;GenCollectedHeap::do_collection 62
all;mysqld;main;mysqld_main;handle_connections_sockets;python;PyEval_EvalFrameEx;PyObject_Call 187
all;mysqld;main;mysqld_main;handle_connections_sockets;python;PyEval_EvalFrameEx;PyObject_Call;function_call 170
all;mysqld;main;mysqld_main;handle_connections_sockets;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx 134
all;mysqld;main;mysqld_main;handle_connections_sockets;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx;PyRun_StringFlags 110
all;mysqld;main;mysqld_main;handle_connections_sockets;python;gc_collect;collect 32
all;mysqld;main;mysqld_main;handle_connections_sockets;python;gc_collect;collect;move_unreachable 110
all;mysqld;main;mysqld_main;handle_connections_sockets;python;gc_collect;collect;move_unreachable;delete_garbage 177
all;mysqld;main;mysqld_main;handle_connections_sockets;python;gc_collect;collect;move_unreachable;delete_garbage;subtype_dealloc 53
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command 196
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command 188
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 119
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select 77
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec 24
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record 121
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next 78
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 71
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc 25
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level 21
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level;buf_page_get_gen 104
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command 334
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command 110
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 123
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert 35
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record 145
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row 37
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row 47
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 66
all;blinkit_app;main;app_main;event_loop;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql;btr_cur_optimistic_insert 85
all;blinkit_app;main;app_main;event_loop;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 216
all;blinkit_app;main;app_main;event_loop;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch 243
all;blinkit_app;main;app_main;event_loop;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low 63
all;blinkit_app;main;app_main;event_loop;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io 109
all;blinkit_app;main;app_main;event_loop;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 88
all;blinkit_app;main;app_main;event_loop;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func;io_submit 118
all;blinkit_app;main;app_main;event_loop;mysqld;log_writer;log_write_up_to 140
all;blinkit_app;main;app_main;event_loop;mysqld;log_writer;log_write_up_to;log_write_blocks 39
all;blinkit_app;main;app_main;event_loop;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func 201
all;blinkit_app;main;app_main;event_loop;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 34
all;blinkit_app;main;app_main;event_loop;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite;sys_pwrite64 152
all;blinkit_app;main;app_main;event_loop;mysqld;srv_master_thread;srv_master_do_active_tasks 108
all;blinkit_app;main;app_main;event_loop;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close 36
all;blinkit_app;main;app_main;event_loop;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close;lock_rec_free_all_from_discard_page 137
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch 49
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;validate_records 159
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity 37
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 87
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match 63
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match;pcre_exec 58
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch 52
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;transform_data 266
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules 146
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 86
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations 32
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations;floating_point_calc 59
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch 69
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;clean_data 117
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates 123
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 175
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash 34
all;blinkit_app;main;app_main;event_loop;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash;crypto_hash_update 18
all;blinkit_app;main;app_main;event_loop;blinkit_app;file_handler;read_csv_file 249
all;blinkit_app;main;app_main;event_loop;blinkit_app;file_handler;read_csv_file;parse_csv_line 206
all;blinkit_app;main;app_main;event_loop;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields 68
all;blinkit_app;main;app_main;event_loop;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize 112
all;blinkit_app;main;app_main;event_loop;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy 95
all;blinkit_app;main;app_main;event_loop;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy;__memcpy_avx_unaligned 53
all;blinkit_app;main;app_main;event_loop;blinkit_app;api_client;send_http_request 341
all;blinkit_app;main;app_main;event_loop;blinkit_app;api_client;send_http_request;build_request_payload 161
all;blinkit_app;main;app_main;event_loop;blinkit_app;api_client;send_http_request;build_request_payload;json_encode 189
all;blinkit_app;main;app_main;event_loop;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string 117
all;blinkit_app;main;app_main;event_loop;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string;string_append 129
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 103
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto 37
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto 118
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg 55
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg 116
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg 75
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 111
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb 68
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 98
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 103
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 186
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom 107
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom 69
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg 108
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg 61
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 84
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 19
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_rcv_established 65
all;blinkit_app;main;app_main;event_loop;kernel;net_rx_action;__napi_poll 149
all;blinkit_app;main;app_main;event_loop;kernel;net_rx_action;__napi_poll;e1000_clean 238
all;blinkit_app;main;app_main;event_loop;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq 44
all;blinkit_app;main;app_main;event_loop;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb 62
all;blinkit_app;main;app_main;event_loop;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core 90
all;blinkit_app;main;app_main;event_loop;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 86
all;blinkit_app;main;app_main;event_loop;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 67
all;blinkit_app;main;app_main;event_loop;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;dst_input 105
all;blinkit_app;main;app_main;event_loop;kernel;page_fault;do_page_fault 170
all;blinkit_app;main;app_main;event_loop;kernel;page_fault;do_page_fault;__do_page_fault 106
all;blinkit_app;main;app_main;event_loop;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 134
all;blinkit_app;main;app_main;event_loop;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault 61
all;blinkit_app;main;app_main;event_loop;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault 63
all;blinkit_app;main;app_main;event_loop;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 44
all;blinkit_app;main;app_main;event_loop;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable 79
all;blinkit_app;main;app_main;event_loop;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable;__alloc_pages_nodemask 25
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;allocate_buffer 129
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;allocate_buffer;malloc 94
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc 93
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 115
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc 73
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap 26
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap;sys_mmap 61
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;free_buffer 34
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;free_buffer;free 166
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;free_buffer;free;__libc_free 103
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 55
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap 54
all;blinkit_app;main;app_main;event_loop;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap;sys_munmap 83
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 70
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 85
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read 90
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read 68
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter 45
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter 117
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 51
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead;ondemand_readahead 88
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 186
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 174
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write 41
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write 168
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter 92
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter 102
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 25
all;blinkit_app;main;app_main;event_loop;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write;ext4_da_write_begin 33
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread 192
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;wait_for_task 233
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait 133
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 161
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex 39
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex;futex_wait_queue_me 105
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread 80
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;execute_task 193
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk 159
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 176
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation 126
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation;type_check 18
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;synchronize 193
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;synchronize;acquire_lock 138
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock 48
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 169
all;blinkit_app;main;app_main;event_loop;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi;sys_futex 71
all;blinkit_app;main;app_main;event_loop;java;JavaCalls::call_helper;JavaCalls::call_virtual 145
all;blinkit_app;main;app_main;event_loop;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer 109
all;blinkit_app;main;app_main;event_loop;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke 125
all;blinkit_app;main;app_main;event_loop;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke;Reflection::invoke_method 42
all;blinkit_app;main;app_main;event_loop;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method 108
all;blinkit_app;main;app_main;event_loop;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method 210
all;blinkit_app;main;app_main;event_loop;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method;Compile::Compile 157
all;blinkit_app;main;app_main;event_loop;java;VMThread::loop;VMThread::evaluate_operation 57
all;blinkit_app;main;app_main;event_loop;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit 98
all;blinkit_app;main;app_main;event_loop;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit;GenCollectedHeap::do_collection 97
all;blinkit_app;main;app_main;event_loop;python;PyEval_EvalFrameEx;PyObject_Call 181
all;blinkit_app;main;app_main;event_loop;python;PyEval_EvalFrameEx;PyObject_Call;function_call 216
all;blinkit_app;main;app_main;event_loop;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx 53
all;blinkit_app;main;app_main;event_loop;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx;PyRun_StringFlags 64
all;blinkit_app;main;app_main;event_loop;python;gc_collect;collect 47
all;blinkit_app;main;app_main;event_loop;python;gc_collect;collect;move_unreachable 150
all;blinkit_app;main;app_main;event_loop;python;gc_collect;collect;move_unreachable;delete_garbage 81
all;blinkit_app;main;app_main;event_loop;python;gc_collect;collect;move_unreachable;delete_garbage;subtype_dealloc 55
all;java;main;JavaMain;run;mysqld;handle_connection;do_command 91
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command 221
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 31
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select 103
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec 127
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record 38
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next 121
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 28
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc 82
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level 80
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level;buf_page_get_gen 51
all;java;main;JavaMain;run;mysqld;handle_connection;do_command 106
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command 144
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 105
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert 101
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record 119
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row 31
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row 31
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 103
all;java;main;JavaMain;run;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql;btr_cur_optimistic_insert 52
all;java;main;JavaMain;run;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 213
all;java;main;JavaMain;run;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch 30
all;java;main;JavaMain;run;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low 135
all;java;main;JavaMain;run;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io 28
all;java;main;JavaMain;run;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 78
all;java;main;JavaMain;run;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func;io_submit 27
all;java;main;JavaMain;run;mysqld;log_writer;log_write_up_to 185
all;java;main;JavaMain;run;mysqld;log_writer;log_write_up_to;log_write_blocks 114
all;java;main;JavaMain;run;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func 181
all;java;main;JavaMain;run;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 27
all;java;main;JavaMain;run;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite;sys_pwrite64 69
all;java;main;JavaMain;run;mysqld;srv_master_thread;srv_master_do_active_tasks 190
all;java;main;JavaMain;run;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close 122
all;java;main;JavaMain;run;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close;lock_rec_free_all_from_discard_page 92
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch 108
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;validate_records 264
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity 39
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 151
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match 31
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match;pcre_exec 39
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch 64
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;transform_data 72
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules 54
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 142
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations 141
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations;floating_point_calc 20
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch 287
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;clean_data 83
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates 73
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 32
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash 21
all;java;main;JavaMain;run;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash;crypto_hash_update 23
all;java;main;JavaMain;run;blinkit_app;file_handler;read_csv_file 292
all;java;main;JavaMain;run;blinkit_app;file_handler;read_csv_file;parse_csv_line 74
all;java;main;JavaMain;run;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields 77
all;java;main;JavaMain;run;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize 184
all;java;main;JavaMain;run;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy 33
all;java;main;JavaMain;run;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy;__memcpy_avx_unaligned 51
all;java;main;JavaMain;run;blinkit_app;api_client;send_http_request 307
all;java;main;JavaMain;run;blinkit_app;api_client;send_http_request;build_request_payload 244
all;java;main;JavaMain;run;blinkit_app;api_client;send_http_request;build_request_payload;json_encode 35
all;java;main;JavaMain;run;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string 124
all;java;main;JavaMain;run;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string;string_append 82
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 131
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto 221
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto 156
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg 69
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg 60
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg 24
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 109
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb 32
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 33
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 129
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 72
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom 66
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom 81
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg 91
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg 109
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 43
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 102
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_rcv_established 94
all;java;main;JavaMain;run;kernel;net_rx_action;__napi_poll 171
all;java;main;JavaMain;run;kernel;net_rx_action;__napi_poll;e1000_clean 174
all;java;main;JavaMain;run;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq 73
all;java;main;JavaMain;run;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb 41
all;java;main;JavaMain;run;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core 52
all;java;main;JavaMain;run;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 57
all;java;main;JavaMain;run;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 23
all;java;main;JavaMain;run;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;dst_input 78
all;java;main;JavaMain;run;kernel;page_fault;do_page_fault 141
all;java;main;JavaMain;run;kernel;page_fault;do_page_fault;__do_page_fault 302
all;java;main;JavaMain;run;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 112
all;java;main;JavaMain;run;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault 139
all;java;main;JavaMain;run;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault 46
all;java;main;JavaMain;run;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 32
all;java;main;JavaMain;run;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable 15
all;java;main;JavaMain;run;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable;__alloc_pages_nodemask 86
all;java;main;JavaMain;run;blinkit_app;memory_manager;allocate_buffer 212
all;java;main;JavaMain;run;blinkit_app;memory_manager;allocate_buffer;malloc 146
all;java;main;JavaMain;run;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc 65
all;java;main;JavaMain;run;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 81
all;java;main;JavaMain;run;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc 72
all;java;main;JavaMain;run;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap 39
all;java;main;JavaMain;run;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap;sys_mmap 86
all;java;main;JavaMain;run;blinkit_app;memory_manager;free_buffer 243
all;java;main;JavaMain;run;blinkit_app;memory_manager;free_buffer;free 32
all;java;main;JavaMain;run;blinkit_app;memory_manager;free_buffer;free;__libc_free 61
all;java;main;JavaMain;run;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 100
all;java;main;JavaMain;run;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap 76
all;java;main;JavaMain;run;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap;sys_munmap 28
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 273
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 93
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read 107
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read 114
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter 67
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter 17
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 106
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead;ondemand_readahead 120
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 213
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 150
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write 57
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write 133
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter 49
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter 47
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 82
all;java;main;JavaMain;run;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write;ext4_da_write_begin 106
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread 216
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;wait_for_task 127
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait 154
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 23
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex 26
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex;futex_wait_queue_me 34
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread 177
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;execute_task 228
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk 53
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 107
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation 39
all;java;main;JavaMain;run;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation;type_check 60
all;java;main;JavaMain;run;blinkit_app;thread_pool;synchronize 309
all;java;main;JavaMain;run;blinkit_app;thread_pool;synchronize;acquire_lock 104
all;java;main;JavaMain;run;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock 167
all;java;main;JavaMain;run;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 93
all;java;main;JavaMain;run;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi;sys_futex 23
all;java;main;JavaMain;run;java;JavaCalls::call_helper;JavaCalls::call_virtual 183
all;java;main;JavaMain;run;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer 153
all;java;main;JavaMain;run;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke 49
all;java;main;JavaMain;run;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke;Reflection::invoke_method 132
all;java;main;JavaMain;run;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method 107
all;java;main;JavaMain;run;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method 81
all;java;main;JavaMain;run;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method;Compile::Compile 31
all;java;main;JavaMain;run;java;VMThread::loop;VMThread::evaluate_operation 167
all;java;main;JavaMain;run;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit 152
all;java;main;JavaMain;run;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit;GenCollectedHeap::do_collection 144
all;java;main;JavaMain;run;python;PyEval_EvalFrameEx;PyObject_Call 282
all;java;main;JavaMain;run;python;PyEval_EvalFrameEx;PyObject_Call;function_call 219
all;java;main;JavaMain;run;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx 87
all;java;main;JavaMain;run;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx;PyRun_StringFlags 24
all;java;main;JavaMain;run;python;gc_collect;collect 84
all;java;main;JavaMain;run;python;gc_collect;collect;move_unreachable 251
all;java;main;JavaMain;run;python;gc_collect;collect;move_unreachable;delete_garbage 154
all;java;main;JavaMain;run;python;gc_collect;collect;move_unreachable;delete_garbage;subtype_dealloc 34
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command 112
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command 136
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 239
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select 84
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec 61
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record 95
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next 24
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 90
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc 47
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level 117
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next;row_search_mvcc;btr_cur_search_to_nth_level;buf_page_get_gen 63
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command 177
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command 48
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 200
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert 175
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record 93
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row 107
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row 24
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 129
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql;btr_cur_optimistic_insert 125
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 167
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch 77
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low 43
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io 34
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 27
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func;io_submit 72
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;log_writer;log_write_up_to 134
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;log_writer;log_write_up_to;log_write_blocks 76
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func 139
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 25
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite;sys_pwrite64 17
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;srv_master_thread;srv_master_do_active_tasks 168
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close 285
all;python;main;Py_Main;PyRun_SimpleFileExFlags;mysqld;srv_master_thread;srv_master_do_active_tasks;lock_sys_close;lock_rec_free_all_from_discard_page 155
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch 196
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;validate_records 123
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity 148
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 36
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match 36
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format;regex_match;pcre_exec 93
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch 93
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;transform_data 160
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules 66
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 90
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations 116
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields;math_operations;floating_point_calc 15
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch 232
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;clean_data 156
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates 119
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 106
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash 25
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key;sha256_hash;crypto_hash_update 23
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;file_handler;read_csv_file 157
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;file_handler;read_csv_file;parse_csv_line 44
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields 53
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize 105
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy 31
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;string_tokenize;memcpy;__memcpy_avx_unaligned 92
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;api_client;send_http_request 259
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;api_client;send_http_request;build_request_payload 170
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;api_client;send_http_request;build_request_payload;json_encode 218
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string 123
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;json_object_to_string;string_append 62
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 36
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto 74
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto 78
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg 83
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg 98
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg 99
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 64
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb 41
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit 22
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit;tcp_transmit_skb;ip_queue_xmit;ip_output 34
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 205
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom 43
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom 169
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg 145
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg 68
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg 116
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 97
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf;tcp_rcv_established 78
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;net_rx_action;__napi_poll 140
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;net_rx_action;__napi_poll;e1000_clean 27
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq 57
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb 48
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core 50
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 22
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish 76
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv;ip_rcv_finish;dst_input 76
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;page_fault;do_page_fault 111
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;page_fault;do_page_fault;__do_page_fault 105
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 162
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault 39
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault 81
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 63
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable 20
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page;alloc_zeroed_user_highpage_movable;__alloc_pages_nodemask 29
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;allocate_buffer 84
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;allocate_buffer;malloc 87
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc 155
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 28
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc 96
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap 124
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc;sysmalloc;mmap;sys_mmap 104
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;free_buffer 214
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;free_buffer;free 183
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;free_buffer;free;__libc_free 29
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 59
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap 115
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free;munmap;sys_munmap 116
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 259
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 93
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read 44
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read 64
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter 40
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter 23
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 21
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead;ondemand_readahead 103
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64 307
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 24
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write 74
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write 119
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter 25
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter 16
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 129
all;python;main;Py_Main;PyRun_SimpleFileExFlags;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write;ext4_da_write_begin 72
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread 231
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;wait_for_task 141
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait 20
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 114
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex 40
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait;sys_futex;futex_wait_queue_me 112
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread 182
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;execute_task 149
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk 135
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 19
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation 67
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk;field_validation;type_check 34
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;synchronize 219
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;synchronize;acquire_lock 123
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock 134
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 117
all;python;main;Py_Main;PyRun_SimpleFileExFlags;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi;sys_futex 56
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;JavaCalls::call_helper;JavaCalls::call_virtual 174
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer 41
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke 30
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;JavaCalls::call_helper;JavaCalls::call_virtual;instanceKlass::call_class_initializer;Method::invoke;Reflection::invoke_method 26
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method 300
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method 188
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;CompileBroker::compiler_thread_loop;CompileBroker::invoke_compiler_on_method;C2Compiler::compile_method;Compile::Compile 82
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;VMThread::loop;VMThread::evaluate_operation 52
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit 80
all;python;main;Py_Main;PyRun_SimpleFileExFlags;java;VMThread::loop;VMThread::evaluate_operation;VM_GenCollectForAllocation::doit;GenCollectedHeap::do_collection 182
all;python;main;Py_Main;PyRun_SimpleFileExFlags;python;PyEval_EvalFrameEx;PyObject_Call 288
all;python;main;Py_Main;PyRun_SimpleFileExFlags;python;PyEval_EvalFrameEx;PyObject_Call;function_call 220
all;python;main;Py_Main;PyRun_SimpleFileExFlags;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx 138
all;python;main;Py_Main;PyRun_SimpleFileExFlags;python;PyEval_EvalFrameEx;PyObject_Call;function_call;PyEval_EvalCodeEx;PyRun_StringFlags 46
all;python;main;Py_Main;PyRun_SimpleFileExFlags;python;gc_collect;collect 181
all;python;main;Py_Main;PyRun_SimpleFileExFlags;python;gc_collect;collect;move_unreachable 68
all;python;main;Py_Main;PyRun_SimpleFileExFlags;python;gc_collect;collect;move_unreachable;delete_garbage 42
all;python;main;Py_Main;PyRun_SimpleFileExFlags;python;gc_collect;collect;move_unreachable;delete_garbage;subtype_dealloc 147
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read 850
all;kernel;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write 720
all;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command 650
all;blinkit_app;data_processor;process_batch;validate_records 580
all;kernel;page_fault;do_page_fault;__do_page_fault;handle_mm_fault 520
all;blinkit_app;data_processor;process_batch;transform_data 480
all;kernel;softirq;__do_softirq;net_rx_action 420
all;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker 380
all;blinkit_app;file_handler;read_csv_file;parse_csv_line 350
all;kernel;irq;do_IRQ;handle_irq 320
