main;DataStagingApp;DatabaseConnector;executeQuery;mysql_query 150
main;DataStagingApp;DatabaseConnector;executeQuery;mysql_fetch_row 120
main;DataStagingApp;DatabaseConnector;connection_pool;get_connection 80
main;DataStagingApp;DatabaseConnector;connection_pool;create_connection 45
main;DataStagingApp;DataProcessor;processRecords;validateData 200
main;DataStagingApp;DataProcessor;processRecords;transformData 180
main;DataStagingApp;DataProcessor;processRecords;cleanData 160
main;DataStagingApp;DataProcessor;batchProcessor;processBatch 140
main;DataStagingApp;FileHandler;readFile;fread 100
main;DataStagingApp;FileHandler;writeFile;fwrite 90
main;DataStagingApp;FileHandler;parseCSV;csv_parser 85
main;DataStagingApp;FileHandler;parseJSON;json_parser 75
main;DataStagingApp;APIClient;sendRequest;http_post 110
main;DataStagingApp;APIClient;sendRequest;http_get 95
main;DataStagingApp;APIClient;handleResponse;json_decode 70
main;DataStagingApp;MemoryManager;allocate;malloc 60
main;DataStagingApp;MemoryManager;deallocate;free 40
main;DataStagingApp;MemoryManager;garbage_collect 35
main;DataStagingApp;Logger;writeLog;file_write 50
main;DataStagingApp;Metrics;recordMetric;counter_increment 30
main;DataStagingApp;Metrics;recordMetric;histogram_update 25
main;DataStagingApp;ConfigManager;loadConfig;yaml_parse 40
main;DataStagingApp;ConfigManager;validateConfig 20
main;DataStagingApp;initialize;setup_logging 15
main;DataStagingApp;initialize;setup_database 25
main;DataStagingApp;ErrorHandler;handleException;log_error 35
main;DataStagingApp;ErrorHandler;handleException;send_alert 20
main;DataStagingApp;ThreadPool;worker_thread;process_task 130
main;DataStagingApp;ThreadPool;worker_thread;wait_for_task 45
main;DataStagingApp;ThreadPool;synchronize;mutex_lock 25
main;DataStagingApp;CacheManager;get;redis_get 65
main;DataStagingApp;CacheManager;set;redis_set 55
main;DataStagingApp;CacheManager;invalidate;redis_del 30
