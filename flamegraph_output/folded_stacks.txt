main;start_kernel;rest_init;kernel_init;kernel_init_freeable;do_basic_setup;driver_init;platform_driver_register 45
main;start_kernel;rest_init;cpu_startup_entry;cpu_idle_loop;default_idle_call;arch_cpu_idle 320
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;__vfs_read;generic_file_read_iter 180
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;__vfs_write;generic_perform_write 165
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_openat;do_sys_openat2;do_filp_open;path_openat;link_path_walk 95
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_close;__close_fd;filp_close;fput;__fput 75
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_mmap;ksys_mmap_pgoff;vm_mmap_pgoff;do_mmap;mmap_region 140
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_munmap;__vm_munmap;__do_munmap;unmap_region 85
main;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_anonymous_page 220
main;page_fault;do_page_fault;__do_page_fault;handle_mm_fault;__handle_mm_fault;handle_pte_fault;do_wp_page 125
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_sendto;__sys_sendto;sock_sendmsg;inet_sendmsg;tcp_sendmsg;tcp_write_xmit 190
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_recvfrom;__sys_recvfrom;sock_recvmsg;inet_recvmsg;tcp_recvmsg;tcp_cleanup_rbuf 155
main;net_rx_action;__napi_poll;e1000_clean;e1000_clean_rx_irq;netif_receive_skb;__netif_receive_skb_core;ip_rcv 110
main;net_tx_action;qdisc_run;__qdisc_run;qdisc_restart;dequeue_skb;dev_hard_start_xmit;e1000_xmit_frame 98
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_read;ksys_read;vfs_read;ext4_file_read_iter;generic_file_read_iter;page_cache_sync_readahead 175
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_write;ksys_write;vfs_write;ext4_file_write_iter;__generic_file_write_iter;generic_perform_write 160
main;entry_SYSCALL_64_after_hwframe;do_syscall_64;__x64_sys_fsync;do_fsync;vfs_fsync;ext4_sync_file;jbd2_complete_transaction 88
main;kworker/u16:1;worker_thread;process_one_work;wb_workfn;wb_do_writeback;__writeback_inodes_wb;writeback_sb_inodes;__writeback_single_inode;do_writepages;ext4_writepages 145
main;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_select;JOIN::exec;evaluate_join_record;handler::ha_rnd_next;ha_innobase::rnd_next 280
main;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_insert;write_record;handler::ha_write_row;ha_innobase::write_row;row_insert_for_mysql 245
main;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_update;mysql_update_single_table;handler::ha_update_row;ha_innobase::update_row 210
main;mysqld;handle_connection;do_command;dispatch_command;mysql_execute_command;mysql_delete;mysql_delete_single_table;handler::ha_delete_row;ha_innobase::delete_row 185
main;mysqld;buf_flush_page_cleaner_coordinator;buf_flush_page_cleaner_worker;buf_flush_do_batch;buf_flush_write_block_low;fil_io;os_aio_func 195
main;mysqld;log_writer;log_write_up_to;log_write_blocks;os_file_write_func;pwrite 165
main;mysqld;srv_master_thread;srv_master_do_active_tasks;srv_master_evict_from_table_cache;dict_table_close 75
main;blinkit_app;data_processor;process_batch;validate_records;check_data_integrity;validate_field_format 320
main;blinkit_app;data_processor;process_batch;transform_data;apply_business_rules;calculate_derived_fields 285
main;blinkit_app;data_processor;process_batch;clean_data;remove_duplicates;hash_record_key 250
main;blinkit_app;data_processor;process_batch;enrich_data;lookup_reference_data;query_cache 220
main;blinkit_app;file_handler;read_csv_file;parse_csv_line;split_fields;trim_whitespace 195
main;blinkit_app;file_handler;write_output_file;format_record;serialize_json;escape_special_chars 170
main;blinkit_app;api_client;send_http_request;build_request_payload;json_encode;allocate_buffer 155
main;blinkit_app;api_client;process_response;parse_json_response;json_decode;parse_object 140
main;blinkit_app;cache_manager;redis_get;send_command;socket_write;tcp_send 125
main;blinkit_app;cache_manager;redis_set;send_command;socket_read;tcp_recv 110
main;blinkit_app;logger;write_log_entry;format_timestamp;strftime;localtime 95
main;blinkit_app;metrics_collector;record_metric;update_histogram;calculate_percentile 80
main;blinkit_app;error_handler;handle_exception;format_error_message;stack_trace_capture 65
main;blinkit_app;config_loader;parse_yaml_config;yaml_parse_document;yaml_parse_node 55
main;blinkit_app;thread_pool;worker_thread;wait_for_task;pthread_cond_wait;futex_wait 180
main;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;validate_chunk 165
main;blinkit_app;thread_pool;worker_thread;execute_task;process_data_chunk;transform_chunk 150
main;blinkit_app;thread_pool;synchronize;acquire_lock;pthread_mutex_lock;futex_lock_pi 85
main;blinkit_app;thread_pool;synchronize;release_lock;pthread_mutex_unlock;futex_unlock_pi 45
main;blinkit_app;memory_manager;allocate_buffer;malloc;__libc_malloc;_int_malloc 135
main;blinkit_app;memory_manager;reallocate_buffer;realloc;__libc_realloc;_int_realloc 95
main;blinkit_app;memory_manager;free_buffer;free;__libc_free;_int_free 75
main;blinkit_app;memory_manager;garbage_collect;mark_and_sweep;mark_objects 65
main;blinkit_app;io_manager;async_read;epoll_wait;sys_epoll_wait;ep_poll 145
main;blinkit_app;io_manager;async_write;epoll_ctl;sys_epoll_ctl;ep_insert 95
main;blinkit_app;io_manager;flush_buffers;fsync;sys_fsync;ext4_sync_file 85
