Blinkit Data Staging Performance Analysis Summary
==================================================

Top Functions by Sample Count:
------------------------------
 1. arch_cpu_idle                     320 samples
 2. validate_field_format             320 samples
 3. calculate_derived_fields          285 samples
 4. ha_innobase::rnd_next             280 samples
 5. hash_record_key                   250 samples
 6. row_insert_for_mysql              245 samples
 7. do_anonymous_page                 220 samples
 8. query_cache                       220 samples
 9. ha_innobase::update_row           210 samples
10. os_aio_func                       195 samples

Total samples analyzed: 7511
Unique stack traces: 51
