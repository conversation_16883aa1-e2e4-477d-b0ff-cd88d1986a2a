[{"timestamp": 1753622969, "received_at": 1753622970, "user_agent": "&source=android_market&version=15&device_manufacturer=samsung&device_brand=samsung&device_model=SM-A556E&app_version=17.37.2&app_type=blinkit_android&sdk_version=35&network_type=WIFI&user_lang=en&kmm_identifier=null", "verification": 1, "source": "android", "device_id": "af9ef236-4016-48ee-afba-3b1715b79dfe", "user_id": "62234241", "session_id": "c23abed2-4130-45ee-b7d0-8f37ade9cc501753622900", "location": "787", "number_of_events": 0, "ip": "**************", "batch_count": 0, "batch_offset": 0, "location_info": {"user_defined_latitude": 18.563846, "user_defined_longitude": 73.960235, "current_longitude": null, "current_latitude": null}, "app_info": {"device_performance": "DEVICE_PERFORMANCE_UNSPECIFIED", "theme": "APP_THEME_UNSPECIFIED", "system_theme": "SYSTEM_THEME_DARK", "app_appearance": "APPEARANCE_LIGHT"}, "namespace": ""}, {"key": "impression_events", "value": {"event_name": "Product List Visit", "timezone": "Asia/Kolkata", "install_source": "#-NA", "install_campaign": "#-NA", "install_medium": "#-NA", "install_referrer": "#-NA", "chain_id": 1383, "lifetime_orders": 209, "latitude": 18.563846, "merchant_id": 35902, "monthly_orders": 11, "city_name": "Pune", "user_type": "ACTIVE", "segment_type": ["school_cohort", "school_cohort_low", "stationary_buyers", "plant_users", "femcare_bathing_user", "toilet_roll_user", "detergent_potenial_users", "harpic_user", "platform_devotee", "gillette_user", "fnv_non_watermelon_user", "health_fitness", "fnv_non_muskmelon_user", "fnv_non_mango_user", "ads_female", "plum_potential_user", "chocolate_munchies_biscuit_premium", "ads_toddler_and_infant", "ads_diy_grocery_buy", "ads_diy_personal_care_buy", "ads_plant_lovers", "surface_cleaner_user", "ads_diy_health_buy", "ads_diy_sexual_wellness_buy", "razor_premium", "amex_defaulter", "fem_care_user", "credit_card_user_flag2", "diswashing_gel_sampling", "beauty_customers", "grocery_user", "party_esttential_potential", "top_twenty_purchaser", "sexual_wellness_user", "sports_fitness", "personal_care_user", "premium_customer", "airtel_flyers_users", "brad_pav_users", "hpstationary_auto", "perforalemon_auto", "deconstructsuns_auto", "personalisation_toddler_infant_cohort_high", "babycareuser_cdp", "female_users", "personalisation_vrat_cohort_low", "ORD_FriendshipBands", "bpc_navratna", "oralcareperfora_cdp", "devotees", "bpcuser_minus_deconstruct", "personalisation_kids_cohort_high", "ads_fnv", "skincare_gulabrigel", "ritebite_users_L6", "dishwashingusers_giffy", "ASBL_Flyer", "segment_z_blinkit_new_store_offer", "segment_z_blinkit_coke_offer_v3", "segment_z_blinkit_coke_offer_v4"], "user_experiment_buckets": ["RANDOM#bucket1", "INCREASING_ROLLOUT#bucket2", "DECREASING_ROLLOUT#bucket2"], "city_id": 787, "longitude": 73.960235, "segment_enabled_features": ["slp-weighted-ranking-equation:0", "show-sort-slp", "ads-service-flow-rollout:baelish", "back-in-stock-grid:enabled", "cbf-similarity-enabled", "cbf-dismiss-button-enabled", "see-all-on-slp", "see-all-recipe-on-plp", "ads-collection-inventory-check-migration", "semantic-search", "slp-3-cards", "autosuggest-keyword-ranking:0", "paas-cart-merge-enabled", "plp-tobacco-consent:disable", "top-brands:3", "pin-lq-products", "pecos", "generated-tags-autosuggest", "continue-browsing-for:enable", "autocomplete-prefix-match-variation:default", "mandatory-autosuggest", "feed-recipe-v2-rollout:enable", "oos-widget-on-slp", "search-show-duplicate-autosuggest", "enable-usecase-container", "search-autosuggest-tap-vs-type:1", "enable-empty-search-v2", "low-converting-keywords-boosting", "city-sales-ranking-v2", "ad-density-variation:0", "product-suggestion", "generated-tag-variation:1", "search-similarity-model", "keyterm-similarity-variation:1", "show-user-query-keyword-autosuggestion", "search-instead-for", "incomplete-words-boosting", "recommendations-variation:1", "variant-selection-pop-up-v2:bottom_sheet_with_tag", "item_rail_v2", "show-images-in-autosuggest", "attribute-personalization:2", "location-info-reverse-geocode-service:sauron", "pdp-recommendation:fetch_related_products_from_espina", "on-demand-merchandising", "should-use-constant-scoring", "use-autosuggest-as-intent", "category-filtering", "use-ner", "autosuggest-incomplete-spellcorrect", "sponsored-banners-v2", "autosuggest-variation:1", "search-models-variations:1", "show-usecases", "keyword-recipe-position-decider:keyword_first", "inline-filters-visibility", "show-similar-products", "user-bucket:A", "category_reorder_on_feed_experiment:2_0_0", "search-within-plp", "buy-more-save-more:b3c61c", "new-er-scoring-logic", "use-sales-data", "affluence-experiment", "autocomplete-variation:0", "use-spellcorrect-model", "sponsored-products-boost:1", "few-left-exp:bf21a9", "use-primary-secondary-merged-flow-in-type-to-search", "category-grid-old-user-rows:2", "zomato_onesupport_chat", "plp-instamart:vertical-plp", "type-to-search", "search-keyterm-similarity", "search-city-level-autosuggest", "global-collection", "fastest_delivery_vs_eta:control", "top-seller-nu-city-wise:control_top_seller", "households-served-experiment:control_households", "is-cart-footer-strip-shown", "bill-buster-experiment:billbuster_with_timer", "express-allowed-user-segment", "express-merchant-delivery-threshold:-1", "exp-feed-carousel-with-variations:3000", "plp-promo-banner:bf21a9", "google_maps", "map_my_india", "quick-cart-enabled", "instant-cart", "show-dynamic-paan-corner-banner", "show-search-filters", "instant-cart", "track-order-unified-rating-experience-rollout", "show-kitchen-tools-rail", "track-order-v-3-enabled", "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag", "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag", "special-groups-for-goa:fd7a20", "smart-basket-version:v1", "intelligent-pb-version:v2", "discover-new-finds:enabled", "dnf-boosting:", "byc-boosting-config:fd30b6", "npr-migration", "dynamic-curated-for-you-rollout", "pdp-similar-items-v2", "npr-version:v8", "rollout-tod-best-sellers", "app-v-16-rollout-enabled", "post-checkout-scratch-card-rollout", "pdp-similar-products-ab", "attribute-rails:3", "cms-filters-rollout", "pin-new-products", "pb-propensity", "enable_import_z_address_on_cart", "new-user-ranking", "consumer-app-international-phone-login-enabled", "use-query-to-product", "dc-rail", "aspirational-card-rail", "byc-pb-ptype-logic", "slp-trending-behaviour:3", "empty-search-rollout", "dc-realtime-sorting-config:bf21a9", "cart-master-ptype-based-deboosting-on-npr", "reduce-precision-set-byc", "mv-instant-enabled", "empty-search-trending-keyterms:enabled", "enable-blinkit-money", "consumer-app-ios-nuke-image-loading-enabled", "dark-mode-rollout-enabled", "high-confident-pb-ranking", "use-butterfly-model", "pb-intelligent-scoring-version:v2", "new-text-match-qtp-scoring", "is_variant_compression_enabled", "is-primary-config-endpoint-enabled", "post-checkout-crystal-bff-migration-rollout-enabled", "track-order-v2-aerobar-version-rollout", "consumer-app-wishlist-feature-enabled", "butterfly-qtp-ab", "use-butterfly-spellcorrect", "location-permission-popup-v2-enabled", "enable-pharma-flow", "location-autosuggest-backend-enabled", "consumer-app-web-view-auth-token-flow-enabled", "use-pds-product-id-products", "consumer-app-feeding-india-impact-page-rollout-enabled", "far_away_flow_profile", "far_away_flow_cart"], "user_id": 62234241, "traits_middleware": {"is_restricted_mode_enabled": false, "restricted_mode_enabled_lifetime": false}, "app_version": "17.37.2", "app_version_code": 80170372, "app_flavor": "normal", "cart_id": "#-NA", "app_details": {"app_type": "blinkit_android", "app_version": "17.37.2", "app_version_code": 280170372, "app_flavor": "normal"}, "device_details": {"os_version": "35", "device_name": "samsung SM-A556E", "height_in_px": 2209, "width_in_px": 1080, "font_scale": 1, "zoom_scale": 1, "default_density": 450, "current_density": 450, "refresh_rate": 120, "cpu_level": "HIGH", "memory_level": "AVERAGE", "storage_level": "EXCELLENT", "network_level": "EXCELLENT", "battery_level": "LOW", "is_accessibility_enabled": false}, "network_details": {"is_internet_connected": true, "network_type": "WIFI", "network_operator": "AIRTEL", "network_operator_type": "AIRTEL"}, "memory_details": {"remaining_memory_in_percentage": 51.532345, "max_memory_in_mbs": 256}, "channel": null, "appsflyer_app_instance_id": "1752246026552-5582245874463572822", "firebase_app_instance_id": "54938432447336fedd2c990499217945", "session_uuid": "d0d7d806-9938-4110-adcd-61d145cf00da", "session_launch_source": "#-NA", "anonymousId": "36311eb6-1bd5-4199-8eab-44c3e58be2e2", "userId": "62234241", "device_uuid": "b353852c1a87d3a7", "product_list_id": "", "subcategory_id": "-1", "page_type": "", "filters_present": "", "seo": {"min_price": 65, "uom_min_price": "500 ml", "top_brands": ["<PERSON><PERSON><PERSON><PERSON>"], "max_percentage_off": 7, "uom_max_percentage_off": "500 ml", "entity_id": "", "entity_type": "", "page_url": "", "page_description": "", "page_title": "", "heading": "", "seo_content": ""}, "page_name": "listing_widgets", "page_id": "-1", "page_title": "Listing", "page_visit_id": "listing_widgets-48a0f681-b631-42ca-872e-af39ec54bde8", "last_page_name": "feed", "last_page_id": "155", "last_page_title": "feed_default", "last_page_revision_id": "", "last_page_visit_id": "feed-c872ff9a-dab8-4621-a44e-b927128c76a0", "last_sub_page_name": "product_page", "last_sub_page_id": "502675", "last_sub_page_title": "PDP Layout V2", "last_sub_page_revision_id": "", "last_sub_page_tracking_id": "pdp_layout_v2", "last_sub_page_visit_id": "product_page-e2589611-9b0d-4224-822f-8a85a9d172e5", "entry_source_group_tracking_id": "pdp_product_brands", "entry_source_id": "14443", "entry_source_name": "pdp_product_brands", "entry_source_position": 5, "entry_source_title": "pdp_product_brands", "entry_source_tracking_id": "pdp_product_brands", "traits": {"install_source": "#-NA", "install_campaign": "#-NA", "install_medium": "#-NA", "install_referrer": "#-NA", "chain_id": 1383, "lifetime_orders": 209, "latitude": 18.563846, "merchant_id": 35902, "monthly_orders": 11, "city_name": "Pune", "user_type": "ACTIVE", "segment_type": ["school_cohort", "school_cohort_low", "stationary_buyers", "plant_users", "femcare_bathing_user", "toilet_roll_user", "detergent_potenial_users", "harpic_user", "platform_devotee", "gillette_user", "fnv_non_watermelon_user", "health_fitness", "fnv_non_muskmelon_user", "fnv_non_mango_user", "ads_female", "plum_potential_user", "chocolate_munchies_biscuit_premium", "ads_toddler_and_infant", "ads_diy_grocery_buy", "ads_diy_personal_care_buy", "ads_plant_lovers", "surface_cleaner_user", "ads_diy_health_buy", "ads_diy_sexual_wellness_buy", "razor_premium", "amex_defaulter", "fem_care_user", "credit_card_user_flag2", "diswashing_gel_sampling", "beauty_customers", "grocery_user", "party_esttential_potential", "top_twenty_purchaser", "sexual_wellness_user", "sports_fitness", "personal_care_user", "premium_customer", "airtel_flyers_users", "brad_pav_users", "hpstationary_auto", "perforalemon_auto", "deconstructsuns_auto", "personalisation_toddler_infant_cohort_high", "babycareuser_cdp", "female_users", "personalisation_vrat_cohort_low", "ORD_FriendshipBands", "bpc_navratna", "oralcareperfora_cdp", "devotees", "bpcuser_minus_deconstruct", "personalisation_kids_cohort_high", "ads_fnv", "skincare_gulabrigel", "ritebite_users_L6", "dishwashingusers_giffy", "ASBL_Flyer", "segment_z_blinkit_new_store_offer", "segment_z_blinkit_coke_offer_v3", "segment_z_blinkit_coke_offer_v4"], "user_experiment_buckets": ["RANDOM#bucket1", "INCREASING_ROLLOUT#bucket2", "DECREASING_ROLLOUT#bucket2"], "city_id": 787, "longitude": 73.960235, "segment_enabled_features": ["slp-weighted-ranking-equation:0", "show-sort-slp", "ads-service-flow-rollout:baelish", "back-in-stock-grid:enabled", "cbf-similarity-enabled", "cbf-dismiss-button-enabled", "see-all-on-slp", "see-all-recipe-on-plp", "ads-collection-inventory-check-migration", "semantic-search", "slp-3-cards", "autosuggest-keyword-ranking:0", "paas-cart-merge-enabled", "plp-tobacco-consent:disable", "top-brands:3", "pin-lq-products", "pecos", "generated-tags-autosuggest", "continue-browsing-for:enable", "autocomplete-prefix-match-variation:default", "mandatory-autosuggest", "feed-recipe-v2-rollout:enable", "oos-widget-on-slp", "search-show-duplicate-autosuggest", "enable-usecase-container", "search-autosuggest-tap-vs-type:1", "enable-empty-search-v2", "low-converting-keywords-boosting", "city-sales-ranking-v2", "ad-density-variation:0", "product-suggestion", "generated-tag-variation:1", "search-similarity-model", "keyterm-similarity-variation:1", "show-user-query-keyword-autosuggestion", "search-instead-for", "incomplete-words-boosting", "recommendations-variation:1", "variant-selection-pop-up-v2:bottom_sheet_with_tag", "item_rail_v2", "show-images-in-autosuggest", "attribute-personalization:2", "location-info-reverse-geocode-service:sauron", "pdp-recommendation:fetch_related_products_from_espina", "on-demand-merchandising", "should-use-constant-scoring", "use-autosuggest-as-intent", "category-filtering", "use-ner", "autosuggest-incomplete-spellcorrect", "sponsored-banners-v2", "autosuggest-variation:1", "search-models-variations:1", "show-usecases", "keyword-recipe-position-decider:keyword_first", "inline-filters-visibility", "show-similar-products", "user-bucket:A", "category_reorder_on_feed_experiment:2_0_0", "search-within-plp", "buy-more-save-more:b3c61c", "new-er-scoring-logic", "use-sales-data", "affluence-experiment", "autocomplete-variation:0", "use-spellcorrect-model", "sponsored-products-boost:1", "few-left-exp:bf21a9", "use-primary-secondary-merged-flow-in-type-to-search", "category-grid-old-user-rows:2", "zomato_onesupport_chat", "plp-instamart:vertical-plp", "type-to-search", "search-keyterm-similarity", "search-city-level-autosuggest", "global-collection", "fastest_delivery_vs_eta:control", "top-seller-nu-city-wise:control_top_seller", "households-served-experiment:control_households", "is-cart-footer-strip-shown", "bill-buster-experiment:billbuster_with_timer", "express-allowed-user-segment", "express-merchant-delivery-threshold:-1", "exp-feed-carousel-with-variations:3000", "plp-promo-banner:bf21a9", "google_maps", "map_my_india", "quick-cart-enabled", "instant-cart", "show-dynamic-paan-corner-banner", "show-search-filters", "instant-cart", "track-order-unified-rating-experience-rollout", "show-kitchen-tools-rail", "track-order-v-3-enabled", "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag", "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag", "special-groups-for-goa:fd7a20", "smart-basket-version:v1", "intelligent-pb-version:v2", "discover-new-finds:enabled", "dnf-boosting:", "byc-boosting-config:fd30b6", "npr-migration", "dynamic-curated-for-you-rollout", "pdp-similar-items-v2", "npr-version:v8", "rollout-tod-best-sellers", "app-v-16-rollout-enabled", "post-checkout-scratch-card-rollout", "pdp-similar-products-ab", "attribute-rails:3", "cms-filters-rollout", "pin-new-products", "pb-propensity", "enable_import_z_address_on_cart", "new-user-ranking", "consumer-app-international-phone-login-enabled", "use-query-to-product", "dc-rail", "aspirational-card-rail", "byc-pb-ptype-logic", "slp-trending-behaviour:3", "empty-search-rollout", "dc-realtime-sorting-config:bf21a9", "cart-master-ptype-based-deboosting-on-npr", "reduce-precision-set-byc", "mv-instant-enabled", "empty-search-trending-keyterms:enabled", "enable-blinkit-money", "consumer-app-ios-nuke-image-loading-enabled", "dark-mode-rollout-enabled", "high-confident-pb-ranking", "use-butterfly-model", "pb-intelligent-scoring-version:v2", "new-text-match-qtp-scoring", "is_variant_compression_enabled", "is-primary-config-endpoint-enabled", "post-checkout-crystal-bff-migration-rollout-enabled", "track-order-v2-aerobar-version-rollout", "consumer-app-wishlist-feature-enabled", "butterfly-qtp-ab", "use-butterfly-spellcorrect", "location-permission-popup-v2-enabled", "enable-pharma-flow", "location-autosuggest-backend-enabled", "consumer-app-web-view-auth-token-flow-enabled", "use-pds-product-id-products", "consumer-app-feeding-india-impact-page-rollout-enabled", "far_away_flow_profile", "far_away_flow_cart"], "user_id": 62234241, "traits_middleware": {"is_restricted_mode_enabled": false, "restricted_mode_enabled_lifetime": false}, "app_version": "17.37.2", "app_version_code": 80170372, "app_flavor": "normal", "cart_id": "#-NA", "app_details": {"app_type": "blinkit_android", "app_version": "17.37.2", "app_version_code": 280170372, "app_flavor": "normal"}, "device_details": {"os_version": "35", "device_name": "samsung SM-A556E", "height_in_px": 2209, "width_in_px": 1080, "font_scale": 1, "zoom_scale": 1, "default_density": 450, "current_density": 450, "refresh_rate": 120, "cpu_level": "HIGH", "memory_level": "AVERAGE", "storage_level": "EXCELLENT", "network_level": "EXCELLENT", "battery_level": "LOW", "is_accessibility_enabled": false}, "network_details": {"is_internet_connected": true, "network_type": "WIFI", "network_operator": "AIRTEL", "network_operator_type": "AIRTEL"}, "memory_details": {"remaining_memory_in_percentage": 51.532345, "max_memory_in_mbs": 256}, "channel": null, "appsflyer_app_instance_id": "1752246026552-5582245874463572822", "firebase_app_instance_id": "54938432447336fedd2c990499217945", "session_uuid": "d0d7d806-9938-4110-adcd-61d145cf00da", "session_launch_source": "#-NA", "anonymousId": "36311eb6-1bd5-4199-8eab-44c3e58be2e2", "userId": "62234241", "device_uuid": "b353852c1a87d3a7"}}, "url": "", "sequence_id": "16594"}]