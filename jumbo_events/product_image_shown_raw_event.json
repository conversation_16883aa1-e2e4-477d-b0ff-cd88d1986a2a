[{"timestamp": 1753623096, "received_at": 1753623096, "user_agent": "&source=android_market&version=15&device_manufacturer=OPPO&device_brand=OPPO&device_model=CPH2591&app_version=17.11.1&app_type=blinkit_android&sdk_version=35&network_type=WIFI&user_lang=en", "verification": 1, "source": "android", "device_id": "1f158b3c-33c3-4504-9520-e8133a93d8af", "user_id": "133557264", "session_id": "61281993-05cb-4c0d-8bcc-ce568d4f9d781753623025", "location": "956", "number_of_events": 0, "ip": "*************", "batch_count": 0, "batch_offset": 0, "location_info": {"user_defined_latitude": 17.460088, "user_defined_longitude": 78.42121, "current_longitude": null, "current_latitude": null}, "app_info": {"device_performance": "DEVICE_PERFORMANCE_UNSPECIFIED", "theme": "APP_THEME_UNSPECIFIED", "system_theme": "SYSTEM_THEME_DARK", "app_appearance": "APPEARANCE_LIGHT"}, "namespace": null}, {"key": "product_image_shown_events", "value": {"event_name": "Product Shown", "timezone": "Asia/Kolkata", "install_source": "#-NA", "install_campaign": "#-NA", "install_medium": "#-NA", "install_referrer": "#-NA", "chain_id": 1383, "latitude": 17.460088, "merchant_id": 32502, "city_name": "Hyderabad", "user_type": "NEW", "user_id": 133557264, "segment_type": ["int_segment_feb", "android_premium_mobile_user", "baby_potential_user"], "user_experiment_buckets": ["RANDOM#bucket1", "INCREASING_ROLLOUT#bucket2", "DECREASING_ROLLOUT#bucket2"], "city_id": 956, "longitude": 78.42121, "segment_enabled_features": ["slp-weighted-ranking-equation:0", "show-sort-slp", "ads-service-flow-rollout:baelish", "back-in-stock-grid:enabled", "cbf-similarity-enabled", "cbf-dismiss-button-enabled", "see-all-on-slp", "see-all-recipe-on-plp", "ads-collection-inventory-check-migration", "semantic-search", "slp-3-cards", "autosuggest-keyword-ranking:3", "paas-cart-merge-enabled", "plp-tobacco-consent:disable", "top-brands:3", "pin-lq-products", "pecos", "generated-tags-autosuggest", "continue-browsing-for:enable", "autocomplete-prefix-match-variation:default", "mandatory-autosuggest", "feed-recipe-v2-rollout:enable", "oos-widget-on-slp", "search-show-duplicate-autosuggest", "enable-usecase-container", "search-autosuggest-tap-vs-type:1", "enable-empty-search-v2", "low-converting-keywords-boosting", "city-sales-ranking-v2", "ad-density-variation:0", "product-suggestion", "generated-tag-variation:1", "search-similarity-model", "keyterm-similarity-variation:1", "show-user-query-keyword-autosuggestion", "search-instead-for", "incomplete-words-boosting", "recommendations-variation:1", "variant-selection-pop-up-v2:bottom_sheet_with_tag", "item_rail_v2", "show-images-in-autosuggest", "attribute-personalization:2", "location-info-reverse-geocode-service:sauron", "pdp-recommendation:fetch_related_products_from_espina", "on-demand-merchandising", "should-use-constant-scoring", "use-autosuggest-as-intent", "category-filtering", "use-ner", "autosuggest-incomplete-spellcorrect", "sponsored-banners-v2", "autosuggest-variation:1", "search-models-variations:1", "show-usecases", "keyword-recipe-position-decider:keyword_first", "inline-filters-visibility", "show-similar-products", "user-bucket:C", "category_reorder_on_feed_experiment:2_0_0", "search-within-plp", "buy-more-save-more:3f24a7", "new-er-scoring-logic", "use-sales-data", "affluence-experiment", "autocomplete-variation:0", "use-spellcorrect-model", "sponsored-products-boost:1", "few-left-exp:bf21a9", "use-primary-secondary-merged-flow-in-type-to-search", "category-grid-old-user-rows:2", "zomato_onesupport_chat", "plp-instamart:vertical-plp", "type-to-search", "search-keyterm-similarity", "search-city-level-autosuggest", "global-collection", "fastest_delivery_vs_eta:control", "top-seller-nu-city-wise:control_top_seller", "households-served-experiment:control_households", "is-cart-footer-strip-shown", "bill-buster-experiment:billbuster_without_timer", "express-allowed-user-segment", "express-merchant-delivery-threshold:-1", "exp-feed-carousel-with-variations:3000", "plp-promo-banner:bf21a9", "google_maps", "map_my_india", "quick-cart-enabled", "instant-cart", "show-dynamic-paan-corner-banner", "show-search-filters", "instant-cart", "track-order-unified-rating-experience-rollout", "show-kitchen-tools-rail", "track-order-v-3-enabled", "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag", "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag", "special-groups-for-goa:fd7a20", "smart-basket-version:v1", "intelligent-pb-version:v2", "discover-new-finds:enabled", "dnf-boosting:", "byc-boosting-config:fd30b6", "npr-migration", "dynamic-curated-for-you-rollout", "pdp-similar-items-v2", "npr-version:v8", "rollout-tod-best-sellers", "app-v-16-rollout-enabled", "post-checkout-scratch-card-rollout", "pdp-similar-products-ab", "attribute-rails:3", "cms-filters-rollout", "pin-new-products", "pb-propensity", "enable_import_z_address_on_cart", "new-user-ranking", "consumer-app-international-phone-login-enabled", "use-query-to-product", "dc-rail", "aspirational-card-rail", "byc-pb-ptype-logic", "slp-trending-behaviour:3", "empty-search-rollout", "dc-realtime-sorting-config:bf21a9", "cart-master-ptype-based-deboosting-on-npr", "reduce-precision-set-byc", "mv-instant-enabled", "empty-search-trending-keyterms:enabled", "enable-zomato-money", "dark-mode-rollout-enabled", "high-confident-pb-ranking", "use-butterfly-model", "pb-intelligent-scoring-version:v2", "new-text-match-qtp-scoring", "is_variant_compression_enabled", "track-order-v2-aerobar-version-rollout", "use-butterfly-spellcorrect", "location-permission-popup-v2-enabled", "location-autosuggest-backend-enabled", "use-pds-product-id-products", "far_away_flow_profile", "far_away_flow_cart"], "traits_middleware": {"is_restricted_mode_enabled": false, "restricted_mode_enabled_lifetime": false}, "app_version": "17.11.1", "app_version_code": 80170111, "app_flavor": "normal", "cart_id": "1683123846", "app_details": {"app_type": "blinkit_android", "app_version": "17.11.1", "app_version_code": 280170111, "app_flavor": "normal"}, "device_details": {"os_version": "35", "device_name": "OPPO CPH2591", "height_in_px": 1460, "width_in_px": 720, "font_scale": 1, "zoom_scale": 1, "default_density": 320, "current_density": 320, "refresh_rate": 60, "cpu_level": "AVERAGE", "memory_level": "AVERAGE", "storage_level": "HIGH", "network_level": "AVERAGE", "battery_level": "EXCELLENT", "is_accessibility_enabled": false}, "network_details": {"is_internet_connected": true, "network_type": "WIFI", "network_operator": "JIO 4G", "network_operator_type": "JIO"}, "memory_details": {"remaining_memory_in_percentage": 85.31762, "max_memory_in_mbs": 384}, "channel": null, "appsflyer_app_instance_id": "1745770647319-7426383293363104427", "firebase_app_instance_id": "07f36ce74ad0d461f273a359099cd17f", "session_uuid": "662d7158-49d4-4d7e-a66d-319b5b4de88c", "session_launch_source": "#-NA", "anonymousId": "8f1cb696-f0a6-4c5d-bab8-16f4c2de924d", "userId": "133557264", "device_uuid": "143d883fdd342209", "product_list_id": "OTg3NjU0MzIxMjM0NTMzNzE=", "subcategory_id": "1489", "page_type": "Category", "filters_present": "", "seo": {"min_price": 7, "uom_min_price": "50 g", "top_brands": null, "max_percentage_off": 27, "uom_max_percentage_off": "500 g", "entity_id": "", "entity_type": "", "page_url": "", "page_description": "", "page_title": "", "heading": "", "seo_content": ""}, "page_name": "listing_widgets", "page_id": "OTg3NjU0MzIxMjM0NTMzNzE=", "page_title": "Listing", "page_visit_id": "listing-3979a75d-8ecd-4b44-962d-df0692599400", "widget_id": "369013", "widget_name": "Product", "widget_title": "Ridge Gourd (Beerakaya)", "badge": "savings", "brand": "", "currency": "INR", "highlight_ids": "80", "icon_type": "wishlist", "inventory": 7, "inventory_limit": 7, "is_top_right_icon_selected": false, "l0_category": "", "l1_category": "", "l2_category": "Fresh Vegetables", "merchant_type": "express", "mrp": 55, "name": "Ridge Gourd (Beerakaya)", "price": 44, "product_cta_type": "recipe_see_more_8", "product_highlights": "P,โ,๳", "product_id": 369013, "product_offers": "percentage_off", "product_position": "18", "ptype": "Ridge Gourd", "quantity": 1, "state": "available", "title": "Ridge Gourd (Beerakaya)", "type_id": 10869, "widget_position": 3, "widget_variation_id": "global_product_listing", "wishlist_added": false, "widget_impression_count": 1, "time_to_delivery_in_mins": 11, "traits": {"install_source": "#-NA", "install_campaign": "#-NA", "install_medium": "#-NA", "install_referrer": "#-NA", "chain_id": 1383, "latitude": 17.460088, "merchant_id": 32502, "city_name": "Hyderabad", "user_type": "NEW", "user_id": 133557264, "segment_type": ["int_segment_feb", "android_premium_mobile_user", "baby_potential_user"], "user_experiment_buckets": ["RANDOM#bucket1", "INCREASING_ROLLOUT#bucket2", "DECREASING_ROLLOUT#bucket2"], "city_id": 956, "longitude": 78.42121, "segment_enabled_features": ["slp-weighted-ranking-equation:0", "show-sort-slp", "ads-service-flow-rollout:baelish", "back-in-stock-grid:enabled", "cbf-similarity-enabled", "cbf-dismiss-button-enabled", "see-all-on-slp", "see-all-recipe-on-plp", "ads-collection-inventory-check-migration", "semantic-search", "slp-3-cards", "autosuggest-keyword-ranking:3", "paas-cart-merge-enabled", "plp-tobacco-consent:disable", "top-brands:3", "pin-lq-products", "pecos", "generated-tags-autosuggest", "continue-browsing-for:enable", "autocomplete-prefix-match-variation:default", "mandatory-autosuggest", "feed-recipe-v2-rollout:enable", "oos-widget-on-slp", "search-show-duplicate-autosuggest", "enable-usecase-container", "search-autosuggest-tap-vs-type:1", "enable-empty-search-v2", "low-converting-keywords-boosting", "city-sales-ranking-v2", "ad-density-variation:0", "product-suggestion", "generated-tag-variation:1", "search-similarity-model", "keyterm-similarity-variation:1", "show-user-query-keyword-autosuggestion", "search-instead-for", "incomplete-words-boosting", "recommendations-variation:1", "variant-selection-pop-up-v2:bottom_sheet_with_tag", "item_rail_v2", "show-images-in-autosuggest", "attribute-personalization:2", "location-info-reverse-geocode-service:sauron", "pdp-recommendation:fetch_related_products_from_espina", "on-demand-merchandising", "should-use-constant-scoring", "use-autosuggest-as-intent", "category-filtering", "use-ner", "autosuggest-incomplete-spellcorrect", "sponsored-banners-v2", "autosuggest-variation:1", "search-models-variations:1", "show-usecases", "keyword-recipe-position-decider:keyword_first", "inline-filters-visibility", "show-similar-products", "user-bucket:C", "category_reorder_on_feed_experiment:2_0_0", "search-within-plp", "buy-more-save-more:3f24a7", "new-er-scoring-logic", "use-sales-data", "affluence-experiment", "autocomplete-variation:0", "use-spellcorrect-model", "sponsored-products-boost:1", "few-left-exp:bf21a9", "use-primary-secondary-merged-flow-in-type-to-search", "category-grid-old-user-rows:2", "zomato_onesupport_chat", "plp-instamart:vertical-plp", "type-to-search", "search-keyterm-similarity", "search-city-level-autosuggest", "global-collection", "fastest_delivery_vs_eta:control", "top-seller-nu-city-wise:control_top_seller", "households-served-experiment:control_households", "is-cart-footer-strip-shown", "bill-buster-experiment:billbuster_without_timer", "express-allowed-user-segment", "express-merchant-delivery-threshold:-1", "exp-feed-carousel-with-variations:3000", "plp-promo-banner:bf21a9", "google_maps", "map_my_india", "quick-cart-enabled", "instant-cart", "show-dynamic-paan-corner-banner", "show-search-filters", "instant-cart", "track-order-unified-rating-experience-rollout", "show-kitchen-tools-rail", "track-order-v-3-enabled", "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag", "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag", "special-groups-for-goa:fd7a20", "smart-basket-version:v1", "intelligent-pb-version:v2", "discover-new-finds:enabled", "dnf-boosting:", "byc-boosting-config:fd30b6", "npr-migration", "dynamic-curated-for-you-rollout", "pdp-similar-items-v2", "npr-version:v8", "rollout-tod-best-sellers", "app-v-16-rollout-enabled", "post-checkout-scratch-card-rollout", "pdp-similar-products-ab", "attribute-rails:3", "cms-filters-rollout", "pin-new-products", "pb-propensity", "enable_import_z_address_on_cart", "new-user-ranking", "consumer-app-international-phone-login-enabled", "use-query-to-product", "dc-rail", "aspirational-card-rail", "byc-pb-ptype-logic", "slp-trending-behaviour:3", "empty-search-rollout", "dc-realtime-sorting-config:bf21a9", "cart-master-ptype-based-deboosting-on-npr", "reduce-precision-set-byc", "mv-instant-enabled", "empty-search-trending-keyterms:enabled", "enable-zomato-money", "dark-mode-rollout-enabled", "high-confident-pb-ranking", "use-butterfly-model", "pb-intelligent-scoring-version:v2", "new-text-match-qtp-scoring", "is_variant_compression_enabled", "track-order-v2-aerobar-version-rollout", "use-butterfly-spellcorrect", "location-permission-popup-v2-enabled", "location-autosuggest-backend-enabled", "use-pds-product-id-products", "far_away_flow_profile", "far_away_flow_cart"], "traits_middleware": {"is_restricted_mode_enabled": false, "restricted_mode_enabled_lifetime": false}, "app_version": "17.11.1", "app_version_code": 80170111, "app_flavor": "normal", "cart_id": "1683123846", "app_details": {"app_type": "blinkit_android", "app_version": "17.11.1", "app_version_code": 280170111, "app_flavor": "normal"}, "device_details": {"os_version": "35", "device_name": "OPPO CPH2591", "height_in_px": 1460, "width_in_px": 720, "font_scale": 1, "zoom_scale": 1, "default_density": 320, "current_density": 320, "refresh_rate": 60, "cpu_level": "AVERAGE", "memory_level": "AVERAGE", "storage_level": "HIGH", "network_level": "AVERAGE", "battery_level": "EXCELLENT", "is_accessibility_enabled": false}, "network_details": {"is_internet_connected": true, "network_type": "WIFI", "network_operator": "JIO 4G", "network_operator_type": "JIO"}, "memory_details": {"remaining_memory_in_percentage": 85.31762, "max_memory_in_mbs": 384}, "channel": null, "appsflyer_app_instance_id": "1745770647319-7426383293363104427", "firebase_app_instance_id": "07f36ce74ad0d461f273a359099cd17f", "session_uuid": "662d7158-49d4-4d7e-a66d-319b5b4de88c", "session_launch_source": "#-NA", "anonymousId": "8f1cb696-f0a6-4c5d-bab8-16f4c2de924d", "userId": "133557264", "device_uuid": "143d883fdd342209"}}, "url": "", "sequence_id": "5167"}]