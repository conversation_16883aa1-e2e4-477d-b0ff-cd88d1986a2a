version: '3'
services:
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: prod-jumbo-kafka
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: prod-jumbo-kafka-1.internal.zomans.com:9092,prod-jumbo-kafka-2.internal.zomans.com:9092,prod-jumbo-kafka-3.internal.zomans.com:9092,prod-jumbo-kafka-4.internal.zomans.com:9092,prod-jumbo-kafka-5.internal.zomans.com:9092
      
      KAFKA_CLUSTERS_1_NAME: stag-jumbo-kafka
      KAFKA_CLUSTERS_1_BOOTSTRAPSERVERS: stag-jumbo-kafka-1.internal.zomans.com:9092,stag-jumbo-kafka-2.internal.zomans.com:9092,stag-jumbo-kafka-3.internal.zomans.com:9092
      
      KAFKA_CLUSTERS_2_NAME: prod-z-b-kafka
      KAFKA_CLUSTERS_2_BOOTSTRAPSERVERS: prod-blinkit-kafka-1.internal.zomans.com:9092,prod-blinkit-kafka-2.internal.zomans.com:9092,prod-blinkit-kafka-3.internal.zomans.com:9092
      
      # KAFKA_CLUSTERS_3_NAME: preprod-analytics-bridge
      # KAFKA_CLUSTERS_3_BOOTSTRAPSERVERS: ****************************************.groupzomato-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
      
    restart: unless-stopped