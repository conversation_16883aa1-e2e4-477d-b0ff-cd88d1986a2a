#!/usr/bin/env python3
from kafka import KafkaConsumer
import json
import os
from typing import Dict, Any

# Configuration
BOOTSTRAP_SERVERS = 'prod-jumbo-kafka-1.internal.zomans.com:9092,prod-jumbo-kafka-2.internal.zomans.com:9092,prod-jumbo-kafka-3.internal.zomans.com:9092,prod-jumbo-kafka-4.internal.zomans.com:9092,prod-jumbo-kafka-5.internal.zomans.com:9092'
TOPIC = 'blinkit_search_topic_v2'
OUTPUT_DIR = 'event_examples'

def ensure_output_dir():
    """Create output directory if it doesn't exist"""
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)

def save_event(event_key: str, event_data: Dict[str, Any]):
    """Save event data to a JSON file"""
    filename = os.path.join(OUTPUT_DIR, f"{event_key}.json")
    with open(filename, 'w') as f:
        json.dump(event_data, f, indent=2)
    print(f"Saved example for event: {event_key}")

def main():
    # Track which event types we've seen
    seen_events = set()
    
    # Create Kafka consumer without a group (temporary consumer)
    consumer = KafkaConsumer(
        TOPIC,
        bootstrap_servers=BOOTSTRAP_SERVERS.split(','),
        auto_offset_reset='latest',  # Start from latest messages
        enable_auto_commit=False,    # No need to commit offsets
        value_deserializer=lambda x: json.loads(x.decode('utf-8')),
        key_deserializer=lambda x: x.decode('utf-8') if x else None,
        group_id=None                # No consumer group
    )
    
    print(f"Listening for events on topic: {TOPIC}")
    
    try:
        for message in consumer:
            try:
                # Get the event key (e.g., "impression_events", "product_image_shown_events")
                event_key = message.key
                
                # Skip if we've already seen this event type or if key is None
                if not event_key or event_key in seen_events:
                    continue
                
                # Get the full message value
                event_data = message.value
                
                # Save the event example
                save_event(event_key, event_data)
                
                # Mark this event type as seen
                seen_events.add(event_key)
                
                # Print progress
                print(f"Found {len(seen_events)} unique event types so far...")
                
                # If we've seen all event types, we can exit
                # Note: Remove this if you want to keep the consumer running
                # to capture new event types that might appear later
                # if len(seen_events) >= 10:  # Adjust based on expected number of event types
                #     break
                    
            except json.JSONDecodeError:
                print("Error decoding JSON message")
            except Exception as e:
                print(f"Error processing message: {str(e)}")
                
    except KeyboardInterrupt:
        print("\nShutting down consumer...")
    finally:
        consumer.close()
        print(f"Collected examples for {len(seen_events)} event types in '{OUTPUT_DIR}'")

if __name__ == "__main__":
    ensure_output_dir()
    main()