current-cluster: prod-jumbo-kafka
clusteroverride: ""
clusters:
- name: prod-jumbo-kafka
  version: ""
  brokers:
  - prod-jumbo-kafka-1.internal.zomans.com:9092
  - prod-jumbo-kafka-2.internal.zomans.com:9092
  - prod-jumbo-kafka-3.internal.zomans.com:9092
  - prod-jumbo-kafka-4.internal.zomans.com:9092
  - prod-jumbo-kafka-5.internal.zomans.com:9092
  SASL: null
  TLS: null
  security-protocol: ""
  schema-registry-url: ""
  schema-registry-credentials: null
- name: stag-jumbo-kafka
  version: ""
  brokers:
  - stag-jumbo-kafka-1.internal.zomans.com:9092
  - stag-jumbo-kafka-2.internal.zomans.com:9092
  - stag-jumbo-kafka-3.internal.zomans.com:9092
  SASL: null
  TLS: null
  security-protocol: ""
  schema-registry-url: ""
  schema-registry-credentials: null
- name: prod-z-b-kafka
  version: ""
  brokers:
  - prod-blinkit-kafka-1.internal.zomans.com:9092
  - prod-blinkit-kafka-2.internal.zomans.com:9092
  - prod-blinkit-kafka-3.internal.zomans.com:9092
  SASL: null
  TLS: null
  security-protocol: ""
  schema-registry-url: ""
  schema-registry-credentials: null
- name: preprod-analytics-bridge-warpstream
  version: ""
  brokers:
  - ****************************************.groupzomato-agent.kafka.discoveryv2.prod-y.ap-southeast-1.warpstream.com:9092
  SASL: null
  TLS: null
  security-protocol: ""
  schema-registry-url: ""
  schema-registry-credentials: null