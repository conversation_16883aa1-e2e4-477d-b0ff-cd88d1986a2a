[{"timestamp": 1753626596, "received_at": 1753626598, "user_agent": "&source=ios&sdk_version=1.0&device_manufacturer=Apple&device_model=iPhone+XR&app_version=17.19.0&version=18.5&network_type=WIFI&lang=en", "verification": 1, "source": "ios", "device_id": "7F38CCA3-CD92-4B68-ADE3-7739FB21A81A", "user_id": "123470972", "session_id": "08C2EED5E93D4CAAADC8942A706610A7", "location": "1", "number_of_events": 0, "ip": "*************", "batch_count": 0, "batch_offset": 0, "location_info": {"user_defined_latitude": null, "user_defined_longitude": null, "current_longitude": null, "current_latitude": null}, "app_info": {"device_performance": null, "theme": null, "system_theme": "APPEARANCE_DARK", "app_appearance": "APPEARANCE_LIGHT"}, "namespace": null}, {"value": {"widget_impression_count": 1, "product_id": 683805, "latitude": 22.345617, "type_id": 1781, "user_id": "123470972", "segment_type": ["ntc_electronics_and_electricals", "fnv_non_muskmelon_user", "fnv_non_mango_user", "low_carts_user", "ads_diy_grocery_buy", "chocolate_munchies_biscuit_premium", "grocery_user", "party_esttential_potential", "ASBL_Flyer", "segment_z_blinkit_coke_offer_v4", "segment_z_gsb_blinkit_new_signup"], "widget_id": "683805", "state": "available", "last_page_visit_id": "80EAAB2A-21B1-4DE3-8D47-C1D7303CD601", "time_to_delivery_in_mins": 8, "app_version": "17.19.0", "user_type": "ACTIVE", "title": "<PERSON><PERSON> Lindor Assorted Chocolate Gift Pack", "app_type": "blinkit_ios", "is_top_right_icon_selected": false, "city_id": 1904, "currency": "INR", "longitude": 73.1722005, "page_visit_id": "GSplitViewController-06672288-05B7-442B-80A0-47E67F1085D9", "wishlist_added": false, "last_page_id": "155", "brand": "<PERSON><PERSON>", "widget_title": "<PERSON><PERSON> Lindor Assorted Chocolate Gift Pack", "highlight_ids": "0", "icon_type": "wishlist", "widget_variation_id": "global_product_listing", "appsflyer_app_instance_id": "1746872710237-0337893", "product_offers": "", "price": 475, "monthly_orders": 3, "city_name": "Vadodara", "mrp": 475, "widget_name": "Product", "seo": {"heading": "", "max_percentage_off": 15, "page_description": "", "min_price": 80, "top_brands": ["Cadbury Dairy Milk Silk", "Cadbury Celebrations", "<PERSON><PERSON>"], "entity_id": "", "page_url": "", "entity_type": "", "page_title": "", "uom_min_price": "89.2 g", "seo_content": "", "uom_max_percentage_off": "13 g"}, "product_position": "32", "product_highlights": "", "traits": {"session_uuid": "B606252F-8DCA-46D4-8384-79986CDE8B26", "city_id": 1904, "device_uuid": "7F38CCA3-CD92-4B68-ADE3-7739FB21A81A", "user_id": "123470972", "app_version": "17.19.0", "lifetime_orders": 7, "channel": "BLINKIT", "app_flavor": "NORMAL", "merchant_id": 41209, "city_name": "Vadodara", "latitude": 22.345617, "chain_id": 1383, "appsflyer_app_instance_id": "1746872710237-0337893", "cart_id": "1665238699", "app_details": {"app_version": "17.19.0", "app_type": "blinkit_ios", "app_flavor": "NORMAL", "app_version_code": "171900"}, "traits_middleware": {"restricted_mode_enabled_lifetime": false, "is_restricted_mode_enabled": false}, "firebase_app_instance_id": "ADFA6ADC7AC741E6B2E0F98EB14400E6", "longitude": 73.1722005, "user_type": "ACTIVE", "segment_enabled_features": ["slp-weighted-ranking-equation:0", "show-sort-slp", "ads-service-flow-rollout:baelish", "back-in-stock-grid:enabled", "cbf-similarity-enabled", "cbf-dismiss-button-enabled", "see-all-on-slp", "see-all-recipe-on-plp", "ads-collection-inventory-check-migration", "semantic-search", "slp-3-cards", "autosuggest-keyword-ranking:0", "paas-cart-merge-enabled", "plp-tobacco-consent:enable", "top-brands:3", "pin-lq-products", "pecos", "generated-tags-autosuggest", "continue-browsing-for:enable", "autocomplete-prefix-match-variation:default", "mandatory-autosuggest", "feed-recipe-v2-rollout:enable", "oos-widget-on-slp", "search-show-duplicate-autosuggest", "enable-usecase-container", "search-autosuggest-tap-vs-type:1", "enable-empty-search-v2", "low-converting-keywords-boosting", "city-sales-ranking-v2", "ad-density-variation:0", "product-suggestion", "generated-tag-variation:1", "search-similarity-model", "keyterm-similarity-variation:1", "show-user-query-keyword-autosuggestion", "search-instead-for", "incomplete-words-boosting", "recommendations-variation:1", "variant-selection-pop-up-v2:bottom_sheet_with_tag", "item_rail_v2", "show-images-in-autosuggest", "attribute-personalization:2", "location-info-reverse-geocode-service:sauron", "pdp-recommendation:fetch_related_products_from_espina", "on-demand-merchandising", "should-use-constant-scoring", "use-autosuggest-as-intent", "category-filtering", "use-ner", "autosuggest-incomplete-spellcorrect", "sponsored-banners-v2", "autosuggest-variation:1", "search-models-variations:1", "show-usecases", "keyword-recipe-position-decider:keyword_first", "inline-filters-visibility", "show-similar-products", "user-bucket:C", "category_reorder_on_feed_experiment:2_0_0", "search-within-plp", "buy-more-save-more:54da0c", "new-er-scoring-logic", "use-sales-data", "affluence-experiment", "autocomplete-variation:0", "use-spellcorrect-model", "sponsored-products-boost:1", "few-left-exp:bf21a9", "use-primary-secondary-merged-flow-in-type-to-search", "category-grid-old-user-rows:5", "plp-instamart:vertical-plp", "type-to-search", "search-keyterm-similarity", "search-city-level-autosuggest", "global-collection", "fastest_delivery_vs_eta:control", "top-seller-nu-city-wise:control_top_seller", "households-served-experiment:control_households", "is-cart-footer-strip-shown", "bill-buster-experiment:billbuster_without_timer", "express-allowed-user-segment", "express-merchant-delivery-threshold:-1", "exp-feed-carousel-with-variations:3000", "plp-promo-banner:bf21a9", "google_maps", "map_my_india", "quick-cart-enabled", "show-dynamic-paan-corner-banner", "show-search-filters", "track-order-unified-rating-experience-rollout", "show-kitchen-tools-rail", "track-order-v-3-enabled", "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag", "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag", "special-groups-for-goa:fd7a20", "smart-basket-version:v1", "intelligent-pb-version:v2", "discover-new-finds:enabled", "dnf-boosting:", "byc-boosting-config:fd30b6", "npr-migration", "dynamic-curated-for-you-rollout", "pdp-similar-items-v2", "npr-version:v8", "rollout-tod-best-sellers", "app-v-16-rollout-enabled", "post-checkout-scratch-card-rollout", "pdp-similar-products-ab", "attribute-rails:3", "cms-filters-rollout", "pin-new-products", "pb-propensity", "post-checkout-ios-type-6-snippet-migration-enabled", "enable_import_z_address_on_cart", "new-user-ranking", "consumer-app-international-phone-login-enabled", "use-query-to-product", "dc-rail", "aspirational-card-rail", "byc-pb-ptype-logic", "consumer-app-pdp-switcher-enabled", "slp-trending-behaviour:3", "empty-search-rollout", "dc-realtime-sorting-config:bf21a9", "cart-master-ptype-based-deboosting-on-npr", "reduce-precision-set-byc", "mv-instant-enabled", "empty-search-trending-keyterms:enabled", "enable-blinkit-money", "dark-mode-rollout-enabled", "high-confident-pb-ranking", "use-butterfly-model", "pb-intelligent-scoring-version:v2", "new-text-match-qtp-scoring", "is-primary-config-endpoint-enabled", "post-checkout-crystal-bff-migration-rollout-enabled", "track-order-v2-aerobar-version-rollout", "consumer-app-wishlist-feature-enabled", "use-butterfly-spellcorrect", "location-permission-popup-v2-enabled", "enable-pharma-flow", "location-autosuggest-backend-enabled", "consumer-app-web-view-auth-token-flow-enabled", "use-pds-product-id-products", "consumer-app-feeding-india-impact-page-rollout-enabled", "far_away_flow_profile", "far_away_flow_cart"], "idfv": "A0337893-3DDB-4B14-8C71-C64D05C44CCA", "app_type": "blinkit_ios", "user_experiment_buckets": ["RANDOM#bucket2", "INCREASING_ROLLOUT#bucket2", "DECREASING_ROLLOUT#bucket1"], "app_version_code": 171900, "segment_type": ["ntc_electronics_and_electricals", "fnv_non_muskmelon_user", "fnv_non_mango_user", "low_carts_user", "ads_diy_grocery_buy", "chocolate_munchies_biscuit_premium", "grocery_user", "party_esttential_potential", "ASBL_Flyer", "segment_z_blinkit_coke_offer_v4", "segment_z_gsb_blinkit_new_signup"], "monthly_orders": 3}, "idfv": "A0337893-3DDB-4B14-8C71-C64D05C44CCA", "merchant_id": 41209, "name": "<PERSON><PERSON> Lindor Assorted Chocolate Gift Pack", "widget_position": 2, "channel": "BLINKIT", "filters_present": "relevance;", "inventory_limit": 1, "l2_category": "Chocolate Gift Pack", "user_experiment_buckets": ["RANDOM#bucket2", "INCREASING_ROLLOUT#bucket2", "DECREASING_ROLLOUT#bucket1"], "event_name": "Image Shown", "page_name": "listing_widgets", "eta_identifier": "express", "device_uuid": "7F38CCA3-CD92-4B68-ADE3-7739FB21A81A", "l1_category": "", "subcategory_id": "2379", "app_details": {"app_version": "17.19.0", "app_type": "blinkit_ios", "app_flavor": "NORMAL", "app_version_code": "171900"}, "child_widget_position": 0, "app_flavor": "NORMAL", "firebase_app_instance_id": "ADFA6ADC7AC741E6B2E0F98EB14400E6", "last_page_title": "feed_default", "page_id": "OTg3NjU0MzIxMjM0NTMzNjc=", "traits_middleware": {"restricted_mode_enabled_lifetime": false, "is_restricted_mode_enabled": false}, "page_title": "Listing", "product_list_id": "OTg3NjU0MzIxMjM0NTMzNjc=", "segment_enabled_features": ["slp-weighted-ranking-equation:0", "show-sort-slp", "ads-service-flow-rollout:baelish", "back-in-stock-grid:enabled", "cbf-similarity-enabled", "cbf-dismiss-button-enabled", "see-all-on-slp", "see-all-recipe-on-plp", "ads-collection-inventory-check-migration", "semantic-search", "slp-3-cards", "autosuggest-keyword-ranking:0", "paas-cart-merge-enabled", "plp-tobacco-consent:enable", "top-brands:3", "pin-lq-products", "pecos", "generated-tags-autosuggest", "continue-browsing-for:enable", "autocomplete-prefix-match-variation:default", "mandatory-autosuggest", "feed-recipe-v2-rollout:enable", "oos-widget-on-slp", "search-show-duplicate-autosuggest", "enable-usecase-container", "search-autosuggest-tap-vs-type:1", "enable-empty-search-v2", "low-converting-keywords-boosting", "city-sales-ranking-v2", "ad-density-variation:0", "product-suggestion", "generated-tag-variation:1", "search-similarity-model", "keyterm-similarity-variation:1", "show-user-query-keyword-autosuggestion", "search-instead-for", "incomplete-words-boosting", "recommendations-variation:1", "variant-selection-pop-up-v2:bottom_sheet_with_tag", "item_rail_v2", "show-images-in-autosuggest", "attribute-personalization:2", "location-info-reverse-geocode-service:sauron", "pdp-recommendation:fetch_related_products_from_espina", "on-demand-merchandising", "should-use-constant-scoring", "use-autosuggest-as-intent", "category-filtering", "use-ner", "autosuggest-incomplete-spellcorrect", "sponsored-banners-v2", "autosuggest-variation:1", "search-models-variations:1", "show-usecases", "keyword-recipe-position-decider:keyword_first", "inline-filters-visibility", "show-similar-products", "user-bucket:C", "category_reorder_on_feed_experiment:2_0_0", "search-within-plp", "buy-more-save-more:54da0c", "new-er-scoring-logic", "use-sales-data", "affluence-experiment", "autocomplete-variation:0", "use-spellcorrect-model", "sponsored-products-boost:1", "few-left-exp:bf21a9", "use-primary-secondary-merged-flow-in-type-to-search", "category-grid-old-user-rows:5", "plp-instamart:vertical-plp", "type-to-search", "search-keyterm-similarity", "search-city-level-autosuggest", "global-collection", "fastest_delivery_vs_eta:control", "top-seller-nu-city-wise:control_top_seller", "households-served-experiment:control_households", "is-cart-footer-strip-shown", "bill-buster-experiment:billbuster_without_timer", "express-allowed-user-segment", "express-merchant-delivery-threshold:-1", "exp-feed-carousel-with-variations:3000", "plp-promo-banner:bf21a9", "google_maps", "map_my_india", "quick-cart-enabled", "show-dynamic-paan-corner-banner", "show-search-filters", "track-order-unified-rating-experience-rollout", "show-kitchen-tools-rail", "track-order-v-3-enabled", "variant-selection-pop-up-with-item-rail-slp:bottom_sheet_with_tag", "variant-selection-pop-up-with-item-rail-plp:bottom_sheet_with_tag", "special-groups-for-goa:fd7a20", "smart-basket-version:v1", "intelligent-pb-version:v2", "discover-new-finds:enabled", "dnf-boosting:", "byc-boosting-config:fd30b6", "npr-migration", "dynamic-curated-for-you-rollout", "pdp-similar-items-v2", "npr-version:v8", "rollout-tod-best-sellers", "app-v-16-rollout-enabled", "post-checkout-scratch-card-rollout", "pdp-similar-products-ab", "attribute-rails:3", "cms-filters-rollout", "pin-new-products", "pb-propensity", "post-checkout-ios-type-6-snippet-migration-enabled", "enable_import_z_address_on_cart", "new-user-ranking", "consumer-app-international-phone-login-enabled", "use-query-to-product", "dc-rail", "aspirational-card-rail", "byc-pb-ptype-logic", "consumer-app-pdp-switcher-enabled", "slp-trending-behaviour:3", "empty-search-rollout", "dc-realtime-sorting-config:bf21a9", "cart-master-ptype-based-deboosting-on-npr", "reduce-precision-set-byc", "mv-instant-enabled", "empty-search-trending-keyterms:enabled", "enable-blinkit-money", "dark-mode-rollout-enabled", "high-confident-pb-ranking", "use-butterfly-model", "pb-intelligent-scoring-version:v2", "new-text-match-qtp-scoring", "is-primary-config-endpoint-enabled", "post-checkout-crystal-bff-migration-rollout-enabled", "track-order-v2-aerobar-version-rollout", "consumer-app-wishlist-feature-enabled", "use-butterfly-spellcorrect", "location-permission-popup-v2-enabled", "enable-pharma-flow", "location-autosuggest-backend-enabled", "consumer-app-web-view-auth-token-flow-enabled", "use-pds-product-id-products", "consumer-app-feeding-india-impact-page-rollout-enabled", "far_away_flow_profile", "far_away_flow_cart"], "l0_category": "", "last_page_name": "feed", "inventory": 1, "merchant_type": "express", "lifetime_orders": 7, "badge": "savings", "app_version_code": 171900, "quantity": 1, "session_uuid": "B606252F-8DCA-46D4-8384-79986CDE8B26", "page_type": "Category", "cart_id": "1665238699", "chain_id": 1383, "page_tracking_id": "#-NA", "ptype": "Chocolate Gift Pack"}, "key": "product_image_shown_events", "url": "Screen Name"}]